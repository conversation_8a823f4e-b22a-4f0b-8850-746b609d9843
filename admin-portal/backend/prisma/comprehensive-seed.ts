import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

enum Role {
  ADMIN = 'ADMIN',
  DRIVER = 'DRIVER',
  CITIZEN = 'CITIZEN',
}

enum BinStatus {
  NORMAL = 'NORMAL',
  FULL = 'FULL',
  OVERFLOW = 'OVERFLOW',
  MAINTENANCE = 'MAINTENANCE',
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...');

  // Clear existing data
  await prisma.alert.deleteMany();
  await prisma.report.deleteMany();
  await prisma.assignment.deleteMany();
  await prisma.pickup.deleteMany();
  await prisma.bin.deleteMany();
  await prisma.user.deleteMany();

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      phone: '+237123456789',
      name: '<PERSON><PERSON>',
      password: adminPassword,
      role: Role.ADMIN,
      address: '<PERSON>stère de l\'Environnement, <PERSON>undé',
    },
  });

  // Create drivers
  const driverPassword = await bcrypt.hash('driver123', 10);
  const drivers = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456790',
        name: 'Jean Baptiste Mbarga',
        password: driverPassword,
        role: Role.DRIVER,
        address: 'Douala, Cameroun',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456791',
        name: 'Marie Ngono',
        password: driverPassword,
        role: Role.DRIVER,
        address: 'Yaoundé, Cameroun',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456792',
        name: 'Paul Essomba',
        password: driverPassword,
        role: Role.DRIVER,
        address: 'Bafoussam, Cameroun',
      },
    }),
  ]);

  // Create citizens
  const citizenPassword = await bcrypt.hash('citizen123', 10);
  const citizens = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456793',
        name: 'Paul Atangana',
        password: citizenPassword,
        role: Role.CITIZEN,
        address: 'Quartier Bastos, Yaoundé',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456794',
        name: 'Grace Biya',
        password: citizenPassword,
        role: Role.CITIZEN,
        address: 'Bonanjo, Douala',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456795',
        name: 'Emmanuel Fouda',
        password: citizenPassword,
        role: Role.CITIZEN,
        address: 'Akwa, Douala',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456796',
        name: 'Aminata Sow',
        password: citizenPassword,
        role: Role.CITIZEN,
        address: 'Mvog-Mbi, Yaoundé',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+237123456797',
        name: 'Joseph Nkomo',
        password: citizenPassword,
        role: Role.CITIZEN,
        address: 'Essos, Yaoundé',
      },
    }),
  ]);

  // Create comprehensive bins across major Cameroon cities
  const bins = await Promise.all([
    // Douala bins
    prisma.bin.create({
      data: {
        name: 'Central Market Bin 1',
        location: 'Marché Central, Douala',
        latitude: 4.0511,
        longitude: 9.7679,
        capacity: 240,
        currentLevel: 95,
        status: BinStatus.OVERFLOW,
        sensorId: 'SENSOR_001',
        batteryLevel: 78,
        temperature: 28.5,
        humidity: 65,
        lastEmptied: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Central Market Bin 2',
        location: 'Marché Central, Douala',
        latitude: 4.0521,
        longitude: 9.7689,
        capacity: 240,
        currentLevel: 88,
        status: BinStatus.FULL,
        sensorId: 'SENSOR_002',
        batteryLevel: 65,
        temperature: 29.1,
        humidity: 68,
        lastEmptied: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Université de Douala Bin 1',
        location: 'Université de Douala',
        latitude: 4.0611,
        longitude: 9.7779,
        capacity: 120,
        currentLevel: 45,
        status: BinStatus.NORMAL,
        sensorId: 'SENSOR_003',
        batteryLevel: 92,
        temperature: 26.8,
        humidity: 58,
        lastEmptied: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Bonanjo Résidentiel Bin 1',
        location: 'Quartier Bonanjo, Douala',
        latitude: 4.0411,
        longitude: 9.7579,
        capacity: 180,
        currentLevel: 102,
        status: BinStatus.OVERFLOW,
        sensorId: 'SENSOR_004',
        batteryLevel: 15,
        temperature: 29.2,
        humidity: 72,
        lastEmptied: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Akwa Business District Bin 1',
        location: 'Quartier Akwa, Douala',
        latitude: 4.0311,
        longitude: 9.7479,
        capacity: 200,
        currentLevel: 78,
        status: BinStatus.NORMAL,
        sensorId: 'SENSOR_005',
        batteryLevel: 82,
        temperature: 28.8,
        humidity: 64,
        lastEmptied: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Marché Deido Bin 1',
        location: 'Marché Deido, Douala',
        latitude: 4.0711,
        longitude: 9.7879,
        capacity: 300,
        currentLevel: 91,
        status: BinStatus.FULL,
        sensorId: 'SENSOR_006',
        batteryLevel: 45,
        temperature: 30.1,
        humidity: 75,
        lastEmptied: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      },
    }),
    // Yaoundé bins
    prisma.bin.create({
      data: {
        name: 'Marché Mfoundi Bin 1',
        location: 'Marché Mfoundi, Yaoundé',
        latitude: 3.8480,
        longitude: 11.5021,
        capacity: 250,
        currentLevel: 56,
        status: BinStatus.NORMAL,
        sensorId: 'SENSOR_007',
        batteryLevel: 91,
        temperature: 25.4,
        humidity: 62,
        lastEmptied: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Quartier Bastos Bin 1',
        location: 'Quartier Bastos, Yaoundé',
        latitude: 3.8680,
        longitude: 11.5221,
        capacity: 150,
        currentLevel: 34,
        status: BinStatus.NORMAL,
        sensorId: 'SENSOR_008',
        batteryLevel: 87,
        temperature: 24.8,
        humidity: 58,
        lastEmptied: new Date(Date.now() - 12 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Université de Yaoundé I Bin 1',
        location: 'Université de Yaoundé I',
        latitude: 3.8580,
        longitude: 11.5121,
        capacity: 180,
        currentLevel: 89,
        status: BinStatus.FULL,
        sensorId: 'SENSOR_009',
        batteryLevel: 72,
        temperature: 26.1,
        humidity: 65,
        lastEmptied: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Mvog-Mbi Bin 1',
        location: 'Quartier Mvog-Mbi, Yaoundé',
        latitude: 3.8380,
        longitude: 11.4921,
        capacity: 200,
        currentLevel: 105,
        status: BinStatus.OVERFLOW,
        sensorId: 'SENSOR_010',
        batteryLevel: 8,
        temperature: 27.3,
        humidity: 71,
        lastEmptied: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Essos Commercial Bin 1',
        location: 'Quartier Essos, Yaoundé',
        latitude: 3.8780,
        longitude: 11.5321,
        capacity: 220,
        currentLevel: 23,
        status: BinStatus.NORMAL,
        sensorId: 'SENSOR_011',
        batteryLevel: 94,
        temperature: 25.7,
        humidity: 59,
        lastEmptied: new Date(Date.now() - 8 * 60 * 60 * 1000),
      },
    }),
    prisma.bin.create({
      data: {
        name: 'Nlongkak Bin 1',
        location: 'Quartier Nlongkak, Yaoundé',
        latitude: 3.8756,
        longitude: 11.5089,
        capacity: 200,
        currentLevel: 97,
        status: BinStatus.OVERFLOW,
        sensorId: 'SENSOR_012',
        batteryLevel: 12,
        temperature: 26.8,
        humidity: 69,
        lastEmptied: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      },
    }),
  ]);

  // Create critical alerts
  const alerts = await Promise.all([
    prisma.alert.create({
      data: {
        binId: bins[0].id, // Central Market Bin 1 (overflow)
        type: 'BIN_OVERFLOW',
        severity: 'CRITICAL',
        title: 'Débordement Critique - Marché Central',
        message: 'La poubelle du marché central déborde dangereusement. Intervention urgente requise.',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[3].id, // Bonanjo overflow
        type: 'BIN_OVERFLOW',
        severity: 'CRITICAL',
        title: 'Débordement - Quartier Bonanjo',
        message: 'Poubelle résidentielle en débordement depuis 5 jours.',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[9].id, // Mvog-Mbi overflow
        type: 'BIN_OVERFLOW',
        severity: 'CRITICAL',
        title: 'Débordement - Mvog-Mbi',
        message: 'Situation critique dans le quartier Mvog-Mbi.',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[3].id, // Low battery
        type: 'SENSOR_LOW_BATTERY',
        severity: 'HIGH',
        title: 'Batterie Faible - Capteur',
        message: 'Le capteur SENSOR_004 a une batterie critique (15%).',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[9].id, // Low battery
        type: 'SENSOR_LOW_BATTERY',
        severity: 'HIGH',
        title: 'Batterie Critique - Mvog-Mbi',
        message: 'Le capteur SENSOR_010 nécessite un remplacement de batterie (8%).',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[5].id, // Temperature alert
        type: 'HIGH_TEMPERATURE',
        severity: 'MEDIUM',
        title: 'Température Élevée - Marché Deido',
        message: 'Température anormalement élevée détectée (30.1°C).',
        isResolved: false,
      },
    }),
    prisma.alert.create({
      data: {
        binId: bins[1].id, // Resolved alert
        type: 'BIN_FULL',
        severity: 'HIGH',
        title: 'Poubelle Pleine - Marché Central',
        message: 'Poubelle pleine nécessitant une collecte.',
        isResolved: true,
        resolvedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        resolvedBy: admin.id,
      },
    }),
  ]);

  // Create pickup requests
  const pickups = await Promise.all([
    prisma.pickup.create({
      data: {
        userId: citizens[0].id,
        location: 'Quartier Bastos, Yaoundé',
        latitude: 3.8680,
        longitude: 11.5221,
        description: 'Collecte de déchets ménagers volumineux',
        wasteType: 'BULKY',
        status: 'PENDING',
        priority: 'HIGH',
        scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      },
    }),
    prisma.pickup.create({
      data: {
        userId: citizens[1].id,
        location: 'Bonanjo, Douala',
        latitude: 4.0411,
        longitude: 9.7579,
        description: 'Déchets organiques de restaurant',
        wasteType: 'ORGANIC',
        status: 'IN_PROGRESS',
        priority: 'NORMAL',
        scheduledAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
    }),
    prisma.pickup.create({
      data: {
        userId: citizens[2].id,
        location: 'Akwa, Douala',
        latitude: 4.0311,
        longitude: 9.7479,
        description: 'Déchets électroniques',
        wasteType: 'ELECTRONIC',
        status: 'COMPLETED',
        priority: 'NORMAL',
        scheduledAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        completedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
      },
    }),
    prisma.pickup.create({
      data: {
        userId: citizens[3].id,
        location: 'Mvog-Mbi, Yaoundé',
        latitude: 3.8380,
        longitude: 11.4921,
        description: 'Déchets de construction',
        wasteType: 'CONSTRUCTION',
        status: 'PENDING',
        priority: 'LOW',
        scheduledAt: new Date(Date.now() + 48 * 60 * 60 * 1000),
      },
    }),
    prisma.pickup.create({
      data: {
        userId: citizens[4].id,
        location: 'Essos, Yaoundé',
        latitude: 3.8780,
        longitude: 11.5321,
        description: 'Collecte de déchets recyclables',
        wasteType: 'RECYCLABLE',
        status: 'COMPLETED',
        priority: 'NORMAL',
        scheduledAt: new Date(Date.now() - 48 * 60 * 60 * 1000),
        completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
    }),
  ]);

  // Create assignments for pickups
  const assignments = await Promise.all([
    prisma.assignment.create({
      data: {
        pickupId: pickups[1].id, // IN_PROGRESS pickup
        userId: drivers[0].id,
        status: 'IN_PROGRESS',
        notes: 'En route vers le lieu de collecte',
      },
    }),
    prisma.assignment.create({
      data: {
        pickupId: pickups[2].id, // COMPLETED pickup
        userId: drivers[1].id,
        status: 'COMPLETED',
        notes: 'Collecte terminée avec succès',
      },
    }),
    prisma.assignment.create({
      data: {
        pickupId: pickups[4].id, // COMPLETED pickup
        userId: drivers[2].id,
        status: 'COMPLETED',
        notes: 'Déchets recyclables collectés et triés',
      },
    }),
  ]);

  // Create reports
  const reports = await Promise.all([
    prisma.report.create({
      data: {
        userId: citizens[0].id,
        binId: bins[0].id,
        type: 'BIN_OVERFLOW',
        title: 'Poubelle débordante au marché',
        description: 'La poubelle du marché central déborde depuis plusieurs jours. Les déchets se répandent sur la chaussée et créent des problèmes d\'hygiène.',
        location: 'Marché Central, Douala',
        latitude: 4.0511,
        longitude: 9.7679,
        status: 'PENDING',
      },
    }),
    prisma.report.create({
      data: {
        userId: citizens[1].id,
        binId: bins[3].id,
        type: 'BIN_DAMAGE',
        title: 'Poubelle endommagée',
        description: 'La poubelle de Bonanjo est cassée et ne ferme plus correctement. Les animaux peuvent accéder aux déchets.',
        location: 'Quartier Bonanjo, Douala',
        latitude: 4.0411,
        longitude: 9.7579,
        status: 'IN_PROGRESS',
      },
    }),
    prisma.report.create({
      data: {
        userId: citizens[2].id,
        type: 'ILLEGAL_DUMPING',
        title: 'Dépôt sauvage de déchets',
        description: 'Des déchets ont été déposés illégalement près de l\'université. Il y a des déchets électroniques et des matériaux de construction.',
        location: 'Près de l\'Université de Douala',
        latitude: 4.0611,
        longitude: 9.7779,
        status: 'RESOLVED',
      },
    }),
    prisma.report.create({
      data: {
        userId: citizens[3].id,
        binId: bins[9].id,
        type: 'MISSED_COLLECTION',
        title: 'Collecte manquée',
        description: 'La collecte prévue n\'a pas eu lieu cette semaine dans notre quartier. Les poubelles débordent.',
        location: 'Quartier Mvog-Mbi, Yaoundé',
        latitude: 3.8380,
        longitude: 11.4921,
        status: 'PENDING',
      },
    }),
    prisma.report.create({
      data: {
        userId: citizens[4].id,
        type: 'OTHER',
        title: 'Problème d\'odeur',
        description: 'Fortes odeurs nauséabondes provenant de la zone de collecte. Cela affecte la qualité de vie des résidents.',
        location: 'Quartier Essos, Yaoundé',
        latitude: 3.8780,
        longitude: 11.5321,
        status: 'RESOLVED',
      },
    }),
    prisma.report.create({
      data: {
        userId: citizens[0].id,
        binId: bins[5].id,
        type: 'BIN_FULL',
        title: 'Poubelle pleine au marché Deido',
        description: 'La poubelle du marché Deido est pleine et nécessite une collecte urgente.',
        location: 'Marché Deido, Douala',
        latitude: 4.0711,
        longitude: 9.7879,
        status: 'RESOLVED',
      },
    }),
  ]);

  console.log(`✅ Created ${bins.length} bins`);
  console.log(`✅ Created ${drivers.length} drivers`);
  console.log(`✅ Created ${citizens.length} citizens`);
  console.log(`✅ Created 1 admin user`);
  console.log(`✅ Created ${alerts.length} alerts`);
  console.log(`✅ Created ${pickups.length} pickups`);
  console.log(`✅ Created ${assignments.length} assignments`);
  console.log(`✅ Created ${reports.length} reports`);
  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
