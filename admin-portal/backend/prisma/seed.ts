import { PrismaClient } from '@prisma/client';
import { Role, WasteType, BinStatus } from '../src/common/enums';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      phone: '+237123456789',
      name: 'System Administrator',
      password: adminPassword,
      role: Role.ADMIN,
    },
  });

  // Create driver users
  const driverPassword = await bcrypt.hash('driver123', 10);
  const driver1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      phone: '+237123456790',
      name: '<PERSON>',
      password: driverPassword,
      role: Role.DRIVER,
    },
  });

  const driver2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      phone: '+237123456791',
      name: 'Marie Ngono',
      password: driverPassword,
      role: Role.DRIVER,
    },
  });

  // Create test citizen users
  const citizenPassword = await bcrypt.hash('citizen123', 10);
  const citizen1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      phone: '+237123456792',
      name: 'Paul Atangana',
      password: citizenPassword,
      role: Role.CITIZEN,
      address: 'Quartier Bastos, Yaoundé',
    },
  });

  // Create waste bins around Yaoundé
  const bins = [
    {
      name: 'Bastos Market Bin',
      location: 'Marché de Bastos, Yaoundé',
      latitude: 3.8480,
      longitude: 11.5021,
      capacity: 240,
      currentLevel: 75,
      status: BinStatus.NORMAL,
      sensorId: 'sensor_001',
    },
    {
      name: 'Mfoundi University Bin',
      location: 'Université de Yaoundé I',
      latitude: 3.8667,
      longitude: 11.5167,
      capacity: 120,
      currentLevel: 90,
      status: BinStatus.FULL,
      sensorId: 'sensor_002',
    },
    {
      name: 'Mvog-Ada Bin',
      location: 'Quartier Mvog-Ada',
      latitude: 3.8378,
      longitude: 11.5230,
      capacity: 180,
      currentLevel: 45,
      status: BinStatus.NORMAL,
      sensorId: 'sensor_003',
    },
    {
      name: 'Nlongkak Bin',
      location: 'Quartier Nlongkak',
      latitude: 3.8756,
      longitude: 11.5089,
      capacity: 200,
      currentLevel: 95,
      status: BinStatus.OVERFLOW,
      sensorId: 'sensor_004',
    },
  ];

  for (const binData of bins) {
    await prisma.bin.upsert({
      where: { sensorId: binData.sensorId },
      update: {},
      create: binData,
    });
  }

  // Create sample pickup requests
  const pickups = [
    {
      userId: citizen1.id,
      location: 'Quartier Bastos, Yaoundé',
      latitude: 3.8480,
      longitude: 11.5021,
      description: 'Large amount of household waste',
      wasteType: WasteType.GENERAL,
    },
    {
      userId: citizen1.id,
      location: 'Quartier Mvog-Ada',
      latitude: 3.8378,
      longitude: 11.5230,
      description: 'Electronic waste - old TV and computer',
      wasteType: WasteType.ELECTRONIC,
    },
  ];

  for (const pickupData of pickups) {
    await prisma.pickup.create({
      data: pickupData,
    });
  }

  // Create sample alerts
  const fullBin = await prisma.bin.findFirst({
    where: { status: BinStatus.FULL },
  });

  const overflowBin = await prisma.bin.findFirst({
    where: { status: BinStatus.OVERFLOW },
  });

  if (fullBin) {
    await prisma.alert.create({
      data: {
        binId: fullBin.id,
        type: 'BIN_FULL',
        severity: 'MEDIUM',
        title: 'Bin Full Alert',
        message: `Bin at ${fullBin.location} is full and needs emptying`,
      },
    });
  }

  if (overflowBin) {
    await prisma.alert.create({
      data: {
        binId: overflowBin.id,
        type: 'BIN_OVERFLOW',
        severity: 'HIGH',
        title: 'Bin Overflow Alert',
        message: `Bin at ${overflowBin.location} is overflowing!`,
      },
    });
  }

  console.log('✅ Database seeding completed!');
  console.log('👤 Admin user created: <EMAIL> / admin123');
  console.log('🚛 Driver users created: <EMAIL> / driver123');
  console.log('👥 Citizen user created: <EMAIL> / citizen123');
  console.log(`📦 Created ${bins.length} waste bins`);
  console.log(`📋 Created ${pickups.length} pickup requests`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
