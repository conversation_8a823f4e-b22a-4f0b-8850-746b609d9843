// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  phone     String   @unique
  email     String?  @unique
  name      String
  password  String
  role      String   @default("CITIZEN")
  isActive  Boolean  @default(true)
  avatar    String?
  address   String?
  
  // Relationships
  pickups   Pickup[]
  reports   Report[]
  assignments Assignment[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Pickup {
  id          String       @id @default(cuid())
  userId      String
  location    String
  latitude    Float?
  longitude   Float?
  description String?
  wasteType   String       @default("GENERAL")
  status      String       @default("PENDING")
  priority    String       @default("NORMAL")
  scheduledAt DateTime?
  completedAt DateTime?
  
  // Relationships
  user        User         @relation(fields: [userId], references: [id])
  assignments Assignment[]
  images      PickupImage[]
  
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@map("pickups")
}

model Bin {
  id          String    @id @default(cuid())
  name        String
  location    String
  latitude    Float
  longitude   Float
  capacity    Float     @default(100) // in liters
  currentLevel Float    @default(0)   // percentage
  status      String    @default("NORMAL")
  lastEmptied DateTime?
  
  // IoT sensor data
  sensorId    String?   @unique
  batteryLevel Float?   // percentage
  temperature Float?    // celsius
  humidity    Float?    // percentage
  
  // Relationships
  alerts      Alert[]
  reports     Report[]
  
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("bins")
}

model Alert {
  id          String      @id @default(cuid())
  binId       String?
  type        String
  severity    String      @default("MEDIUM")
  title       String
  message     String
  isResolved  Boolean     @default(false)
  resolvedAt  DateTime?
  resolvedBy  String?
  
  // Relationships
  bin         Bin?        @relation(fields: [binId], references: [id])
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("alerts")
}

model Report {
  id          String     @id @default(cuid())
  userId      String
  binId       String?
  type        String
  title       String
  description String
  location    String?
  latitude    Float?
  longitude   Float?
  status      String       @default("PENDING")
  
  // Relationships
  user        User       @relation(fields: [userId], references: [id])
  bin         Bin?       @relation(fields: [binId], references: [id])
  images      ReportImage[]
  
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@map("reports")
}

model Assignment {
  id        String           @id @default(cuid())
  pickupId  String
  userId    String           // Driver/Worker assigned
  status    String           @default("ASSIGNED")
  notes     String?
  
  // Relationships
  pickup    Pickup           @relation(fields: [pickupId], references: [id])
  user      User             @relation(fields: [userId], references: [id])
  
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@map("assignments")
}

model PickupImage {
  id       String @id @default(cuid())
  pickupId String
  url      String
  caption  String?
  
  // Relationships
  pickup   Pickup @relation(fields: [pickupId], references: [id])
  
  createdAt DateTime @default(now())

  @@map("pickup_images")
}

model ReportImage {
  id       String @id @default(cuid())
  reportId String
  url      String
  caption  String?
  
  // Relationships
  report   Report @relation(fields: [reportId], references: [id])
  
  createdAt DateTime @default(now())

  @@map("report_images")
}

model SensorData {
  id          String   @id @default(cuid())
  sensorId    String
  binId       String?
  dataType    String   // 'level', 'temperature', 'humidity', etc.
  value       Float
  unit        String   // '%', 'C', 'cm', etc.
  timestamp   DateTime @default(now())
  
  createdAt   DateTime @default(now())

  @@map("sensor_data")
}


