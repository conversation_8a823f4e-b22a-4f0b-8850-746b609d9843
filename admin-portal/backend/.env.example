# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/cleancity360"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Redis
REDIS_URL="redis://localhost:6379"

# MQTT
MQTT_BROKER_URL="mqtt://localhost:1883"

# Server
PORT=3001
NODE_ENV="development"

# Email (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File uploads
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880

# Logging
LOG_LEVEL="debug"
