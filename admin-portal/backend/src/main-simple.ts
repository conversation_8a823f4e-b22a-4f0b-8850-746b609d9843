import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as express from 'express';

async function bootstrap() {
  // Create a simple Express app for now
  const app = express();
  
  // Enable CORS
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
    
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  app.use(express.json());

  // Health check endpoint
  app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', message: 'Cleancity360 API is running' });
  });

  // Mock auth endpoints
  app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    // Mock authentication
    if (email && password) {
      res.json({
        access_token: 'mock-jwt-token',
        user: {
          id: '1',
          email: email,
          name: 'Demo User',
          role: 'CITIZEN',
          phone: '+237123456789'
        }
      });
    } else {
      res.status(401).json({ message: 'Invalid credentials' });
    }
  });

  app.post('/api/auth/register', (req, res) => {
    const { email, name, phone } = req.body;
    
    res.json({
      access_token: 'mock-jwt-token',
      user: {
        id: '2',
        email: email,
        name: name,
        role: 'CITIZEN',
        phone: phone
      }
    });
  });

  app.get('/api/auth/profile', (req, res) => {
    res.json({
      id: '1',
      email: '<EMAIL>',
      name: 'Demo User',
      role: 'CITIZEN',
      phone: '+237123456789'
    });
  });

  // Mock bins endpoints
  app.get('/api/bins', (req, res) => {
    res.json({
      bins: [
        {
          id: '1',
          name: 'Bastos Market Bin',
          location: 'Marché de Bastos, Yaoundé',
          latitude: 3.8480,
          longitude: 11.5021,
          capacity: 240,
          currentLevel: 75,
          status: 'NORMAL'
        }
      ],
      pagination: { total: 1, page: 1, limit: 10, totalPages: 1 }
    });
  });

  app.get('/api/bins/nearby', (req, res) => {
    res.json([
      {
        id: '1',
        name: 'Bastos Market Bin',
        location: 'Marché de Bastos, Yaoundé',
        latitude: 3.8480,
        longitude: 11.5021,
        capacity: 240,
        currentLevel: 75,
        status: 'NORMAL'
      }
    ]);
  });

  // Mock pickups endpoints
  app.get('/api/pickups/my-pickups', (req, res) => {
    res.json({
      pickups: [
        {
          id: '1',
          location: 'Quartier Bastos',
          description: 'Household waste pickup',
          wasteType: 'GENERAL',
          status: 'PENDING',
          priority: 'NORMAL',
          createdAt: new Date().toISOString()
        }
      ],
      pagination: { total: 1, page: 1, limit: 10, totalPages: 1 }
    });
  });

  app.post('/api/pickups', (req, res) => {
    res.json({
      id: '2',
      ...req.body,
      status: 'PENDING',
      createdAt: new Date().toISOString()
    });
  });

  // Mock reports endpoints
  app.get('/api/reports/my-reports', (req, res) => {
    res.json({
      reports: [],
      pagination: { total: 0, page: 1, limit: 10, totalPages: 0 }
    });
  });

  app.post('/api/reports', (req, res) => {
    res.json({
      id: '1',
      ...req.body,
      status: 'PENDING',
      createdAt: new Date().toISOString()
    });
  });

  const port = process.env.PORT || 3001;
  app.listen(port, () => {
    console.log(`🚀 Cleancity360 API (Simple Mode) is running on: http://localhost:${port}`);
    console.log(`📚 Health check: http://localhost:${port}/api/health`);
  });
}

bootstrap();
