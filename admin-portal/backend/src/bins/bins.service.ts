import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CreateBinDto } from './dto/create-bin.dto';
import { UpdateBinDto } from './dto/update-bin.dto';
import { BinStatus } from '../common/enums';

@Injectable()
export class BinsService {
  constructor(private prisma: PrismaService) {}

  async create(createBinDto: CreateBinDto) {
    return this.prisma.bin.create({
      data: createBinDto,
      include: {
        alerts: {
          where: { isResolved: false },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        _count: {
          select: {
            alerts: { where: { isResolved: false } },
            reports: true,
          },
        },
      },
    });
  }

  async findAll(page = 1, limit = 10, status?: BinStatus) {
    const skip = (page - 1) * limit;
    
    const where = status ? { status } : {};
    
    const [bins, total] = await Promise.all([
      this.prisma.bin.findMany({
        where,
        skip,
        take: limit,
        include: {
          alerts: {
            where: { isResolved: false },
            orderBy: { createdAt: 'desc' },
            take: 3,
          },
          _count: {
            select: {
              alerts: { where: { isResolved: false } },
              reports: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.bin.count({ where }),
    ]);

    return {
      bins,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const bin = await this.prisma.bin.findUnique({
      where: { id },
      include: {
        alerts: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        reports: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            alerts: { where: { isResolved: false } },
            reports: true,
          },
        },
      },
    });

    if (!bin) {
      throw new NotFoundException('Bin not found');
    }

    return bin;
  }

  async update(id: string, updateBinDto: UpdateBinDto) {
    const bin = await this.findOne(id);
    
    return this.prisma.bin.update({
      where: { id },
      data: updateBinDto,
      include: {
        alerts: {
          where: { isResolved: false },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        _count: {
          select: {
            alerts: { where: { isResolved: false } },
            reports: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    const bin = await this.findOne(id);
    
    return this.prisma.bin.delete({
      where: { id },
    });
  }

  async updateSensorData(sensorId: string, data: any) {
    // Find bin by sensor ID
    const bin = await this.prisma.bin.findUnique({
      where: { sensorId },
    });

    if (!bin) {
      throw new NotFoundException('Bin with sensor ID not found');
    }

    // Update bin data
    const updateData: any = {};
    if (data.level !== undefined) {
      updateData.currentLevel = data.level;
      
      // Update status based on level
      if (data.level >= 95) {
        updateData.status = BinStatus.OVERFLOW;
      } else if (data.level >= 80) {
        updateData.status = BinStatus.FULL;
      } else {
        updateData.status = BinStatus.NORMAL;
      }
    }

    if (data.batteryLevel !== undefined) {
      updateData.batteryLevel = data.batteryLevel;
    }

    if (data.temperature !== undefined) {
      updateData.temperature = data.temperature;
    }

    if (data.humidity !== undefined) {
      updateData.humidity = data.humidity;
    }

    // Store sensor data
    await this.prisma.sensorData.create({
      data: {
        sensorId,
        binId: bin.id,
        dataType: 'level',
        value: data.level || 0,
        unit: '%',
      },
    });

    return this.prisma.bin.update({
      where: { id: bin.id },
      data: updateData,
    });
  }

  async getBinsByLocation(latitude: number, longitude: number, radius = 5) {
    // Simple distance calculation (for production, use PostGIS)
    const bins = await this.prisma.bin.findMany({
      include: {
        _count: {
          select: {
            alerts: { where: { isResolved: false } },
          },
        },
      },
    });

    // Filter by distance (simplified calculation)
    const nearbyBins = bins.filter(bin => {
      const distance = this.calculateDistance(
        latitude,
        longitude,
        bin.latitude,
        bin.longitude
      );
      return distance <= radius;
    });

    return nearbyBins;
  }

  async getStatistics() {
    const [
      totalBins,
      normalBins,
      fullBins,
      overflowBins,
      damagedBins,
      averageLevel,
    ] = await Promise.all([
      this.prisma.bin.count(),
      this.prisma.bin.count({ where: { status: BinStatus.NORMAL } }),
      this.prisma.bin.count({ where: { status: BinStatus.FULL } }),
      this.prisma.bin.count({ where: { status: BinStatus.OVERFLOW } }),
      this.prisma.bin.count({ where: { status: BinStatus.MAINTENANCE } }),
      this.prisma.bin.aggregate({
        _avg: { currentLevel: true },
      }),
    ]);

    return {
      totalBins,
      statusBreakdown: {
        normal: normalBins,
        full: fullBins,
        overflow: overflowBins,
        damaged: damagedBins,
      },
      averageLevel: averageLevel._avg.currentLevel || 0,
    };
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c;
    return d;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  async forceEmpty(id: string) {
    const bin = await this.findOne(id);

    // Update bin status and create alert for force empty
    await Promise.all([
      this.prisma.bin.update({
        where: { id },
        data: {
          currentLevel: 0,
          lastEmptied: new Date(),
          status: BinStatus.NORMAL,
        },
      }),
      this.prisma.alert.create({
        data: {
          binId: id,
          type: 'FORCE_EMPTY',
          severity: 'MEDIUM',
          title: 'Force Empty Requested',
          message: `Force empty request sent for bin ${bin.name}`,
          isResolved: false,
        },
      }),
    ]);

    return { message: 'Force empty request sent successfully' };
  }

  async scheduleMaintenance(id: string, data: any) {
    const bin = await this.findOne(id);

    // Create maintenance alert
    await this.prisma.alert.create({
      data: {
        binId: id,
        type: 'MAINTENANCE',
        severity: 'LOW',
        title: 'Maintenance Scheduled',
        message: `Maintenance scheduled for bin ${bin.name}. Type: ${data.type || 'General'}. Notes: ${data.notes || 'No additional notes'}`,
        isResolved: false,
      },
    });

    return { message: 'Maintenance scheduled successfully' };
  }
}
