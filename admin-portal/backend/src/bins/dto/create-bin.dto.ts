import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsEnum } from 'class-validator';
import { BinStatus } from '../../common/enums';

export class CreateBinDto {
  @ApiProperty({
    example: 'Bastos Market Bin',
    description: 'Bin name/identifier',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '<PERSON><PERSON>, Yaoundé',
    description: 'Bin location address',
  })
  @IsString()
  @IsNotEmpty()
  location: string;

  @ApiProperty({
    example: 3.8480,
    description: 'Latitude coordinate',
  })
  @IsNumber()
  @IsNotEmpty()
  latitude: number;

  @ApiProperty({
    example: 11.5021,
    description: 'Longitude coordinate',
  })
  @IsNumber()
  @IsNotEmpty()
  longitude: number;

  @ApiProperty({
    example: 240,
    description: 'Bin capacity in liters',
    default: 100,
  })
  @IsNumber()
  @IsOptional()
  capacity?: number = 100;

  @ApiProperty({
    example: 0,
    description: 'Current fill level percentage',
    default: 0,
  })
  @IsNumber()
  @IsOptional()
  currentLevel?: number = 0;

  @ApiProperty({
    example: BinStatus.NORMAL,
    description: 'Bin status',
    enum: BinStatus,
    default: BinStatus.NORMAL,
  })
  @IsEnum(BinStatus)
  @IsOptional()
  status?: BinStatus = BinStatus.NORMAL;

  @ApiProperty({
    example: 'sensor_001',
    description: 'IoT sensor identifier',
    required: false,
  })
  @IsString()
  @IsOptional()
  sensorId?: string;

  @ApiProperty({
    example: 85,
    description: 'Sensor battery level percentage',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  batteryLevel?: number;

  @ApiProperty({
    example: 25.5,
    description: 'Temperature in Celsius',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  temperature?: number;

  @ApiProperty({
    example: 65.2,
    description: 'Humidity percentage',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  humidity?: number;
}
