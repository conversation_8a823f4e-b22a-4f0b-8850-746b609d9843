import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { BinStatus, Role } from '../common/enums';

import { BinsService } from './bins.service';
import { CreateBinDto } from './dto/create-bin.dto';
import { UpdateBinDto } from './dto/update-bin.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('bins')
@Controller('bins')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BinsController {
  constructor(private readonly binsService: BinsService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Create a new waste bin (Admin only)' })
  @ApiResponse({ status: 201, description: 'Bin created successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  create(@Body() createBinDto: CreateBinDto) {
    return this.binsService.create(createBinDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all waste bins with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'status', required: false, enum: BinStatus })
  @ApiResponse({ status: 200, description: 'Bins retrieved successfully' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: BinStatus,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    return this.binsService.findAll(pageNum, limitNum, status);
  }

  @Get('statistics')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Get bin statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  getStatistics() {
    return this.binsService.getStatistics();
  }

  @Get('nearby')
  @ApiOperation({ summary: 'Get bins near a location' })
  @ApiQuery({ name: 'latitude', required: true, type: Number })
  @ApiQuery({ name: 'longitude', required: true, type: Number })
  @ApiQuery({ name: 'radius', required: false, type: Number, example: 5 })
  @ApiResponse({ status: 200, description: 'Nearby bins retrieved successfully' })
  getNearbyBins(
    @Query('latitude') latitude: string,
    @Query('longitude') longitude: string,
    @Query('radius') radius?: string,
  ) {
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);
    const rad = radius ? parseFloat(radius) : 5;
    return this.binsService.getBinsByLocation(lat, lng, rad);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get bin by ID' })
  @ApiResponse({ status: 200, description: 'Bin retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Bin not found' })
  findOne(@Param('id') id: string) {
    return this.binsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Update bin' })
  @ApiResponse({ status: 200, description: 'Bin updated successfully' })
  @ApiResponse({ status: 404, description: 'Bin not found' })
  update(@Param('id') id: string, @Body() updateBinDto: UpdateBinDto) {
    return this.binsService.update(id, updateBinDto);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Delete bin (Admin only)' })
  @ApiResponse({ status: 200, description: 'Bin deleted successfully' })
  @ApiResponse({ status: 404, description: 'Bin not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  remove(@Param('id') id: string) {
    return this.binsService.remove(id);
  }

  @Post('sensor/:sensorId/data')
  @ApiOperation({ summary: 'Update bin data from IoT sensor' })
  @ApiResponse({ status: 200, description: 'Sensor data updated successfully' })
  @ApiResponse({ status: 404, description: 'Bin with sensor ID not found' })
  updateSensorData(@Param('sensorId') sensorId: string, @Body() data: any) {
    return this.binsService.updateSensorData(sensorId, data);
  }
}
