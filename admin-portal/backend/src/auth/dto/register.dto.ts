import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsPhoneNumber } from 'class-validator';
import { Role } from '../../common/enums';

export class RegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: '+237123456789',
    description: 'User phone number',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'User full name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password',
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: '<PERSON>uart<PERSON>, <PERSON><PERSON><PERSON>',
    description: 'User address',
    required: false,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    example: Role.CITIZEN,
    description: 'User role',
    enum: Role,
    default: Role.CITIZEN,
    required: false,
  })
  @IsOptional()
  role?: Role = Role.CITIZEN;
}
