import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CreatePickupDto } from './dto/create-pickup.dto';
import { UpdatePickupDto } from './dto/update-pickup.dto';
import { PickupStatus, Priority, WasteType } from '../common/enums';
import { PointsService } from '../points/points.service';

@Injectable()
export class PickupsService {
  constructor(
    private prisma: PrismaService,
    private pointsService: PointsService,
  ) {}

  async create(createPickupDto: CreatePickupDto, userId: string) {
    const pickup = await this.prisma.pickup.create({
      data: {
        ...createPickupDto,
        userId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
      },
    });

    // Award points for pickup request
    try {
      const isUrgent = createPickupDto.priority === Priority.URGENT;
      await this.pointsService.awardPickupPoints(userId, pickup.id, isUrgent);
    } catch (error) {
      console.log('Failed to award pickup points:', error.message);
      // Don't fail the pickup creation if points fail
    }

    return pickup;
  }

  async findAll(
    page = 1,
    limit = 10,
    status?: PickupStatus,
    priority?: Priority,
    wasteType?: WasteType,
  ) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (wasteType) where.wasteType = wasteType;
    
    const [pickups, total] = await Promise.all([
      this.prisma.pickup.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
            },
          },
          assignments: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          images: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.pickup.count({ where }),
    ]);

    return {
      pickups,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const pickup = await this.prisma.pickup.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            address: true,
          },
        },
        assignments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        images: true,
      },
    });

    if (!pickup) {
      throw new NotFoundException('Pickup not found');
    }

    return pickup;
  }

  async update(id: string, updatePickupDto: UpdatePickupDto) {
    const pickup = await this.findOne(id);
    
    return this.prisma.pickup.update({
      where: { id },
      data: updatePickupDto,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        assignments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
          },
        },
      },
    });
  }

  async assignDriver(pickupId: string, driverId: string) {
    // Check if pickup exists and is not already assigned
    const pickup = await this.findOne(pickupId);
    
    if (pickup.status !== PickupStatus.PENDING) {
      throw new Error('Pickup is not available for assignment');
    }

    // Create assignment
    const assignment = await this.prisma.assignment.create({
      data: {
        pickupId,
        userId: driverId,
      },
    });

    // Update pickup status
    await this.prisma.pickup.update({
      where: { id: pickupId },
      data: { status: PickupStatus.ASSIGNED },
    });

    return assignment;
  }

  async getUserPickups(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [pickups, total] = await Promise.all([
      this.prisma.pickup.findMany({
        where: { userId },
        skip,
        take: limit,
        include: {
          assignments: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          images: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.pickup.count({ where: { userId } }),
    ]);

    return {
      pickups,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async startPickup(id: string) {
    const pickup = await this.findOne(id);

    if (pickup.status !== PickupStatus.ASSIGNED && pickup.status !== PickupStatus.PENDING) {
      throw new Error('Pickup cannot be started in current status');
    }

    return this.prisma.pickup.update({
      where: { id },
      data: {
        status: PickupStatus.IN_PROGRESS,
      },
    });
  }

  async completePickup(id: string, data?: any) {
    const pickup = await this.findOne(id);

    if (pickup.status !== PickupStatus.IN_PROGRESS) {
      throw new Error('Pickup is not in progress');
    }

    return this.prisma.pickup.update({
      where: { id },
      data: {
        status: PickupStatus.COMPLETED,
        completedAt: new Date(),
      },
    });
  }

  async cancelPickup(id: string, reason?: string) {
    const pickup = await this.findOne(id);

    if (pickup.status === PickupStatus.COMPLETED) {
      throw new Error('Cannot cancel completed pickup');
    }

    return this.prisma.pickup.update({
      where: { id },
      data: {
        status: PickupStatus.CANCELLED,
      },
    });
  }
}
