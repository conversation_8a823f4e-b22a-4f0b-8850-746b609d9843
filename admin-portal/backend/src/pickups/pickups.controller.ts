import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Query,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { PickupStatus, Priority, WasteType, Role } from '../common/enums';

import { PickupsService } from './pickups.service';
import { CreatePickupDto } from './dto/create-pickup.dto';
import { UpdatePickupDto } from './dto/update-pickup.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('pickups')
@Controller('pickups')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PickupsController {
  constructor(private readonly pickupsService: PickupsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new pickup request' })
  @ApiResponse({ status: 201, description: 'Pickup request created successfully' })
  create(@Body() createPickupDto: CreatePickupDto, @Request() req) {
    return this.pickupsService.create(createPickupDto, req.user.id);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.DRIVER)
  @ApiOperation({ summary: 'Get all pickup requests with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'status', required: false, enum: PickupStatus })
  @ApiQuery({ name: 'priority', required: false, enum: Priority })
  @ApiQuery({ name: 'wasteType', required: false, enum: WasteType })
  @ApiResponse({ status: 200, description: 'Pickup requests retrieved successfully' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: PickupStatus,
    @Query('priority') priority?: Priority,
    @Query('wasteType') wasteType?: WasteType,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    return this.pickupsService.findAll(pageNum, limitNum, status, priority, wasteType);
  }

  @Get('my-pickups')
  @ApiOperation({ summary: 'Get current user pickup requests' })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiResponse({ status: 200, description: 'User pickup requests retrieved successfully' })
  getMyPickups(
    @Request() req,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    return this.pickupsService.getUserPickups(req.user.id, pageNum, limitNum);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get pickup request by ID' })
  @ApiResponse({ status: 200, description: 'Pickup request retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Pickup request not found' })
  findOne(@Param('id') id: string) {
    return this.pickupsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.DRIVER)
  @ApiOperation({ summary: 'Update pickup request' })
  @ApiResponse({ status: 200, description: 'Pickup request updated successfully' })
  @ApiResponse({ status: 404, description: 'Pickup request not found' })
  update(@Param('id') id: string, @Body() updatePickupDto: UpdatePickupDto) {
    return this.pickupsService.update(id, updatePickupDto);
  }

  @Post(':id/assign/:driverId')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Assign pickup to driver' })
  @ApiResponse({ status: 200, description: 'Pickup assigned successfully' })
  @ApiResponse({ status: 404, description: 'Pickup or driver not found' })
  assignDriver(@Param('id') id: string, @Param('driverId') driverId: string) {
    return this.pickupsService.assignDriver(id, driverId);
  }
}
