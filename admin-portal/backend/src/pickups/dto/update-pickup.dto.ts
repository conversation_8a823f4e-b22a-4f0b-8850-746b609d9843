import { PartialType } from '@nestjs/swagger';
import { CreatePickupDto } from './create-pickup.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { PickupStatus } from '../../common/enums';
import { ApiProperty } from '@nestjs/swagger';

export class UpdatePickupDto extends PartialType(CreatePickupDto) {
  @ApiProperty({
    example: PickupStatus.COMPLETED,
    description: 'Pickup status',
    enum: PickupStatus,
    required: false,
  })
  @IsEnum(PickupStatus)
  @IsOptional()
  status?: PickupStatus;

  @ApiProperty({
    example: '2024-01-15T14:30:00Z',
    description: 'Completion timestamp',
    required: false,
  })
  @IsOptional()
  completedAt?: Date;
}
