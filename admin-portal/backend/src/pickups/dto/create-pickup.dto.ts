import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsNumber } from 'class-validator';
import { WasteType, Priority } from '../../common/enums';

export class CreatePickupDto {
  @ApiProperty({
    example: 'Quartier Ba<PERSON>, Yaoundé',
    description: 'Pickup location address',
  })
  @IsString()
  @IsNotEmpty()
  location: string;

  @ApiProperty({
    example: 3.8480,
    description: 'Latitude coordinate',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  latitude?: number;

  @ApiProperty({
    example: 11.5021,
    description: 'Longitude coordinate',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  longitude?: number;

  @ApiProperty({
    example: 'Large amount of household waste',
    description: 'Description of waste to be collected',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    example: WasteType.GENERAL,
    description: 'Type of waste',
    enum: WasteType,
    default: WasteType.GENERAL,
  })
  @IsEnum(WasteType)
  @IsOptional()
  wasteType?: WasteType = WasteType.GENERAL;

  @ApiProperty({
    example: Priority.NORMAL,
    description: 'Priority level',
    enum: Priority,
    default: Priority.NORMAL,
  })
  @IsEnum(Priority)
  @IsOptional()
  priority?: Priority = Priority.NORMAL;

  @ApiProperty({
    example: '2024-01-15T10:00:00Z',
    description: 'Preferred pickup date and time',
    required: false,
  })
  @IsOptional()
  scheduledAt?: Date;
}
