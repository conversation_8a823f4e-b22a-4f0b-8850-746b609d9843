import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DashboardService } from './dashboard.service';

@ApiTags('dashboard')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalBins: { type: 'number' },
        activeBins: { type: 'number' },
        fullBins: { type: 'number' },
        overflowBins: { type: 'number' },
        activeAlerts: { type: 'number' },
        criticalAlerts: { type: 'number' },
        pendingPickups: { type: 'number' },
        completedPickups: { type: 'number' },
        activeUsers: { type: 'number' },
        totalReports: { type: 'number' },
        resolvedReports: { type: 'number' },
      },
    },
  })
  async getStats() {
    return this.dashboardService.getStats();
  }

  @Get('recent-activity')
  @ApiOperation({ summary: 'Get recent activity' })
  @ApiResponse({
    status: 200,
    description: 'Recent activity retrieved successfully',
  })
  async getRecentActivity() {
    return this.dashboardService.getRecentActivity();
  }

  @Get('analytics')
  @ApiOperation({ summary: 'Get dashboard analytics' })
  @ApiQuery({ name: 'period', required: false, enum: ['7d', '30d', '90d', '1y'], example: '7d' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        collectionEfficiency: { type: 'number' },
        averageResponseTime: { type: 'number' },
        wasteCollected: { type: 'number' },
        carbonSaved: { type: 'number' },
        trends: {
          type: 'object',
          properties: {
            pickups: { type: 'array' },
            alerts: { type: 'array' },
            efficiency: { type: 'array' },
          },
        },
      },
    },
  })
  async getAnalytics(@Query('period') period: string = '7d') {
    return this.dashboardService.getAnalytics(period);
  }
}
