import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DashboardService } from './dashboard.service';

@ApiTags('dashboard')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalBins: { type: 'number' },
        activeBins: { type: 'number' },
        fullBins: { type: 'number' },
        overflowBins: { type: 'number' },
        activeAlerts: { type: 'number' },
        criticalAlerts: { type: 'number' },
        pendingPickups: { type: 'number' },
        completedPickups: { type: 'number' },
        activeUsers: { type: 'number' },
        totalReports: { type: 'number' },
        resolvedReports: { type: 'number' },
      },
    },
  })
  async getStats() {
    return this.dashboardService.getStats();
  }

  @Get('recent-activity')
  @ApiOperation({ summary: 'Get recent activity' })
  @ApiResponse({
    status: 200,
    description: 'Recent activity retrieved successfully',
  })
  async getRecentActivity() {
    return this.dashboardService.getRecentActivity();
  }
}
