import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CreateAlertDto } from './dto/create-alert.dto';
import { UpdateAlertDto } from './dto/update-alert.dto';
import { AlertType, Severity } from '../common/enums';

@Injectable()
export class AlertsService {
  constructor(private prisma: PrismaService) {}

  async create(createAlertDto: CreateAlertDto) {
    return this.prisma.alert.create({
      data: createAlertDto,
      include: {
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
            latitude: true,
            longitude: true,
          },
        },
      },
    });
  }

  async findAll(
    page = 1,
    limit = 10,
    type?: AlertType,
    severity?: Severity,
    isResolved?: boolean,
  ) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    if (type) where.type = type;
    if (severity) where.severity = severity;
    if (isResolved !== undefined) where.isResolved = isResolved;
    
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where,
        skip,
        take: limit,
        include: {
          bin: {
            select: {
              id: true,
              name: true,
              location: true,
              latitude: true,
              longitude: true,
              currentLevel: true,
              status: true,
            },
          },
        },
        orderBy: [
          { isResolved: 'asc' },
          { severity: 'desc' },
          { createdAt: 'desc' },
        ],
      }),
      this.prisma.alert.count({ where }),
    ]);

    return {
      alerts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const alert = await this.prisma.alert.findUnique({
      where: { id },
      include: {
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
            latitude: true,
            longitude: true,
            currentLevel: true,
            status: true,
            capacity: true,
          },
        },
      },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    return alert;
  }

  async update(id: string, updateAlertDto: UpdateAlertDto) {
    const alert = await this.findOne(id);
    
    return this.prisma.alert.update({
      where: { id },
      data: updateAlertDto,
      include: {
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
            latitude: true,
            longitude: true,
          },
        },
      },
    });
  }

  async resolve(id: string, resolvedBy: string) {
    const alert = await this.findOne(id);
    
    if (alert.isResolved) {
      throw new Error('Alert is already resolved');
    }

    return this.prisma.alert.update({
      where: { id },
      data: {
        isResolved: true,
        resolvedAt: new Date(),
        resolvedBy,
      },
      include: {
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    const alert = await this.findOne(id);
    
    return this.prisma.alert.delete({
      where: { id },
    });
  }

  async createBinAlert(binId: string, type: AlertType, severity: Severity, message: string) {
    const bin = await this.prisma.bin.findUnique({
      where: { id: binId },
    });

    if (!bin) {
      throw new NotFoundException('Bin not found');
    }

    const title = this.generateAlertTitle(type, bin.name);

    return this.create({
      binId,
      type,
      severity,
      title,
      message,
    });
  }

  async getStatistics() {
    const [
      totalAlerts,
      unresolvedAlerts,
      criticalAlerts,
      highAlerts,
      mediumAlerts,
      lowAlerts,
      alertsByType,
    ] = await Promise.all([
      this.prisma.alert.count(),
      this.prisma.alert.count({ where: { isResolved: false } }),
      this.prisma.alert.count({ 
        where: { isResolved: false, severity: Severity.CRITICAL } 
      }),
      this.prisma.alert.count({ 
        where: { isResolved: false, severity: Severity.HIGH } 
      }),
      this.prisma.alert.count({ 
        where: { isResolved: false, severity: Severity.MEDIUM } 
      }),
      this.prisma.alert.count({ 
        where: { isResolved: false, severity: Severity.LOW } 
      }),
      this.prisma.alert.groupBy({
        by: ['type'],
        where: { isResolved: false },
        _count: { type: true },
      }),
    ]);

    return {
      totalAlerts,
      unresolvedAlerts,
      severityBreakdown: {
        critical: criticalAlerts,
        high: highAlerts,
        medium: mediumAlerts,
        low: lowAlerts,
      },
      typeBreakdown: alertsByType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {}),
    };
  }

  async getRecentAlerts(limit = 10) {
    return this.prisma.alert.findMany({
      where: { isResolved: false },
      take: limit,
      include: {
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
      orderBy: [
        { severity: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  private generateAlertTitle(type: AlertType, binName: string): string {
    const titles = {
      BIN_FULL: `Bin Full - ${binName}`,
      BIN_OVERFLOW: `Bin Overflow - ${binName}`,
      SENSOR_OFFLINE: `Sensor Offline - ${binName}`,
      MAINTENANCE_REQUIRED: `Maintenance Required - ${binName}`,
      ILLEGAL_DUMPING: `Illegal Dumping Detected - ${binName}`,
      SYSTEM_ERROR: `System Error - ${binName}`,
    };

    return titles[type] || `Alert - ${binName}`;
  }
}
