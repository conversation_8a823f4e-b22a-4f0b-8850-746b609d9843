import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum } from 'class-validator';
import { AlertType, Severity } from '../../common/enums';

export class CreateAlertDto {
  @ApiProperty({
    example: 'bin_001',
    description: 'ID of the bin related to this alert',
    required: false,
  })
  @IsString()
  @IsOptional()
  binId?: string;

  @ApiProperty({
    example: AlertType.BIN_FULL,
    description: 'Type of alert',
    enum: AlertType,
  })
  @IsEnum(AlertType)
  @IsNotEmpty()
  type: AlertType;

  @ApiProperty({
    example: Severity.HIGH,
    description: 'Alert severity level',
    enum: Severity,
    default: Severity.MEDIUM,
  })
  @IsEnum(Severity)
  @IsOptional()
  severity?: Severity = Severity.MEDIUM;

  @ApiProperty({
    example: 'Bin Full Alert',
    description: 'Alert title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: 'Bin at <PERSON><PERSON> is full and needs emptying',
    description: 'Detailed alert message',
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}
