import { PartialType } from '@nestjs/swagger';
import { CreateAlertDto } from './create-alert.dto';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateAlertDto extends PartialType(CreateAlertDto) {
  @ApiProperty({
    example: true,
    description: 'Whether the alert is resolved',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isResolved?: boolean;

  @ApiProperty({
    example: 'user_123',
    description: 'ID of user who resolved the alert',
    required: false,
  })
  @IsString()
  @IsOptional()
  resolvedBy?: string;
}
