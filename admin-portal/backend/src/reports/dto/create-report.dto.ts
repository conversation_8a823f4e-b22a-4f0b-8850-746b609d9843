import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsNumber } from 'class-validator';
import { ReportType } from '../../common/enums';

export class CreateReportDto {
  @ApiProperty({
    example: 'bin_001',
    description: 'ID of the bin related to this report',
    required: false,
  })
  @IsString()
  @IsOptional()
  binId?: string;

  @ApiProperty({
    example: ReportType.ILLEGAL_DUMPING,
    description: 'Type of report',
    enum: ReportType,
  })
  @IsEnum(ReportType)
  @IsNotEmpty()
  type: ReportType;

  @ApiProperty({
    example: 'Illegal Dumping Report',
    description: 'Report title',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: 'Large pile of construction waste dumped near the bin',
    description: 'Detailed description of the issue',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    example: '<PERSON>uart<PERSON>, <PERSON><PERSON><PERSON>',
    description: 'Location where the issue was observed',
    required: false,
  })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({
    example: 3.8480,
    description: 'Latitude coordinate',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  latitude?: number;

  @ApiProperty({
    example: 11.5021,
    description: 'Longitude coordinate',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  longitude?: number;
}
