import { PartialType } from '@nestjs/swagger';
import { CreateReportDto } from './create-report.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { ReportStatus } from '../../common/enums';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateReportDto extends PartialType(CreateReportDto) {
  @ApiProperty({
    example: ReportStatus.IN_PROGRESS,
    description: 'Report status',
    enum: ReportStatus,
    required: false,
  })
  @IsEnum(ReportStatus)
  @IsOptional()
  status?: ReportStatus;
}
