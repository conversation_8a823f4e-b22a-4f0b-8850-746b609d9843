import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { ReportType, ReportStatus } from '../common/enums';

@Injectable()
export class ReportsService {
  constructor(private prisma: PrismaService) {}

  async create(createReportDto: CreateReportDto, userId: string) {
    return this.prisma.report.create({
      data: {
        ...createReportDto,
        userId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
            latitude: true,
            longitude: true,
          },
        },
        images: true,
      },
    });
  }

  async findAll(
    page = 1,
    limit = 10,
    type?: ReportType,
    status?: ReportStatus,
  ) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    if (type) where.type = type;
    if (status) where.status = status;
    
    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
            },
          },
          bin: {
            select: {
              id: true,
              name: true,
              location: true,
              latitude: true,
              longitude: true,
            },
          },
          images: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.report.count({ where }),
    ]);

    return {
      reports,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            address: true,
          },
        },
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
            latitude: true,
            longitude: true,
            currentLevel: true,
            status: true,
          },
        },
        images: true,
      },
    });

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async update(id: string, updateReportDto: UpdateReportDto) {
    const report = await this.findOne(id);
    
    return this.prisma.report.update({
      where: { id },
      data: updateReportDto,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
          },
        },
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
        images: true,
      },
    });
  }

  async remove(id: string) {
    const report = await this.findOne(id);
    
    return this.prisma.report.delete({
      where: { id },
    });
  }

  async getUserReports(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where: { userId },
        skip,
        take: limit,
        include: {
          bin: {
            select: {
              id: true,
              name: true,
              location: true,
              latitude: true,
              longitude: true,
            },
          },
          images: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.report.count({ where: { userId } }),
    ]);

    return {
      reports,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getStatistics() {
    const [
      totalReports,
      pendingReports,
      investigatingReports,
      resolvedReports,
      rejectedReports,
      reportsByType,
    ] = await Promise.all([
      this.prisma.report.count(),
      this.prisma.report.count({ where: { status: ReportStatus.PENDING } }),
      this.prisma.report.count({ where: { status: ReportStatus.IN_PROGRESS } }),
      this.prisma.report.count({ where: { status: ReportStatus.RESOLVED } }),
      this.prisma.report.count({ where: { status: ReportStatus.REJECTED } }),
      this.prisma.report.groupBy({
        by: ['type'],
        _count: { type: true },
      }),
    ]);

    return {
      totalReports,
      statusBreakdown: {
        pending: pendingReports,
        investigating: investigatingReports,
        resolved: resolvedReports,
        rejected: rejectedReports,
      },
      typeBreakdown: reportsByType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {}),
    };
  }

  async getRecentReports(limit = 10) {
    return this.prisma.report.findMany({
      where: { 
        status: { 
          in: [ReportStatus.PENDING, ReportStatus.IN_PROGRESS]
        } 
      },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        bin: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
