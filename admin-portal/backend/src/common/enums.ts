// TypeScript enums for the application
// These replace Prisma enums since SQLite doesn't support them

export enum Role {
  CITIZEN = 'CITIZEN',
  DRIVER = 'DRIVER',
  ADMIN = 'ADMIN',
  SUPERVISOR = 'SUPERVISOR',
}

export enum WasteType {
  GENERAL = 'GENERAL',
  RECYCLABLE = 'RECYCLABLE',
  ORGANIC = 'ORGANIC',
  ELECTRONIC = 'ELECTRONIC',
  HAZARDOUS = 'HAZARDOUS',
}

export enum PickupStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum Priority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum ReportType {
  OVERFLOWING_BIN = 'OVERFLOWING_BIN',
  DAMAGED_BIN = 'DAMAGED_BIN',
  ILLEGAL_DUMPING = 'ILLEGAL_DUMPING',
  MISSED_PICKUP = 'MISSED_PICKUP',
  POOR_SERVICE = 'POOR_SERVICE',
  OTHER = 'OTHER',
}

export enum ReportStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  REJECTED = 'REJECTED',
}

export enum BinStatus {
  NORMAL = 'NORMAL',
  FULL = 'FULL',
  OVERFLOW = 'OVERFLOW',
  MAINTENANCE = 'MAINTENANCE',
  OFFLINE = 'OFFLINE',
}

// Validation arrays for runtime checking
export const VALID_ROLES = Object.values(Role);
export const VALID_WASTE_TYPES = Object.values(WasteType);
export const VALID_PICKUP_STATUSES = Object.values(PickupStatus);
export const VALID_PRIORITIES = Object.values(Priority);
export const VALID_REPORT_TYPES = Object.values(ReportType);
export const VALID_REPORT_STATUSES = Object.values(ReportStatus);
export const VALID_BIN_STATUSES = Object.values(BinStatus);
