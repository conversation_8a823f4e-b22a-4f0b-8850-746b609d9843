// Enums for the application
export enum Role {
  CITIZEN = '<PERSON><PERSON><PERSON>Z<PERSON>',
  ADMIN = 'ADMIN',
  DRIVER = 'DRIVER',
  SUPERVISOR = 'SUPERVISOR',
}

export enum PickupStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum BinStatus {
  NORMAL = 'NORMAL',
  FULL = 'FULL',
  OVERFLOW = 'OVERFLOW',
  DAMAGED = 'DAMAGED',
  MAINTENANCE = 'MAINTENANCE',
}

export enum WasteType {
  GENERAL = 'GENERAL',
  RECYCLABLE = 'RECYCLABLE',
  ORGANIC = 'ORGANIC',
  HAZARDOUS = 'HAZARDOUS',
  ELECTRONIC = 'ELECTRONIC',
}

export enum Priority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum AlertType {
  BIN_FULL = 'BIN_FULL',
  BIN_OVERFLOW = 'BIN_OVERFLOW',
  SENSOR_OFFLINE = 'SENSOR_OFFLINE',
  MAINTENANCE_REQUIRED = 'MAINTENANCE_REQUIRED',
  ILLEGAL_DUMPING = 'ILLEGAL_DUMPING',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
}

export enum Severity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum ReportType {
  ILLEGAL_DUMPING = 'ILLEGAL_DUMPING',
  DAMAGED_BIN = 'DAMAGED_BIN',
  MISSED_PICKUP = 'MISSED_PICKUP',
  OVERFLOW = 'OVERFLOW',
  OTHER = 'OTHER',
}

export enum ReportStatus {
  PENDING = 'PENDING',
  INVESTIGATING = 'INVESTIGATING',
  RESOLVED = 'RESOLVED',
  REJECTED = 'REJECTED',
}

export enum AssignmentStatus {
  ASSIGNED = 'ASSIGNED',
  ACCEPTED = 'ACCEPTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}
