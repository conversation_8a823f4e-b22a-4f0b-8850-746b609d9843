import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';

export interface PointsTransactionDto {
  type: 'EARNED' | 'REDEEMED' | 'BONUS' | 'PENALTY';
  action: string;
  points: number;
  description: string;
  pickupId?: string;
  reportId?: string;
}

@Injectable()
export class PointsService {
  constructor(private prisma: PrismaService) {}

  async getSettings() {
    let settings = await this.prisma.pointsSettings.findFirst();
    
    if (!settings) {
      // Create default settings if none exist
      settings = await this.prisma.pointsSettings.create({
        data: {
          isEnabled: true,
          pickupRequestPoints: 10,
          reportSubmitPoints: 5,
          reportVerifiedPoints: 15,
          urgentPickupMultiplier: 1.5,
          maxDailyPoints: 100,
          pointsExpiryDays: 365,
        },
      });
    }
    
    return settings;
  }

  async updateSettings(data: Partial<{
    isEnabled: boolean;
    pickupRequestPoints: number;
    reportSubmitPoints: number;
    reportVerifiedPoints: number;
    urgentPickupMultiplier: number;
    maxDailyPoints: number;
    pointsExpiryDays: number;
  }>) {
    const settings = await this.getSettings();
    
    return this.prisma.pointsSettings.update({
      where: { id: settings.id },
      data,
    });
  }

  async getUserPoints(userId: string) {
    if (!userId) {
      throw new NotFoundException('User ID is required');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        cleanPoints: true,
        totalEarned: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async getUserTransactions(userId: string, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    
    const [transactions, total] = await Promise.all([
      this.prisma.pointsTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          user: {
            select: { name: true, email: true },
          },
        },
      }),
      this.prisma.pointsTransaction.count({
        where: { userId },
      }),
    ]);

    return {
      transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async addPoints(userId: string, transaction: PointsTransactionDto) {
    const settings = await this.getSettings();
    
    if (!settings.isEnabled) {
      throw new Error('Points system is disabled');
    }

    // Check daily limit for earned points
    if (transaction.type === 'EARNED') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todayEarned = await this.prisma.pointsTransaction.aggregate({
        where: {
          userId,
          type: 'EARNED',
          createdAt: {
            gte: today,
            lt: tomorrow,
          },
        },
        _sum: {
          points: true,
        },
      });

      const currentDailyPoints = todayEarned._sum.points || 0;
      if (currentDailyPoints + transaction.points > settings.maxDailyPoints) {
        throw new Error(`Daily points limit exceeded. Current: ${currentDailyPoints}, Limit: ${settings.maxDailyPoints}`);
      }
    }

    // Create transaction and update user points in a transaction
    return this.prisma.$transaction(async (tx) => {
      // Create the points transaction
      const pointsTransaction = await tx.pointsTransaction.create({
        data: {
          userId,
          ...transaction,
        },
      });

      // Update user's points
      const pointsChange = transaction.type === 'REDEEMED' || transaction.type === 'PENALTY' 
        ? -Math.abs(transaction.points) 
        : transaction.points;

      const totalEarnedChange = transaction.type === 'EARNED' || transaction.type === 'BONUS'
        ? transaction.points
        : 0;

      await tx.user.update({
        where: { id: userId },
        data: {
          cleanPoints: {
            increment: pointsChange,
          },
          totalEarned: {
            increment: totalEarnedChange,
          },
        },
      });

      return pointsTransaction;
    });
  }

  async awardPickupPoints(userId: string, pickupId: string, isUrgent = false) {
    const settings = await this.getSettings();
    let points = settings.pickupRequestPoints;
    
    if (isUrgent) {
      points = Math.round(points * settings.urgentPickupMultiplier);
    }

    return this.addPoints(userId, {
      type: 'EARNED',
      action: 'PICKUP_REQUEST',
      points,
      description: `Points earned for ${isUrgent ? 'urgent ' : ''}pickup request`,
      pickupId,
    });
  }

  async awardReportPoints(userId: string, reportId: string) {
    const settings = await this.getSettings();
    
    return this.addPoints(userId, {
      type: 'EARNED',
      action: 'REPORT_SUBMITTED',
      points: settings.reportSubmitPoints,
      description: 'Points earned for submitting a report',
      reportId,
    });
  }

  async awardReportVerificationBonus(userId: string, reportId: string) {
    const settings = await this.getSettings();
    
    return this.addPoints(userId, {
      type: 'BONUS',
      action: 'REPORT_VERIFIED',
      points: settings.reportVerifiedPoints,
      description: 'Bonus points for verified report',
      reportId,
    });
  }

  async getLeaderboard(limit = 10) {
    return this.prisma.user.findMany({
      where: {
        role: 'CITIZEN',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        avatar: true,
        cleanPoints: true,
        totalEarned: true,
      },
      orderBy: {
        cleanPoints: 'desc',
      },
      take: limit,
    });
  }

  async getPointsStatistics() {
    const [
      totalUsers,
      totalPointsEarned,
      totalTransactions,
      topEarner,
    ] = await Promise.all([
      this.prisma.user.count({
        where: { role: 'CITIZEN' },
      }),
      this.prisma.pointsTransaction.aggregate({
        where: { type: { in: ['EARNED', 'BONUS'] } },
        _sum: { points: true },
      }),
      this.prisma.pointsTransaction.count(),
      this.prisma.user.findFirst({
        where: { role: 'CITIZEN' },
        orderBy: { cleanPoints: 'desc' },
        select: { name: true, cleanPoints: true },
      }),
    ]);

    return {
      totalUsers,
      totalPointsEarned: totalPointsEarned._sum.points || 0,
      totalTransactions,
      topEarner,
    };
  }
}
