import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { PointsService, PointsTransactionDto } from './points.service';
import { Role } from '../common/enums';

@Controller('points')
@UseGuards(JwtAuthGuard)
export class PointsController {
  constructor(private readonly pointsService: PointsService) {}

  @Get('settings')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async getSettings() {
    return this.pointsService.getSettings();
  }

  @Patch('settings')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async updateSettings(@Body() updateData: any) {
    return this.pointsService.updateSettings(updateData);
  }

  @Get('my-points')
  async getMyPoints(@Request() req) {
    return this.pointsService.getUserPoints(req.user.id);
  }

  @Get('my-transactions')
  async getMyTransactions(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
  ) {
    return this.pointsService.getUserTransactions(req.user.id, page, limit);
  }

  @Get('user/:userId')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async getUserPoints(@Param('userId') userId: string) {
    return this.pointsService.getUserPoints(userId);
  }

  @Get('user/:userId/transactions')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async getUserTransactions(
    @Param('userId') userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
  ) {
    return this.pointsService.getUserTransactions(userId, page, limit);
  }

  @Post('award')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async awardPoints(
    @Body() body: { userId: string; transaction: PointsTransactionDto },
  ) {
    return this.pointsService.addPoints(body.userId, body.transaction);
  }

  @Get('leaderboard')
  async getLeaderboard(
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return this.pointsService.getLeaderboard(limit);
  }

  @Get('statistics')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async getStatistics() {
    return this.pointsService.getPointsStatistics();
  }
}
