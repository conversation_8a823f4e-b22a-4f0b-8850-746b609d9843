{"name": "cleancity360-frontend", "version": "1.0.0", "description": "React frontend for Cleancity360 admin portal", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "react-leaflet": "^4.2.1", "leaflet": "^1.9.4", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/leaflet": "^1.9.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}