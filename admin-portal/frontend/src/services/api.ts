import axios from 'axios';
import toast from 'react-hot-toast';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else if (error.message) {
      toast.error(error.message);
    }
    
    return Promise.reject(error);
  }
);

// Bins API
export const binsApi = {
  getAll: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/bins', { params }),
  getById: (id: string) => api.get(`/bins/${id}`),
  create: (data: any) => api.post('/bins', data),
  update: (id: string, data: any) => api.patch(`/bins/${id}`, data),
  delete: (id: string) => api.delete(`/bins/${id}`),
  getStatistics: () => api.get('/bins/statistics'),
  updateSensorData: (sensorId: string, data: any) =>
    api.post(`/bins/sensor/${sensorId}/data`, data),
  forceEmpty: (id: string) => api.post(`/bins/${id}/force-empty`),
  maintenance: (id: string, data: any) => api.post(`/bins/${id}/maintenance`, data),
};

// Pickups API
export const pickupsApi = {
  getAll: (params?: { page?: number; limit?: number; status?: string; priority?: string }) =>
    api.get('/pickups', { params }),
  getById: (id: string) => api.get(`/pickups/${id}`),
  create: (data: any) => api.post('/pickups', data),
  update: (id: string, data: any) => api.patch(`/pickups/${id}`, data),
  delete: (id: string) => api.delete(`/pickups/${id}`),
  assign: (id: string, driverId: string) => api.post(`/pickups/${id}/assign`, { driverId }),
  start: (id: string) => api.post(`/pickups/${id}/start`),
  complete: (id: string, data?: any) => api.post(`/pickups/${id}/complete`, data),
  cancel: (id: string, reason?: string) => api.post(`/pickups/${id}/cancel`, { reason }),
};

// Alerts API
export const alertsApi = {
  getAll: (params?: { page?: number; limit?: number; severity?: string; status?: string }) =>
    api.get('/alerts', { params }),
  getById: (id: string) => api.get(`/alerts/${id}`),
  create: (data: any) => api.post('/alerts', data),
  update: (id: string, data: any) => api.patch(`/alerts/${id}`, data),
  delete: (id: string) => api.delete(`/alerts/${id}`),
  resolve: (id: string, notes?: string) => api.post(`/alerts/${id}/resolve`, { notes }),
  acknowledge: (id: string) => api.post(`/alerts/${id}/acknowledge`),
};

// Reports API
export const reportsApi = {
  getAll: (params?: { page?: number; limit?: number; type?: string; status?: string }) =>
    api.get('/reports', { params }),
  getById: (id: string) => api.get(`/reports/${id}`),
  create: (data: any) => api.post('/reports', data),
  update: (id: string, data: any) => api.patch(`/reports/${id}`, data),
  delete: (id: string) => api.delete(`/reports/${id}`),
  resolve: (id: string, notes?: string) => api.post(`/reports/${id}/resolve`, { notes }),
  addComment: (id: string, comment: string) => api.post(`/reports/${id}/comments`, { comment }),
};

// Users API
export const usersApi = {
  getAll: (params?: { page?: number; limit?: number; role?: string; status?: string }) =>
    api.get('/users', { params }),
  getById: (id: string) => api.get(`/users/${id}`),
  create: (data: any) => api.post('/users', data),
  update: (id: string, data: any) => api.patch(`/users/${id}`, data),
  delete: (id: string) => api.delete(`/users/${id}`),
  activate: (id: string) => api.post(`/users/${id}/activate`),
  deactivate: (id: string) => api.post(`/users/${id}/deactivate`),
  resetPassword: (id: string) => api.post(`/users/${id}/reset-password`),
};

// Dashboard API
export const dashboardApi = {
  getStats: () => api.get('/dashboard/stats'),
  getRecentActivity: () => api.get('/dashboard/recent-activity'),
  getAnalytics: (period?: string) => api.get('/dashboard/analytics', { params: { period } }),
};

export default api;
