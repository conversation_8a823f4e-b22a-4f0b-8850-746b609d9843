import React, { useState, useEffect, useRef } from 'react';
import {
  TrashIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  UsersIcon,
  BellIcon,
  MapPinIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { api } from '../services/api';

interface DashboardStats {
  totalBins: number;
  activeBins: number;
  fullBins: number;
  overflowBins: number;
  activeAlerts: number;
  criticalAlerts: number;
  pendingPickups: number;
  completedPickups: number;
  activeUsers: number;
  totalReports: number;
  resolvedReports: number;
}

interface RecentActivity {
  id: string;
  type: 'alert' | 'pickup' | 'report' | 'user';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

interface Alert {
  id: string;
  type: string;
  severity: string;
  message: string;
  binId?: string;
  location?: string;
  createdAt: string;
}

const NewDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [lastAlertCount, setLastAlertCount] = useState(0);

  // Sound alert for critical notifications
  const playAlertSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(console.error);
    }
  };

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const [statsResponse, alertsResponse] = await Promise.all([
        api.get('/dashboard/stats'),
        api.get('/alerts?limit=10&severity=critical,high')
      ]);

      const newStats = statsResponse.data;
      const newAlerts = alertsResponse.data.alerts || alertsResponse.data;

      setStats(newStats);
      setAlerts(newAlerts);

      // Play sound if new critical alerts
      if (newAlerts.length > lastAlertCount) {
        const criticalAlerts = newAlerts.filter((alert: Alert) => alert.severity === 'critical');
        if (criticalAlerts.length > 0) {
          playAlertSound();
        }
      }
      setLastAlertCount(newAlerts.length);

      // Generate recent activity from alerts and other data
      const activity: RecentActivity[] = newAlerts.slice(0, 5).map((alert: Alert) => ({
        id: alert.id,
        type: 'alert' as const,
        message: `${alert.type}: ${alert.message}`,
        timestamp: alert.createdAt,
        severity: alert.severity as any,
      }));

      setRecentActivity(activity);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Fallback to mock data
      setStats({
        totalBins: 24,
        activeBins: 20,
        fullBins: 6,
        overflowBins: 2,
        activeAlerts: 8,
        criticalAlerts: 2,
        pendingPickups: 12,
        completedPickups: 45,
        activeUsers: 156,
        totalReports: 23,
        resolvedReports: 18,
      });

      // Mock alerts for demo
      setAlerts([
        {
          id: '1',
          type: 'BIN_OVERFLOW',
          severity: 'critical',
          message: 'Bin at Central Market is overflowing',
          location: 'Central Market, Douala',
          createdAt: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'SENSOR_OFFLINE',
          severity: 'high',
          message: 'Sensor offline at University Campus',
          location: 'University Campus, Douala',
          createdAt: new Date(Date.now() - 300000).toISOString(),
        },
      ]);

      setRecentActivity([
        {
          id: '1',
          type: 'alert',
          message: 'BIN_OVERFLOW: Bin at Central Market is overflowing',
          timestamp: new Date().toISOString(),
          severity: 'critical',
        },
        {
          id: '2',
          type: 'pickup',
          message: 'Pickup completed at Residential Area',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          severity: 'low',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    // Refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'alert': return ExclamationTriangleIcon;
      case 'pickup': return TruckIcon;
      case 'report': return ChartBarIcon;
      default: return BellIcon;
    }
  };

  return (
    <div className="space-y-8">
      {/* Hidden audio element for alerts */}
      <audio ref={audioRef} preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav" />
      </audio>

      {/* Header with Cameroon Flag */}
      <div className="bg-gradient-to-r from-green-600 via-red-500 to-yellow-500 rounded-lg p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Clean365 Admin Dashboard</h1>
            <p className="mt-2 text-green-100">
              République du Cameroun - Waste Management System
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-12 bg-white rounded shadow-lg flex overflow-hidden">
              <div className="w-1/3 bg-green-600"></div>
              <div className="w-1/3 bg-red-500"></div>
              <div className="w-1/3 bg-yellow-500"></div>
            </div>
            <div className="text-right">
              <div className="text-sm opacity-90">Last Updated</div>
              <div className="font-semibold">{new Date().toLocaleTimeString()}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total Bins */}
        <div className="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrashIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Bins</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stats?.totalBins || 0}</div>
                    <div className="ml-2 text-sm text-green-600">
                      {stats?.activeBins || 0} active
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Critical Alerts */}
        <div className="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Critical Alerts</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stats?.criticalAlerts || 0}</div>
                    <div className="ml-2 text-sm text-gray-600">
                      {stats?.activeAlerts || 0} total
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Pickups */}
        <div className="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-yellow-500 hover:shadow-xl transition-shadow">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TruckIcon className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending Pickups</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stats?.pendingPickups || 0}</div>
                    <div className="ml-2 text-sm text-green-600">
                      {stats?.completedPickups || 0} completed
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Active Users */}
        <div className="bg-white overflow-hidden shadow-lg rounded-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stats?.activeUsers || 0}</div>
                    <div className="ml-2 text-sm text-blue-600">
                      online
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts and Activity Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Critical Alerts */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-red-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                Critical Alerts
              </h3>
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {alerts.filter(alert => alert.severity === 'critical').length} active
              </span>
            </div>
          </div>
          <div className="max-h-80 overflow-y-auto">
            {alerts.length > 0 ? (
              alerts.map((alert) => (
                <div key={alert.id} className="px-6 py-4 border-b border-gray-100 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                        <span className="ml-2 text-sm text-gray-500">
                          {new Date(alert.createdAt).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="mt-1 text-sm font-medium text-gray-900">{alert.type}</p>
                      <p className="text-sm text-gray-600">{alert.message}</p>
                      {alert.location && (
                        <p className="text-xs text-gray-500 mt-1 flex items-center">
                          <MapPinIcon className="h-3 w-3 mr-1" />
                          {alert.location}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-8 text-center">
                <CheckCircleIcon className="h-12 w-12 text-green-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No critical alerts at this time</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-blue-50">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ClockIcon className="h-5 w-5 text-blue-500 mr-2" />
              Recent Activity
            </h3>
          </div>
          <div className="max-h-80 overflow-y-auto">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => {
                const IconComponent = getActivityIcon(activity.type);
                return (
                  <div key={activity.id} className="px-6 py-4 border-b border-gray-100 hover:bg-gray-50">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <IconComponent className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="ml-3 flex-1">
                        <p className="text-sm text-gray-900">{activity.message}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                      {activity.severity && (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(activity.severity)}`}>
                          {activity.severity}
                        </span>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="px-6 py-8 text-center">
                <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No recent activity</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* System Performance & Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Collection Efficiency */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-green-50">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-5 w-5 text-green-500 mr-2" />
              Collection Efficiency
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">This Week</span>
                <span className="text-2xl font-bold text-green-600">94%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '94%' }}></div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">Completed</div>
                  <div className="font-semibold">{stats?.completedPickups || 0}</div>
                </div>
                <div>
                  <div className="text-gray-500">Pending</div>
                  <div className="font-semibold">{stats?.pendingPickups || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bin Status Overview */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-blue-50">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <TrashIcon className="h-5 w-5 text-blue-500 mr-2" />
              Bin Status Overview
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Normal</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  {stats?.activeBins || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Full</span>
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                  {stats?.fullBins || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Overflow</span>
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                  {stats?.overflowBins || 0}
                </span>
              </div>
              <div className="pt-2 border-t">
                <div className="text-xs text-gray-500">Total Capacity Utilization</div>
                <div className="text-lg font-bold text-gray-900">
                  {stats?.totalBins ? Math.round(((stats.fullBins + stats.overflowBins) / stats.totalBins) * 100) : 0}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-purple-50">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <BellIcon className="h-5 w-5 text-purple-500 mr-2" />
              System Health
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">API Status</span>
                <span className="flex items-center text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Online
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">IoT Sensors</span>
                <span className="flex items-center text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  {stats?.totalBins || 0}/12 Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Database</span>
                <span className="flex items-center text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Connected
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Sync</span>
                <span className="text-sm text-gray-500">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors">
              <TruckIcon className="h-5 w-5 mr-2" />
              Schedule Pickup
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
              <TrashIcon className="h-5 w-5 mr-2" />
              Add New Bin
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 transition-colors">
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
              Create Alert
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
              <ChartBarIcon className="h-5 w-5 mr-2" />
              View Reports
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
              <UsersIcon className="h-5 w-5 mr-2" />
              Manage Users
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 transition-colors">
              <MapPinIcon className="h-5 w-5 mr-2" />
              View Map
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewDashboardPage;
