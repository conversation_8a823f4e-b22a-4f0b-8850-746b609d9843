import React, { useState, useEffect } from 'react';
import {
  ExclamationTriangleIcon,
  BellIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  MapPinIcon,
  TrashIcon,
  Battery0Icon,
  FireIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { alertsApi } from '../services/api';
import toast from 'react-hot-toast';

interface Alert {
  id: string;
  binId?: string;
  type: string;
  severity: string;
  title: string;
  message: string;
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  createdAt: string;
  bin?: {
    name: string;
    location: string;
  };
}

const AlertsPage: React.FC = () => {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState('ALL');
  const [statusFilter, setStatusFilter] = useState('ALL');

  useEffect(() => {
    fetchAlerts();
  }, []);

  const fetchAlerts = async () => {
    try {
      const response = await alertsApi.getAll();
      setAlerts(response.data.alerts || response.data);
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
      toast.error('Failed to fetch alerts');
      // Mock data fallback
      setAlerts([
        {
          id: '1',
          binId: 'bin1',
          type: 'BIN_OVERFLOW',
          severity: 'CRITICAL',
          title: 'Critical Overflow - Central Market',
          message: 'Bin at Central Market is overflowing dangerously. Immediate intervention required.',
          isResolved: false,
          createdAt: new Date().toISOString(),
          bin: {
            name: 'Central Market Bin 1',
            location: 'Central Market, Douala',
          },
        },
        {
          id: '2',
          binId: 'bin2',
          type: 'SENSOR_LOW_BATTERY',
          severity: 'HIGH',
          title: 'Low Battery - Sensor',
          message: 'Sensor SENSOR_004 has critical battery level (15%).',
          isResolved: false,
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          bin: {
            name: 'Bonanjo Residential Bin 1',
            location: 'Quartier Bonanjo, Douala',
          },
        },
        {
          id: '3',
          binId: 'bin3',
          type: 'HIGH_TEMPERATURE',
          severity: 'MEDIUM',
          title: 'High Temperature - Deido Market',
          message: 'Abnormally high temperature detected (30.1°C).',
          isResolved: false,
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          bin: {
            name: 'Deido Market Bin 1',
            location: 'Deido Market, Douala',
          },
        },
        {
          id: '4',
          binId: 'bin4',
          type: 'BIN_FULL',
          severity: 'HIGH',
          title: 'Bin Full - Central Market',
          message: 'Full bin requiring collection.',
          isResolved: true,
          resolvedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          bin: {
            name: 'Central Market Bin 2',
            location: 'Central Market, Douala',
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Action handlers
  const handleViewAlert = async (alertId: string) => {
    try {
      const response = await alertsApi.getById(alertId);
      console.log('Alert details:', response.data);
      toast.success('Alert details loaded');
    } catch (error) {
      toast.error('Failed to load alert details');
    }
  };

  const handleResolveAlert = async (alertId: string) => {
    try {
      await alertsApi.resolve(alertId, 'Resolved by admin');
      toast.success('Alert resolved successfully');
      fetchAlerts();
    } catch (error) {
      toast.error('Failed to resolve alert');
    }
  };

  const handleDeleteAlert = async (alertId: string) => {
    if (window.confirm('Are you sure you want to delete this alert?')) {
      try {
        await alertsApi.delete(alertId);
        toast.success('Alert deleted successfully');
        fetchAlerts();
      } catch (error) {
        toast.error('Failed to delete alert');
      }
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return ExclamationTriangleIcon;
      case 'HIGH': return ExclamationTriangleIcon;
      case 'MEDIUM': return BellIcon;
      case 'LOW': return BellIcon;
      default: return BellIcon;
    }
  };

  const getAlertTypeIcon = (type: string) => {
    switch (type) {
      case 'BIN_OVERFLOW': return TrashIcon;
      case 'BIN_FULL': return TrashIcon;
      case 'SENSOR_LOW_BATTERY': return Battery0Icon;
      case 'HIGH_TEMPERATURE': return FireIcon;
      case 'SENSOR_OFFLINE': return XCircleIcon;
      default: return BellIcon;
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.bin?.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = severityFilter === 'ALL' || alert.severity === severityFilter;
    const matchesStatus = statusFilter === 'ALL' ||
                         (statusFilter === 'ACTIVE' && !alert.isResolved) ||
                         (statusFilter === 'RESOLVED' && alert.isResolved);
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Alerts Management</h1>
            <p className="mt-2 text-sm text-gray-600">
              Monitor and respond to system alerts and notifications
            </p>
          </div>
          <button
            onClick={() => toast('Create Alert functionality coming soon', { icon: 'ℹ️' })}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Alert</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by title, message, or location..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
            >
              <option value="ALL">All Severity</option>
              <option value="CRITICAL">Critical</option>
              <option value="HIGH">High</option>
              <option value="MEDIUM">Medium</option>
              <option value="LOW">Low</option>
            </select>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Status</option>
              <option value="ACTIVE">Active</option>
              <option value="RESOLVED">Resolved</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-gray-900">
                {alerts.filter(alert => alert.severity === 'CRITICAL' && !alert.isResolved).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <ExclamationTriangleIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {alerts.filter(alert => alert.severity === 'HIGH' && !alert.isResolved).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <BellIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900">
                {alerts.filter(alert => !alert.isResolved).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-gray-900">
                {alerts.filter(alert => alert.isResolved).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            System Alerts ({filteredAlerts.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredAlerts.map((alert) => {
            const SeverityIcon = getSeverityIcon(alert.severity);
            const TypeIcon = getAlertTypeIcon(alert.type);

            return (
              <div key={alert.id} className={`p-6 hover:bg-gray-50 ${alert.isResolved ? 'opacity-75' : ''}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg border ${getSeverityColor(alert.severity)}`}>
                      <TypeIcon className="h-5 w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{alert.title}</h4>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          <SeverityIcon className="h-3 w-3 mr-1" />
                          {alert.severity}
                        </span>
                        {alert.isResolved && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                            Resolved
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{alert.message}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        {alert.bin && (
                          <div className="flex items-center">
                            <MapPinIcon className="h-4 w-4 mr-1" />
                            <span>{alert.bin.location}</span>
                          </div>
                        )}
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          <span>{new Date(alert.createdAt).toLocaleString()}</span>
                        </div>
                        {alert.resolvedAt && (
                          <div className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 mr-1" />
                            <span>Resolved {new Date(alert.resolvedAt).toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                      onClick={() => handleViewAlert(alert.id)}
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    {!alert.isResolved && (
                      <button
                        className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                        title="Mark as Resolved"
                        onClick={() => handleResolveAlert(alert.id)}
                      >
                        <CheckIcon className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Alert"
                      onClick={() => handleDeleteAlert(alert.id)}
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredAlerts.length === 0 && (
          <div className="text-center py-12">
            <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No alerts match your search criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertsPage;
