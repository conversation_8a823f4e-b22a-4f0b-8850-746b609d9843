import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  TruckIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  UsersIcon,
  ClockIcon,
  CheckCircleIcon,
  MapPinIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ArrowPathIcon,
  EyeIcon,
  SignalIcon,
  ScaleIcon,
  LeafIcon,
} from '@heroicons/react/24/outline';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { dashboardApi, binsApi } from '../services/api';
import toast from 'react-hot-toast';

interface Statistics {
  totalBins: number;
  activeBins: number;
  fullBins: number;
  overflowBins: number;
  totalPickups: number;
  completedPickups: number;
  pendingPickups: number;
  totalAlerts: number;
  criticalAlerts: number;
  totalUsers: number;
  activeUsers: number;
  collectionEfficiency: number;
  averageResponseTime: number;
  wasteCollected: number;
  carbonSaved: number;
}

interface Analytics {
  collectionEfficiency: number;
  averageResponseTime: number;
  wasteCollected: number;
  carbonSaved: number;
  trends: {
    pickups: Array<{ date: string; value: number }>;
    alerts: Array<{ date: string; value: number }>;
    efficiency: Array<{ date: string; value: number }>;
  };
}

interface ChartDataPoint {
  date: string;
  pickups: number;
  alerts: number;
  efficiency: number;
  waste: number;
}

// Chart color schemes
const COLORS = {
  primary: '#3B82F6',
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  purple: '#8B5CF6',
  indigo: '#6366F1',
  pink: '#EC4899',
  teal: '#14B8A6',
};

const PIE_COLORS = [COLORS.primary, COLORS.success, COLORS.warning, COLORS.danger, COLORS.purple];

interface AnalyticsData {
  period: string;
  pickups: number;
  alerts: number;
  efficiency: number;
}

const StatisticsPage: React.FC = () => {
  const [stats, setStats] = useState<Statistics | null>(null);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchStatistics();
    fetchAnalytics();
  }, [selectedPeriod]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!refreshing) {
        handleRefresh();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshing]);

  const fetchStatistics = async () => {
    try {
      const [dashboardResponse, binsResponse] = await Promise.all([
        dashboardApi.getStats(),
        binsApi.getStatistics(),
      ]);
      
      const dashboardStats = dashboardResponse.data;
      const binsStats = binsResponse.data;
      
      setStats({
        totalBins: binsStats.total || 12,
        activeBins: binsStats.active || 10,
        fullBins: binsStats.full || 3,
        overflowBins: binsStats.overflow || 1,
        totalPickups: dashboardStats.totalPickups || 45,
        completedPickups: dashboardStats.completedPickups || 38,
        pendingPickups: dashboardStats.pendingPickups || 7,
        totalAlerts: dashboardStats.totalAlerts || 15,
        criticalAlerts: dashboardStats.criticalAlerts || 3,
        totalUsers: dashboardStats.totalUsers || 156,
        activeUsers: dashboardStats.activeUsers || 142,
        collectionEfficiency: 94.2,
        averageResponseTime: 2.3,
        wasteCollected: 1247.5,
        carbonSaved: 89.3,
      });
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      toast.error('Failed to load statistics');
      // Fallback data
      setStats({
        totalBins: 12,
        activeBins: 10,
        fullBins: 3,
        overflowBins: 1,
        totalPickups: 45,
        completedPickups: 38,
        pendingPickups: 7,
        totalAlerts: 15,
        criticalAlerts: 3,
        totalUsers: 156,
        activeUsers: 142,
        collectionEfficiency: 94.2,
        averageResponseTime: 2.3,
        wasteCollected: 1247.5,
        carbonSaved: 89.3,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    setAnalyticsLoading(true);
    try {
      const response = await dashboardApi.getAnalytics(selectedPeriod);
      const analyticsData = response.data;

      setAnalytics(analyticsData);

      // Transform trends data for charts
      if (analyticsData.trends) {
        const transformedData: ChartDataPoint[] = analyticsData.trends.pickups.map((item, index) => ({
          date: item.date,
          pickups: item.value,
          alerts: analyticsData.trends.alerts[index]?.value || 0,
          efficiency: analyticsData.trends.efficiency[index]?.value || 0,
          waste: Math.floor(Math.random() * 50) + 10, // Mock waste data
        }));
        setChartData(transformedData);
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      toast.error('Failed to load analytics data');

      // Fallback chart data
      const fallbackData: ChartDataPoint[] = Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        pickups: Math.floor(Math.random() * 20) + 5,
        alerts: Math.floor(Math.random() * 10) + 1,
        efficiency: Math.floor(Math.random() * 20) + 80,
        waste: Math.floor(Math.random() * 50) + 10,
      }));
      setChartData(fallbackData);

      setAnalytics({
        collectionEfficiency: 94.2,
        averageResponseTime: 2.3,
        wasteCollected: 1247.5,
        carbonSaved: 89.3,
        trends: {
          pickups: fallbackData.map(d => ({ date: d.date, value: d.pickups })),
          alerts: fallbackData.map(d => ({ date: d.date, value: d.alerts })),
          efficiency: fallbackData.map(d => ({ date: d.date, value: d.efficiency })),
        },
      });
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchStatistics(), fetchAnalytics()]);
      toast.success('Data refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No statistics available</h3>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Statistics & Analytics</h1>
            <p className="mt-2 text-sm text-gray-600">
              Comprehensive overview of waste management system performance
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={selectedPeriod}
                onChange={(e) => handlePeriodChange(e.target.value)}
              >
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 3 Months</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Collection Efficiency</p>
              <p className="text-3xl font-bold text-green-600">{stats.collectionEfficiency}%</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+2.3% from last week</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
              <p className="text-3xl font-bold text-blue-600">{stats.averageResponseTime}h</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingDownIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">-0.5h improvement</span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <ClockIcon className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Waste Collected</p>
              <p className="text-3xl font-bold text-purple-600">{stats.wasteCollected}kg</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+15% this month</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrashIcon className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-emerald-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Carbon Saved</p>
              <p className="text-3xl font-bold text-emerald-600">{stats.carbonSaved}kg</p>
              <div className="flex items-center mt-2">
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+8% this month</span>
              </div>
            </div>
            <div className="p-3 bg-emerald-100 rounded-full">
              <MapPinIcon className="h-8 w-8 text-emerald-600" />
            </div>
          </div>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <TrashIcon className="h-5 w-5 text-gray-500 mr-2" />
            Bins Status
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Bins</span>
              <span className="font-semibold">{stats.totalBins}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active</span>
              <span className="font-semibold text-green-600">{stats.activeBins}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Full</span>
              <span className="font-semibold text-yellow-600">{stats.fullBins}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Overflow</span>
              <span className="font-semibold text-red-600">{stats.overflowBins}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <TruckIcon className="h-5 w-5 text-gray-500 mr-2" />
            Pickups Overview
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Requests</span>
              <span className="font-semibold">{stats.totalPickups}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Completed</span>
              <span className="font-semibold text-green-600">{stats.completedPickups}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Pending</span>
              <span className="font-semibold text-yellow-600">{stats.pendingPickups}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Success Rate</span>
              <span className="font-semibold text-blue-600">
                {Math.round((stats.completedPickups / stats.totalPickups) * 100)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-500 mr-2" />
            Alerts & Users
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Alerts</span>
              <span className="font-semibold">{stats.totalAlerts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Critical</span>
              <span className="font-semibold text-red-600">{stats.criticalAlerts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Users</span>
              <span className="font-semibold">{stats.totalUsers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Users</span>
              <span className="font-semibold text-green-600">{stats.activeUsers}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pickups Trend Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <TruckIcon className="h-5 w-5 text-gray-500 mr-2" />
              Pickups Trend
            </h3>
            {analyticsLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
            )}
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis stroke="#6b7280" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="pickups"
                  stroke={COLORS.primary}
                  strokeWidth={3}
                  dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: COLORS.primary, strokeWidth: 2 }}
                  name="Daily Pickups"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Alerts Trend Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-gray-500 mr-2" />
              Alerts Trend
            </h3>
            {analyticsLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
            )}
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis stroke="#6b7280" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="alerts"
                  stroke={COLORS.warning}
                  fill={COLORS.warning}
                  fillOpacity={0.3}
                  strokeWidth={2}
                  name="Daily Alerts"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Collection Efficiency Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-gray-500 mr-2" />
              Collection Efficiency
            </h3>
            {analyticsLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
            )}
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis
                  stroke="#6b7280"
                  fontSize={12}
                  domain={[70, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value) => [`${value}%`, 'Efficiency']}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="efficiency"
                  stroke={COLORS.success}
                  strokeWidth={3}
                  dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: COLORS.success, strokeWidth: 2 }}
                  name="Efficiency %"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Waste Collection Chart */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <ScaleIcon className="h-5 w-5 text-gray-500 mr-2" />
              Waste Collection (kg)
            </h3>
            {analyticsLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
            )}
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => `${value}kg`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value) => [`${value}kg`, 'Waste Collected']}
                />
                <Legend />
                <Bar
                  dataKey="waste"
                  fill={COLORS.indigo}
                  radius={[4, 4, 0, 0]}
                  name="Daily Collection"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* System Overview Pie Chart */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <ChartBarIcon className="h-5 w-5 text-gray-500 mr-2" />
            System Overview
          </h3>
          {analyticsLoading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
          )}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Bins Status Pie Chart */}
          <div>
            <h4 className="text-md font-medium text-gray-700 mb-4 text-center">Bins Status Distribution</h4>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Active', value: stats.activeBins, color: COLORS.success },
                      { name: 'Full', value: stats.fullBins, color: COLORS.warning },
                      { name: 'Overflow', value: stats.overflowBins, color: COLORS.danger },
                      { name: 'Maintenance', value: stats.totalBins - stats.activeBins - stats.fullBins - stats.overflowBins, color: COLORS.purple },
                    ]}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {[
                      { name: 'Active', value: stats.activeBins, color: COLORS.success },
                      { name: 'Full', value: stats.fullBins, color: COLORS.warning },
                      { name: 'Overflow', value: stats.overflowBins, color: COLORS.danger },
                      { name: 'Maintenance', value: stats.totalBins - stats.activeBins - stats.fullBins - stats.overflowBins, color: COLORS.purple },
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Pickups Status Pie Chart */}
          <div>
            <h4 className="text-md font-medium text-gray-700 mb-4 text-center">Pickups Status Distribution</h4>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Completed', value: stats.completedPickups, color: COLORS.success },
                      { name: 'Pending', value: stats.pendingPickups, color: COLORS.warning },
                      { name: 'In Progress', value: Math.max(0, stats.totalPickups - stats.completedPickups - stats.pendingPickups), color: COLORS.primary },
                    ]}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {[
                      { name: 'Completed', value: stats.completedPickups, color: COLORS.success },
                      { name: 'Pending', value: stats.pendingPickups, color: COLORS.warning },
                      { name: 'In Progress', value: Math.max(0, stats.totalPickups - stats.completedPickups - stats.pendingPickups), color: COLORS.primary },
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsPage;
