import React, { useState, useEffect, useRef } from 'react';
import {
  TrashIcon,
  ExclamationTriangleIcon,
  TruckIcon,
  UsersIcon,
  BellIcon,
  MapPinIcon,
  ChartBarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { api } from '../services/api';

interface DashboardStats {
  totalBins: number;
  activeBins: number;
  fullBins: number;
  overflowBins: number;
  activeAlerts: number;
  criticalAlerts: number;
  pendingPickups: number;
  completedPickups: number;
  activeUsers: number;
  totalReports: number;
  resolvedReports: number;
}

interface RecentActivity {
  id: string;
  type: 'alert' | 'pickup' | 'report' | 'user';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

interface Alert {
  id: string;
  type: string;
  severity: string;
  message: string;
  binId?: string;
  location?: string;
  createdAt: string;
}

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [lastAlertCount, setLastAlertCount] = useState(0);

  // Sound alert for critical notifications
  const playAlertSound = () => {
    if (audioRef.current) {
      audioRef.current.play().catch(console.error);
    }
  };

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const [statsResponse, alertsResponse] = await Promise.all([
        api.get('/dashboard/stats'),
        api.get('/alerts?limit=10&severity=critical,high')
      ]);

      const newStats = statsResponse.data;
      const newAlerts = alertsResponse.data.alerts || alertsResponse.data;

      setStats(newStats);
      setAlerts(newAlerts);

      // Play sound if new critical alerts
      if (newAlerts.length > lastAlertCount) {
        const criticalAlerts = newAlerts.filter((alert: Alert) => alert.severity === 'critical');
        if (criticalAlerts.length > 0) {
          playAlertSound();
        }
      }
      setLastAlertCount(newAlerts.length);

      // Generate recent activity from alerts and other data
      const activity: RecentActivity[] = newAlerts.slice(0, 5).map((alert: Alert) => ({
        id: alert.id,
        type: 'alert' as const,
        message: `${alert.type}: ${alert.message}`,
        timestamp: alert.createdAt,
        severity: alert.severity as any,
      }));

      setRecentActivity(activity);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Fallback to mock data
      setStats({
        totalBins: 24,
        activeBins: 20,
        fullBins: 6,
        overflowBins: 2,
        activeAlerts: 8,
        criticalAlerts: 2,
        pendingPickups: 12,
        completedPickups: 45,
        activeUsers: 156,
        totalReports: 23,
        resolvedReports: 18,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    // Refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hidden audio element for alerts */}
      <audio ref={audioRef} preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav" />
      </audio>

      {/* Header with Cameroon Flag */}
      <div className="bg-gradient-to-r from-green-600 via-red-500 to-yellow-500 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Clean365 Admin Dashboard</h1>
            <p className="mt-2 text-green-100">
              République du Cameroun - Waste Management System
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-12 bg-white rounded shadow-lg flex">
              <div className="w-1/3 bg-green-600 rounded-l"></div>
              <div className="w-1/3 bg-red-500"></div>
              <div className="w-1/3 bg-yellow-500 rounded-r"></div>
            </div>
            <div className="text-right">
              <div className="text-sm opacity-90">Last Updated</div>
              <div className="font-semibold">{new Date().toLocaleTimeString()}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((stat) => (
          <div key={stat.name} className="card p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </div>
                    <div
                      className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'increase'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}
                    >
                      {stat.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Alerts
            </h3>
            <div className="space-y-3">
              {[1, 2, 3].map((item) => (
                <div key={item} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 truncate">
                      Bin overflow detected at Bastos Market
                    </p>
                    <p className="text-sm text-gray-500">2 minutes ago</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="card">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Pickups
            </h3>
            <div className="space-y-3">
              {[1, 2, 3].map((item) => (
                <div key={item} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 truncate">
                      Pickup completed at Mvog-Ada
                    </p>
                    <p className="text-sm text-gray-500">15 minutes ago</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
