import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  MapPinIcon,
  ClockIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  TrashIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { reportsApi } from '../services/api';
import toast from 'react-hot-toast';

interface Report {
  id: string;
  userId: string;
  binId?: string;
  type: string;
  title: string;
  description: string;
  location: string;
  latitude: number;
  longitude: number;
  status: string;
  createdAt: string;
  user?: {
    name: string;
    phone: string;
  };
  bin?: {
    name: string;
  };
}

const ReportsPage: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [statusFilter, setStatusFilter] = useState('ALL');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      const response = await reportsApi.getAll();
      setReports(response.data.reports || response.data);
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      toast.error('Failed to fetch reports');
      // Mock data fallback
      setReports([
        {
          id: '1',
          userId: 'user1',
          binId: 'bin1',
          type: 'BIN_OVERFLOW',
          title: 'Overflowing bin at market',
          description: 'The bin at Central Market has been overflowing for several days. Waste is spilling onto the road and creating hygiene problems.',
          location: 'Central Market, Douala',
          latitude: 4.0511,
          longitude: 9.7679,
          status: 'PENDING',
          createdAt: new Date().toISOString(),
          user: {
            name: 'Paul Atangana',
            phone: '+237123456793',
          },
          bin: {
            name: 'Central Market Bin 1',
          },
        },
        {
          id: '2',
          userId: 'user2',
          binId: 'bin2',
          type: 'BIN_DAMAGE',
          title: 'Damaged bin',
          description: 'The bin in Bonanjo is broken and does not close properly. Animals can access the waste.',
          location: 'Quartier Bonanjo, Douala',
          latitude: 4.0411,
          longitude: 9.7579,
          status: 'IN_PROGRESS',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Grace Biya',
            phone: '+237123456794',
          },
          bin: {
            name: 'Bonanjo Residential Bin 1',
          },
        },
        {
          id: '3',
          userId: 'user3',
          type: 'ILLEGAL_DUMPING',
          title: 'Illegal waste dumping',
          description: 'Waste has been illegally dumped near the university. There are electronic waste and construction materials.',
          location: 'Near University of Douala',
          latitude: 4.0611,
          longitude: 9.7779,
          status: 'RESOLVED',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Emmanuel Fouda',
            phone: '+237123456795',
          },
        },
        {
          id: '4',
          userId: 'user4',
          binId: 'bin3',
          type: 'MISSED_COLLECTION',
          title: 'Missed collection',
          description: 'The scheduled collection did not take place this week in our neighborhood. The bins are overflowing.',
          location: 'Quartier Mvog-Mbi, Yaoundé',
          latitude: 3.8380,
          longitude: 11.4921,
          status: 'PENDING',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Aminata Sow',
            phone: '+237123456796',
          },
          bin: {
            name: 'Mvog-Mbi Bin 1',
          },
        },
        {
          id: '5',
          userId: 'user5',
          type: 'OTHER',
          title: 'Odor problem',
          description: 'Strong foul odors coming from the collection area. This affects the quality of life of residents.',
          location: 'Quartier Essos, Yaoundé',
          latitude: 3.8780,
          longitude: 11.5321,
          status: 'RESOLVED',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Joseph Nkomo',
            phone: '+237123456797',
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Action handlers
  const handleViewReport = async (reportId: string) => {
    try {
      const response = await reportsApi.getById(reportId);
      console.log('Report details:', response.data);
      toast.success('Report details loaded');
    } catch (error) {
      toast.error('Failed to load report details');
    }
  };

  const handleAddComment = async (reportId: string) => {
    const comment = prompt('Enter your comment:');
    if (comment) {
      try {
        await reportsApi.addComment(reportId, comment);
        toast.success('Comment added successfully');
        fetchReports();
      } catch (error) {
        toast.error('Failed to add comment');
      }
    }
  };

  const handleResolveReport = async (reportId: string) => {
    try {
      await reportsApi.resolve(reportId, 'Resolved by admin');
      toast.success('Report resolved successfully');
      fetchReports();
    } catch (error) {
      toast.error('Failed to resolve report');
    }
  };

  const handleDeleteReport = async (reportId: string) => {
    if (window.confirm('Are you sure you want to delete this report?')) {
      try {
        await reportsApi.delete(reportId);
        toast.success('Report deleted successfully');
        fetchReports();
      } catch (error) {
        toast.error('Failed to delete report');
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'RESOLVED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'BIN_OVERFLOW': return TrashIcon;
      case 'BIN_DAMAGE': return ExclamationTriangleIcon;
      case 'BIN_FULL': return TrashIcon;
      case 'MISSED_COLLECTION': return ClockIcon;
      case 'ILLEGAL_DUMPING': return XCircleIcon;
      case 'OTHER': return DocumentTextIcon;
      default: return DocumentTextIcon;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'BIN_OVERFLOW': return 'bg-red-100 text-red-800';
      case 'BIN_DAMAGE': return 'bg-orange-100 text-orange-800';
      case 'BIN_FULL': return 'bg-yellow-100 text-yellow-800';
      case 'MISSED_COLLECTION': return 'bg-purple-100 text-purple-800';
      case 'ILLEGAL_DUMPING': return 'bg-red-100 text-red-800';
      case 'OTHER': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.user?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'ALL' || report.type === typeFilter;
    const matchesStatus = statusFilter === 'ALL' || report.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Citizen Reports Management</h1>
            <p className="mt-2 text-sm text-gray-600">
              Review and manage citizen-submitted waste management reports
            </p>
          </div>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <PlusIcon className="h-5 w-5" />
            <span>Create Report</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by title, description, location, or reporter..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">All Types</option>
              <option value="BIN_OVERFLOW">Bin Overflow</option>
              <option value="BIN_DAMAGE">Bin Damage</option>
              <option value="BIN_FULL">Bin Full</option>
              <option value="MISSED_COLLECTION">Missed Collection</option>
              <option value="ILLEGAL_DUMPING">Illegal Dumping</option>
              <option value="OTHER">Other</option>
            </select>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="RESOLVED">Resolved</option>
              <option value="REJECTED">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">
                {reports.filter(report => report.status === 'PENDING').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DocumentTextIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">
                {reports.filter(report => report.status === 'IN_PROGRESS').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-gray-900">
                {reports.filter(report => report.status === 'RESOLVED').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-gray-100 rounded-lg">
              <DocumentTextIcon className="h-6 w-6 text-gray-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Reports</p>
              <p className="text-2xl font-bold text-gray-900">{reports.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Reports List */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Citizen Reports ({filteredReports.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredReports.map((report) => {
            const TypeIcon = getTypeIcon(report.type);

            return (
              <div key={report.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${getTypeColor(report.type)}`}>
                      <TypeIcon className="h-5 w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{report.title}</h4>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(report.type)}`}>
                          {report.type.replace('_', ' ')}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                          {report.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{report.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <UserIcon className="h-4 w-4 mr-1" />
                          <span>{report.user?.name}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPinIcon className="h-4 w-4 mr-1" />
                          <span className="max-w-xs truncate">{report.location}</span>
                        </div>
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          <span>{new Date(report.createdAt).toLocaleString()}</span>
                        </div>
                        {report.bin && (
                          <div className="flex items-center">
                            <TrashIcon className="h-4 w-4 mr-1" />
                            <span>{report.bin.name}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                      onClick={() => handleViewReport(report.id)}
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button
                      className="p-2 text-purple-600 hover:text-purple-900 hover:bg-purple-50 rounded-lg transition-colors"
                      title="Add Comment"
                      onClick={() => handleAddComment(report.id)}
                    >
                      <ChatBubbleLeftRightIcon className="h-4 w-4" />
                    </button>
                    {report.status !== 'RESOLVED' && (
                      <button
                        className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                        title="Mark as Resolved"
                        onClick={() => handleResolveReport(report.id)}
                      >
                        <CheckIcon className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Report"
                      onClick={() => handleDeleteReport(report.id)}
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredReports.length === 0 && (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No reports found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No reports match your search criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsPage;
