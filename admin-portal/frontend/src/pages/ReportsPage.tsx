import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  MapPinIcon,
  ClockIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  TrashIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { api } from '../services/api';

interface Report {
  id: string;
  userId: string;
  binId?: string;
  type: string;
  title: string;
  description: string;
  location: string;
  latitude: number;
  longitude: number;
  status: string;
  createdAt: string;
  user?: {
    name: string;
    phone: string;
  };
  bin?: {
    name: string;
  };
}

const ReportsPage: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [statusFilter, setStatusFilter] = useState('ALL');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      const response = await api.get('/reports');
      setReports(response.data.reports || response.data);
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      // Mock data fallback
      setReports([
        {
          id: '1',
          userId: 'user1',
          binId: 'bin1',
          type: 'BIN_OVERFLOW',
          title: 'Overflowing bin at market',
          description: 'The bin at Central Market has been overflowing for several days. Waste is spilling onto the road and creating hygiene problems.',
          location: 'Central Market, Douala',
          latitude: 4.0511,
          longitude: 9.7679,
          status: 'PENDING',
          createdAt: new Date().toISOString(),
          user: {
            name: 'Paul Atangana',
            phone: '+237123456793',
          },
          bin: {
            name: 'Central Market Bin 1',
          },
        },
        {
          id: '2',
          userId: 'user2',
          binId: 'bin2',
          type: 'BIN_DAMAGE',
          title: 'Damaged bin',
          description: 'The bin in Bonanjo is broken and does not close properly. Animals can access the waste.',
          location: 'Quartier Bonanjo, Douala',
          latitude: 4.0411,
          longitude: 9.7579,
          status: 'IN_PROGRESS',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Grace Biya',
            phone: '+237123456794',
          },
          bin: {
            name: 'Bonanjo Residential Bin 1',
          },
        },
        {
          id: '3',
          userId: 'user3',
          type: 'ILLEGAL_DUMPING',
          title: 'Illegal waste dumping',
          description: 'Waste has been illegally dumped near the university. There are electronic waste and construction materials.',
          location: 'Near University of Douala',
          latitude: 4.0611,
          longitude: 9.7779,
          status: 'RESOLVED',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Emmanuel Fouda',
            phone: '+237123456795',
          },
        },
        {
          id: '4',
          userId: 'user4',
          binId: 'bin3',
          type: 'MISSED_COLLECTION',
          title: 'Missed collection',
          description: 'The scheduled collection did not take place this week in our neighborhood. The bins are overflowing.',
          location: 'Quartier Mvog-Mbi, Yaoundé',
          latitude: 3.8380,
          longitude: 11.4921,
          status: 'PENDING',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Aminata Sow',
            phone: '+237123456796',
          },
          bin: {
            name: 'Mvog-Mbi Bin 1',
          },
        },
        {
          id: '5',
          userId: 'user5',
          type: 'OTHER',
          title: 'Odor problem',
          description: 'Strong foul odors coming from the collection area. This affects the quality of life of residents.',
          location: 'Quartier Essos, Yaoundé',
          latitude: 3.8780,
          longitude: 11.5321,
          status: 'RESOLVED',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          user: {
            name: 'Joseph Nkomo',
            phone: '+237123456797',
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'RESOLVED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'BIN_OVERFLOW': return TrashIcon;
      case 'BIN_DAMAGE': return ExclamationTriangleIcon;
      case 'BIN_FULL': return TrashIcon;
      case 'MISSED_COLLECTION': return ClockIcon;
      case 'ILLEGAL_DUMPING': return XCircleIcon;
      case 'OTHER': return DocumentTextIcon;
      default: return DocumentTextIcon;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'BIN_OVERFLOW': return 'bg-red-100 text-red-800';
      case 'BIN_DAMAGE': return 'bg-orange-100 text-orange-800';
      case 'BIN_FULL': return 'bg-yellow-100 text-yellow-800';
      case 'MISSED_COLLECTION': return 'bg-purple-100 text-purple-800';
      case 'ILLEGAL_DUMPING': return 'bg-red-100 text-red-800';
      case 'OTHER': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.user?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'ALL' || report.type === typeFilter;
    const matchesStatus = statusFilter === 'ALL' || report.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Citizen Reports Management</h1>
            <p className="mt-2 text-sm text-gray-600">
              Review and manage citizen-submitted waste management reports
            </p>
          </div>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <PlusIcon className="h-5 w-5" />
            <span>Create Report</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by title, description, location, or reporter..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">All Types</option>
              <option value="BIN_OVERFLOW">Bin Overflow</option>
              <option value="BIN_DAMAGE">Bin Damage</option>
              <option value="BIN_FULL">Bin Full</option>
              <option value="MISSED_COLLECTION">Missed Collection</option>
              <option value="ILLEGAL_DUMPING">Illegal Dumping</option>
              <option value="OTHER">Other</option>
            </select>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="RESOLVED">Resolved</option>
              <option value="REJECTED">Rejected</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
