import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  TrashIcon,
  MapPinIcon,
  Battery0Icon,
  FireIcon,
  EyeIcon,
  CameraIcon,
  SignalIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/react/24/outline';
import { binsApi } from '../services/api';
import toast from 'react-hot-toast';

interface BinDetails {
  id: string;
  name: string;
  location: string;
  latitude: number;
  longitude: number;
  capacity: number;
  currentLevel: number;
  status: string;
  sensorId: string;
  batteryLevel: number;
  temperature: number;
  humidity: number;
  lastEmptied: string;
  sensorType: 'ULTRASONIC' | 'CAMERA' | 'WEIGHT';
  hasCamera: boolean;
  cameraUrl?: string;
  aiAnalysis?: {
    fillLevel: number;
    wasteTypes: string[];
    contamination: boolean;
    lastAnalyzed: string;
    confidence: number;
  };
  sensorData: {
    timestamp: string;
    level: number;
    temperature: number;
    humidity: number;
    batteryLevel: number;
  }[];
}

const BinViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [bin, setBin] = useState<BinDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'sensors' | 'camera' | 'history'>('overview');

  useEffect(() => {
    if (id) {
      fetchBinDetails();
    }
  }, [id]);

  const fetchBinDetails = async () => {
    try {
      const response = await binsApi.getById(id!);
      const binData = response.data;
      
      // Mock enhanced data for demonstration
      setBin({
        ...binData,
        sensorType: 'CAMERA',
        hasCamera: true,
        cameraUrl: 'https://via.placeholder.com/640x480/4ade80/ffffff?text=Live+Camera+Feed',
        aiAnalysis: {
          fillLevel: 78,
          wasteTypes: ['Organic', 'Plastic', 'Paper'],
          contamination: false,
          lastAnalyzed: new Date().toISOString(),
          confidence: 94.2,
        },
        sensorData: [
          {
            timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
            level: 75,
            temperature: 28.5,
            humidity: 65,
            batteryLevel: 78,
          },
          {
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            level: 72,
            temperature: 28.2,
            humidity: 64,
            batteryLevel: 79,
          },
          {
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
            level: 68,
            temperature: 27.8,
            humidity: 63,
            batteryLevel: 80,
          },
        ],
      });
    } catch (error) {
      console.error('Failed to fetch bin details:', error);
      toast.error('Failed to load bin details');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshData = async () => {
    setRefreshing(true);
    try {
      await fetchBinDetails();
      toast.success('Data refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  const handleForceEmpty = async () => {
    try {
      await binsApi.forceEmpty(id!);
      toast.success('Force empty request sent');
      fetchBinDetails();
    } catch (error) {
      toast.error('Failed to send force empty request');
    }
  };

  const handleMaintenance = async () => {
    try {
      await binsApi.maintenance(id!, { type: 'SCHEDULED', notes: 'Maintenance requested from bin view' });
      toast.success('Maintenance scheduled');
      fetchBinDetails();
    } catch (error) {
      toast.error('Failed to schedule maintenance');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (!bin) {
    return (
      <div className="text-center py-12">
        <TrashIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Bin not found</h3>
      </div>
    );
  }

  const fillPercentage = (bin.currentLevel / bin.capacity) * 100;
  const aiConfidence = bin.aiAnalysis?.confidence || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{bin.name}</h1>
            <p className="mt-2 text-sm text-gray-600 flex items-center">
              <MapPinIcon className="h-4 w-4 mr-1" />
              {bin.location}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefreshData}
              disabled={refreshing}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={handleForceEmpty}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Force Empty
            </button>
            <button
              onClick={handleMaintenance}
              className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
            >
              <WrenchScrewdriverIcon className="h-4 w-4 mr-2" />
              Maintenance
            </button>
          </div>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Fill Level</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(fillPercentage)}%</p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${
                    fillPercentage >= 90 ? 'bg-red-600' :
                    fillPercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(fillPercentage, 100)}%` }}
                ></div>
              </div>
            </div>
            <TrashIcon className="h-8 w-8 text-gray-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Battery Level</p>
              <p className="text-2xl font-bold text-gray-900">{bin.batteryLevel}%</p>
              <p className="text-xs text-gray-500 mt-1">
                {bin.batteryLevel > 50 ? 'Good' : bin.batteryLevel > 20 ? 'Low' : 'Critical'}
              </p>
            </div>
            <Battery0Icon className={`h-8 w-8 ${
              bin.batteryLevel > 50 ? 'text-green-500' :
              bin.batteryLevel > 20 ? 'text-yellow-500' : 'text-red-500'
            }`} />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Temperature</p>
              <p className="text-2xl font-bold text-gray-900">{bin.temperature}°C</p>
              <p className="text-xs text-gray-500 mt-1">Humidity: {bin.humidity}%</p>
            </div>
            <FireIcon className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Sensor Type</p>
              <p className="text-lg font-bold text-gray-900">{bin.sensorType}</p>
              <p className="text-xs text-gray-500 mt-1">
                {bin.hasCamera ? 'AI-Enabled' : 'Standard'}
              </p>
            </div>
            {bin.hasCamera ? (
              <CameraIcon className="h-8 w-8 text-purple-500" />
            ) : (
              <SignalIcon className="h-8 w-8 text-blue-500" />
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'overview', name: 'Overview', icon: EyeIcon },
              { id: 'sensors', name: 'Sensor Data', icon: SignalIcon },
              { id: 'camera', name: 'Camera Feed', icon: CameraIcon },
              { id: 'history', name: 'History', icon: ClockIcon },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`${
                  selectedTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Bin Information</h3>
                <dl className="space-y-3">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Sensor ID:</dt>
                    <dd className="text-sm font-medium text-gray-900">{bin.sensorId}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Capacity:</dt>
                    <dd className="text-sm font-medium text-gray-900">{bin.capacity}L</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Current Level:</dt>
                    <dd className="text-sm font-medium text-gray-900">{bin.currentLevel}L</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Status:</dt>
                    <dd className={`text-sm font-medium ${
                      bin.status === 'NORMAL' ? 'text-green-600' :
                      bin.status === 'FULL' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {bin.status}
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-600">Last Emptied:</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      {new Date(bin.lastEmptied).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>
              </div>

              {bin.aiAnalysis && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">AI Analysis</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-green-900">AI Confidence</p>
                        <p className="text-2xl font-bold text-green-600">{aiConfidence}%</p>
                      </div>
                      <CheckCircleIcon className="h-8 w-8 text-green-500" />
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-2">Detected Waste Types:</p>
                      <div className="flex flex-wrap gap-2">
                        {bin.aiAnalysis.wasteTypes.map((type, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {type}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Contamination Detected:</span>
                      <span className={`text-sm font-medium ${
                        bin.aiAnalysis.contamination ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {bin.aiAnalysis.contamination ? 'Yes' : 'No'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Last Analyzed:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {new Date(bin.aiAnalysis.lastAnalyzed).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {selectedTab === 'camera' && bin.hasCamera && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Live Camera Feed</h3>
                <div className="relative inline-block">
                  <img
                    src={bin.cameraUrl}
                    alt="Live camera feed"
                    className="rounded-lg shadow-lg max-w-full h-auto"
                  />
                  <div className="absolute top-4 left-4 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium flex items-center">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    LIVE
                  </div>
                </div>
              </div>
              
              {bin.aiAnalysis && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Real-time AI Analysis</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Fill Level (AI):</span>
                      <span className="ml-2 font-medium">{bin.aiAnalysis.fillLevel}%</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Confidence:</span>
                      <span className="ml-2 font-medium">{bin.aiAnalysis.confidence}%</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BinViewPage;
