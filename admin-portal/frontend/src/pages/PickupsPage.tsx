import React, { useState, useEffect } from 'react';
import {
  TruckIcon,
  MapPinIcon,
  ClockIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
  EyeIcon,
  PencilIcon,
  XMarkIcon,
  CalendarIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { pickupsApi } from '../services/api';
import toast from 'react-hot-toast';

interface Pickup {
  id: string;
  userId: string;
  location: string;
  latitude: number;
  longitude: number;
  description: string;
  wasteType: string;
  status: string;
  priority: string;
  scheduledAt: string;
  completedAt?: string;
  createdAt: string;
  user?: {
    name: string;
    phone: string;
  };
  assignment?: {
    userId: string;
    status: string;
    notes: string;
    user: {
      name: string;
    };
  };
}

const PickupsPage: React.FC = () => {
  const [pickups, setPickups] = useState<Pickup[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedPickup, setSelectedPickup] = useState<Pickup | null>(null);
  const [newPickup, setNewPickup] = useState({
    location: '',
    latitude: '',
    longitude: '',
    description: '',
    wasteType: 'GENERAL',
    priority: 'MEDIUM',
    scheduledAt: '',
  });
  const [priorityFilter, setPriorityFilter] = useState('ALL');

  useEffect(() => {
    fetchPickups();
  }, []);

  const fetchPickups = async () => {
    try {
      const response = await pickupsApi.getAll();
      setPickups(response.data.pickups || response.data);
    } catch (error) {
      console.error('Failed to fetch pickups:', error);
      toast.error('Failed to fetch pickups');
      // Mock data fallback
      setPickups([
        {
          id: '1',
          userId: 'user1',
          location: 'Quartier Bastos, Yaoundé',
          latitude: 3.8680,
          longitude: 11.5221,
          description: 'Large household waste collection',
          wasteType: 'BULKY',
          status: 'PENDING',
          priority: 'HIGH',
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString(),
          user: {
            name: 'Paul Atangana',
            phone: '+237123456793',
          },
        },
        {
          id: '2',
          userId: 'user2',
          location: 'Bonanjo, Douala',
          latitude: 4.0411,
          longitude: 9.7579,
          description: 'Restaurant organic waste',
          wasteType: 'ORGANIC',
          status: 'IN_PROGRESS',
          priority: 'NORMAL',
          scheduledAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString(),
          user: {
            name: 'Grace Biya',
            phone: '+237123456794',
          },
          assignment: {
            userId: 'driver1',
            status: 'IN_PROGRESS',
            notes: 'En route to collection point',
            user: {
              name: 'Jean Baptiste Mbarga',
            },
          },
        },
        {
          id: '3',
          userId: 'user3',
          location: 'Akwa, Douala',
          latitude: 4.0311,
          longitude: 9.7479,
          description: 'Electronic waste disposal',
          wasteType: 'ELECTRONIC',
          status: 'COMPLETED',
          priority: 'NORMAL',
          scheduledAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          completedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString(),
          user: {
            name: 'Emmanuel Fouda',
            phone: '+237123456795',
          },
          assignment: {
            userId: 'driver2',
            status: 'COMPLETED',
            notes: 'Collection completed successfully',
            user: {
              name: 'Marie Ngono',
            },
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Schedule new pickup handlers
  const handleSchedulePickup = async () => {
    try {
      const pickupData = {
        ...newPickup,
        latitude: parseFloat(newPickup.latitude),
        longitude: parseFloat(newPickup.longitude),
        status: 'PENDING', // Set proper initial status
        scheduledAt: newPickup.scheduledAt ? new Date(newPickup.scheduledAt).toISOString() : undefined,
      };

      await pickupsApi.create(pickupData);
      toast.success('Pickup scheduled successfully');
      setShowScheduleModal(false);
      setNewPickup({
        location: '',
        latitude: '',
        longitude: '',
        description: '',
        wasteType: 'GENERAL',
        priority: 'MEDIUM',
        scheduledAt: '',
      });
      fetchPickups();
    } catch (error) {
      toast.error('Failed to schedule pickup');
    }
  };

  const handlePickupInputChange = (field: string, value: string) => {
    setNewPickup(prev => ({ ...prev, [field]: value }));
  };

  // Action handlers
  const handleViewPickup = async (pickupId: string) => {
    try {
      const response = await pickupsApi.getById(pickupId);
      setSelectedPickup(response.data);
      setShowViewModal(true);
    } catch (error) {
      toast.error('Failed to load pickup details');
    }
  };

  const handleEditPickup = async (pickupId: string) => {
    console.log('Edit pickup:', pickupId);
    toast('Edit functionality coming soon', { icon: 'ℹ️' });
  };

  const handleStartPickup = async (pickupId: string) => {
    try {
      await pickupsApi.start(pickupId);
      toast.success('Pickup started successfully');
      fetchPickups();
    } catch (error) {
      toast.error('Failed to start pickup');
    }
  };

  const handlePausePickup = async (pickupId: string) => {
    try {
      await pickupsApi.update(pickupId, { status: 'PAUSED' });
      toast.success('Pickup paused successfully');
      fetchPickups();
    } catch (error) {
      toast.error('Failed to pause pickup');
    }
  };

  const handleCancelPickup = async (pickupId: string) => {
    if (window.confirm('Are you sure you want to cancel this pickup request?')) {
      try {
        await pickupsApi.cancel(pickupId, 'Cancelled by admin');
        toast.success('Pickup cancelled successfully');
        fetchPickups();
      } catch (error) {
        toast.error('Failed to cancel pickup');
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-100 text-red-800';
      case 'NORMAL': return 'bg-blue-100 text-blue-800';
      case 'LOW': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getWasteTypeIcon = (wasteType: string) => {
    switch (wasteType) {
      case 'ORGANIC': return '🥬';
      case 'RECYCLABLE': return '♻️';
      case 'ELECTRONIC': return '📱';
      case 'BULKY': return '🛋️';
      case 'CONSTRUCTION': return '🧱';
      default: return '🗑️';
    }
  };

  const filteredPickups = pickups.filter(pickup => {
    const matchesSearch = pickup.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pickup.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pickup.user?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || pickup.status === statusFilter;
    const matchesPriority = priorityFilter === 'ALL' || pickup.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pickup Requests Management</h1>
            <p className="mt-2 text-sm text-gray-600">
              Manage and track waste pickup requests across Cameroon
            </p>
          </div>
          <button
            onClick={() => setShowScheduleModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Schedule Pickup</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by description, location, or customer..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
            >
              <option value="ALL">All Priority</option>
              <option value="HIGH">High</option>
              <option value="NORMAL">Normal</option>
              <option value="LOW">Low</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">
                {pickups.filter(pickup => pickup.status === 'PENDING').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">
                {pickups.filter(pickup => pickup.status === 'IN_PROGRESS').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {pickups.filter(pickup => pickup.status === 'COMPLETED').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-gray-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-gray-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{pickups.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Pickups Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Pickup Requests ({filteredPickups.length})
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Waste Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Scheduled
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned Driver
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPickups.map((pickup) => (
                <tr key={pickup.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center text-lg">
                          {getWasteTypeIcon(pickup.wasteType)}
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">#{pickup.id.slice(-6)}</div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">{pickup.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{pickup.user?.name}</div>
                        <div className="text-sm text-gray-500">{pickup.user?.phone}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <MapPinIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="max-w-xs truncate">{pickup.location}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {pickup.wasteType}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(pickup.status)}`}>
                      {pickup.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(pickup.priority)}`}>
                      {pickup.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {new Date(pickup.scheduledAt).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {pickup.assignment ? (
                      <div className="text-sm">
                        <div className="font-medium text-gray-900">{pickup.assignment.user.name}</div>
                        <div className="text-gray-500">{pickup.assignment.status}</div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Unassigned</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View Details"
                        onClick={() => handleViewPickup(pickup.id)}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                        title="Edit Request"
                        onClick={() => handleEditPickup(pickup.id)}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      {pickup.status === 'PENDING' && (
                        <button
                          className="p-2 text-purple-600 hover:text-purple-900 hover:bg-purple-50 rounded-lg transition-colors"
                          title="Start Pickup"
                          onClick={() => handleStartPickup(pickup.id)}
                        >
                          <PlayIcon className="h-4 w-4" />
                        </button>
                      )}
                      {pickup.status === 'IN_PROGRESS' && (
                        <button
                          className="p-2 text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 rounded-lg transition-colors"
                          title="Pause Pickup"
                          onClick={() => handlePausePickup(pickup.id)}
                        >
                          <PauseIcon className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                        title="Cancel Request"
                        onClick={() => handleCancelPickup(pickup.id)}
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredPickups.length === 0 && (
          <div className="text-center py-12">
            <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No pickup requests found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No pickup requests match your search criteria.
            </p>
          </div>
        )}
      </div>

      {/* Schedule Pickup Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Schedule Pickup</h3>
              <button
                onClick={() => setShowScheduleModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={newPickup.location}
                  onChange={(e) => handlePickupInputChange('location', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., Central Market, Yaoundé"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Latitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    value={newPickup.latitude}
                    onChange={(e) => handlePickupInputChange('latitude', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="3.8480"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Longitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    value={newPickup.longitude}
                    onChange={(e) => handlePickupInputChange('longitude', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="11.5021"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={newPickup.description}
                  onChange={(e) => handlePickupInputChange('description', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows={3}
                  placeholder="Describe the waste pickup request..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Waste Type
                  </label>
                  <select
                    value={newPickup.wasteType}
                    onChange={(e) => handlePickupInputChange('wasteType', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="GENERAL">General</option>
                    <option value="ORGANIC">Organic</option>
                    <option value="PLASTIC">Plastic</option>
                    <option value="PAPER">Paper</option>
                    <option value="GLASS">Glass</option>
                    <option value="METAL">Metal</option>
                    <option value="ELECTRONIC">Electronic</option>
                    <option value="HAZARDOUS">Hazardous</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <select
                    value={newPickup.priority}
                    onChange={(e) => handlePickupInputChange('priority', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="LOW">Low</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HIGH">High</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Scheduled Date & Time (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={newPickup.scheduledAt}
                  onChange={(e) => handlePickupInputChange('scheduledAt', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowScheduleModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSchedulePickup}
                disabled={!newPickup.location || !newPickup.latitude || !newPickup.longitude || !newPickup.description}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Schedule Pickup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Pickup Details Modal */}
      {showViewModal && selectedPickup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-medium text-gray-900">Pickup Request Details</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedPickup(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pickup Information */}
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Request Information</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Location</p>
                        <p className="text-sm text-gray-600">{selectedPickup.location}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <ClockIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Coordinates</p>
                        <p className="text-sm text-gray-600">
                          {selectedPickup.latitude?.toFixed(6)}, {selectedPickup.longitude?.toFixed(6)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <TruckIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Description</p>
                        <p className="text-sm text-gray-600">{selectedPickup.description}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Waste Type</p>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {selectedPickup.wasteType}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Priority</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedPickup.priority === 'URGENT' ? 'bg-red-100 text-red-800' :
                          selectedPickup.priority === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                          selectedPickup.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {selectedPickup.priority}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Status</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedPickup.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          selectedPickup.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                          selectedPickup.status === 'ASSIGNED' ? 'bg-purple-100 text-purple-800' :
                          selectedPickup.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {selectedPickup.status}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Created</p>
                        <p className="text-sm text-gray-600">
                          {new Date(selectedPickup.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {selectedPickup.scheduledAt && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Scheduled For</p>
                        <p className="text-sm text-gray-600">
                          {new Date(selectedPickup.scheduledAt).toLocaleString()}
                        </p>
                      </div>
                    )}

                    {selectedPickup.user && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Requested By</p>
                        <p className="text-sm text-gray-600">
                          {selectedPickup.user.name} ({selectedPickup.user.phone})
                        </p>
                      </div>
                    )}

                    {selectedPickup.assignment && selectedPickup.assignment.driver && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Assigned Driver</p>
                        <p className="text-sm text-gray-600">
                          {selectedPickup.assignment.driver.name} ({selectedPickup.assignment.driver.phone})
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Map */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900">Location Map</h4>
                <div className="bg-gray-100 rounded-lg h-96 flex items-center justify-center relative overflow-hidden">
                  {selectedPickup.latitude && selectedPickup.longitude ? (
                    <iframe
                      src={`https://www.openstreetmap.org/export/embed.html?bbox=${selectedPickup.longitude - 0.01},${selectedPickup.latitude - 0.01},${selectedPickup.longitude + 0.01},${selectedPickup.latitude + 0.01}&layer=mapnik&marker=${selectedPickup.latitude},${selectedPickup.longitude}`}
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="Pickup Location Map"
                      className="rounded-lg"
                    />
                  ) : (
                    <div className="text-center">
                      <MapPinIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">No coordinates available</p>
                    </div>
                  )}
                </div>

                {/* Map Actions */}
                <div className="flex space-x-2">
                  <a
                    href={`https://www.google.com/maps?q=${selectedPickup.latitude},${selectedPickup.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-center hover:bg-blue-700 transition-colors text-sm"
                  >
                    Open in Google Maps
                  </a>
                  <a
                    href={`https://www.openstreetmap.org/?mlat=${selectedPickup.latitude}&mlon=${selectedPickup.longitude}&zoom=16`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg text-center hover:bg-green-700 transition-colors text-sm"
                  >
                    Open in OSM
                  </a>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedPickup(null);
                }}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PickupsPage;
