import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  CogIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface PointsSettings {
  id: string;
  isEnabled: boolean;
  pickupRequestPoints: number;
  reportSubmitPoints: number;
  reportVerifiedPoints: number;
  urgentPickupMultiplier: number;
  maxDailyPoints: number;
  pointsExpiryDays: number;
  createdAt: string;
  updatedAt: string;
}

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<PointsSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    isEnabled: true,
    pickupRequestPoints: 10,
    reportSubmitPoints: 5,
    reportVerifiedPoints: 15,
    urgentPickupMultiplier: 1.5,
    maxDailyPoints: 100,
    pointsExpiryDays: 365,
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/points/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setFormData({
          isEnabled: data.isEnabled,
          pickupRequestPoints: data.pickupRequestPoints,
          reportSubmitPoints: data.reportSubmitPoints,
          reportVerifiedPoints: data.reportVerifiedPoints,
          urgentPickupMultiplier: data.urgentPickupMultiplier,
          maxDailyPoints: data.maxDailyPoints,
          pointsExpiryDays: data.pointsExpiryDays,
        });
      } else {
        toast.error('Failed to fetch settings');
      }
    } catch (error) {
      toast.error('Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/points/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const updatedSettings = await response.json();
        setSettings(updatedSettings);
        toast.success('Settings updated successfully');
      } else {
        toast.error('Failed to update settings');
      }
    } catch (error) {
      toast.error('Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <CogIcon className="h-8 w-8 text-green-600 mr-3" />
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Clean Points System Settings
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Configure the points and rewards system for citizen engagement
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Form */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-6">
            {/* System Enable/Disable */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {formData.isEnabled ? (
                    <CheckCircleIcon className="h-6 w-6 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-6 w-6 text-red-500" />
                  )}
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-900">
                    Enable Points System
                  </h4>
                  <p className="text-sm text-gray-500">
                    Turn the Clean Points system on or off
                  </p>
                </div>
              </div>
              <button
                type="button"
                onClick={() => handleInputChange('isEnabled', !formData.isEnabled)}
                className={`${
                  formData.isEnabled ? 'bg-green-600' : 'bg-gray-200'
                } relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`}
              >
                <span
                  className={`${
                    formData.isEnabled ? 'translate-x-5' : 'translate-x-0'
                  } pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
                />
              </button>
            </div>

            {/* Points Configuration */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Pickup Request Points
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="0"
                    value={formData.pickupRequestPoints}
                    onChange={(e) => handleInputChange('pickupRequestPoints', parseInt(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Points awarded for submitting a pickup request
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Report Submit Points
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="0"
                    value={formData.reportSubmitPoints}
                    onChange={(e) => handleInputChange('reportSubmitPoints', parseInt(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Points awarded for submitting a report
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Verified Report Bonus
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="0"
                    value={formData.reportVerifiedPoints}
                    onChange={(e) => handleInputChange('reportVerifiedPoints', parseInt(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Additional points when a report is verified
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Urgent Pickup Multiplier
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="1"
                    step="0.1"
                    value={formData.urgentPickupMultiplier}
                    onChange={(e) => handleInputChange('urgentPickupMultiplier', parseFloat(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Multiplier for urgent priority pickups
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Max Daily Points
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="0"
                    value={formData.maxDailyPoints}
                    onChange={(e) => handleInputChange('maxDailyPoints', parseInt(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Maximum points a user can earn per day
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Points Expiry (Days)
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    min="0"
                    value={formData.pointsExpiryDays}
                    onChange={(e) => handleInputChange('pointsExpiryDays', parseInt(e.target.value))}
                    className="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Days after which points expire (0 = never expire)
                </p>
              </div>
            </div>

            {/* Info Box */}
            <div className="rounded-md bg-blue-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    How Clean Points Work
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Citizens earn points for environmental actions</li>
                      <li>Urgent pickups receive bonus points (multiplier applied)</li>
                      <li>Daily limits prevent abuse of the system</li>
                      <li>Points can be used for future rewards and recognition</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSave}
                disabled={saving}
                className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
