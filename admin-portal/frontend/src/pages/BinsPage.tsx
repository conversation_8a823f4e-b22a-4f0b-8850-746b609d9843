import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrashIcon,
  MapPinIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Battery0Icon,
  EyeIcon,
  PencilIcon,
  XMarkIcon,
  WrenchScrewdriverIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { api, binsApi } from '../services/api';
import toast from 'react-hot-toast';

interface Bin {
  id: string;
  name: string;
  location: string;
  latitude: number;
  longitude: number;
  capacity: number;
  currentLevel: number;
  status: string;
  sensorId: string;
  batteryLevel: number;
  temperature: number;
  humidity: number;
  lastEmptied: string;
  createdAt: string;
}

const BinsPage: React.FC = () => {
  const navigate = useNavigate();
  const [bins, setBins] = useState<Bin[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedBin, setSelectedBin] = useState<Bin | null>(null);
  const [newBin, setNewBin] = useState({
    name: '',
    location: '',
    latitude: '',
    longitude: '',
    capacity: '',
    sensorId: '',
  });

  useEffect(() => {
    fetchBins();
  }, []);

  const fetchBins = async () => {
    try {
      const response = await binsApi.getAll();
      setBins(response.data.bins || response.data);
    } catch (error) {
      console.error('Failed to fetch bins:', error);
      toast.error('Failed to fetch bins');
      // Mock data fallback
      setBins([
        {
          id: '1',
          name: 'Central Market Bin 1',
          location: 'Marché Central, Douala',
          latitude: 4.0511,
          longitude: 9.7679,
          capacity: 240,
          currentLevel: 95,
          status: 'OVERFLOW',
          sensorId: 'SENSOR_001',
          batteryLevel: 78,
          temperature: 28.5,
          humidity: 65,
          lastEmptied: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'University Campus Bin 1',
          location: 'Université de Douala',
          latitude: 4.0611,
          longitude: 9.7779,
          capacity: 120,
          currentLevel: 45,
          status: 'NORMAL',
          sensorId: 'SENSOR_003',
          batteryLevel: 92,
          temperature: 26.8,
          humidity: 58,
          lastEmptied: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString(),
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Add new bin handlers
  const handleCreateBin = async () => {
    try {
      const binData = {
        ...newBin,
        latitude: parseFloat(newBin.latitude),
        longitude: parseFloat(newBin.longitude),
        capacity: parseInt(newBin.capacity),
      };

      await binsApi.create(binData);
      toast.success('Bin created successfully');
      setShowAddModal(false);
      setNewBin({
        name: '',
        location: '',
        latitude: '',
        longitude: '',
        capacity: '',
        sensorId: '',
      });
      fetchBins();
    } catch (error) {
      toast.error('Failed to create bin');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setNewBin(prev => ({ ...prev, [field]: value }));
  };

  // Action handlers
  const handleViewBin = async (binId: string) => {
    try {
      const response = await binsApi.getById(binId);
      setSelectedBin(response.data);
      setShowViewModal(true);
    } catch (error) {
      toast.error('Failed to load bin details');
    }
  };

  const handleEditBin = async (binId: string) => {
    // TODO: Open edit modal
    console.log('Edit bin:', binId);
    toast('Edit functionality coming soon', { icon: 'ℹ️' });
  };

  const handleMaintenanceBin = async (binId: string) => {
    try {
      await binsApi.maintenance(binId, { type: 'SCHEDULED', notes: 'Maintenance requested from admin panel' });
      toast.success('Maintenance scheduled successfully');
      fetchBins(); // Refresh the list
    } catch (error) {
      toast.error('Failed to schedule maintenance');
    }
  };

  const handleForceEmpty = async (binId: string) => {
    try {
      await binsApi.forceEmpty(binId);
      toast.success('Force empty request sent successfully');
      fetchBins(); // Refresh the list
    } catch (error) {
      toast.error('Failed to send force empty request');
    }
  };

  const handleDeleteBin = async (binId: string) => {
    if (window.confirm('Are you sure you want to delete this bin? This action cannot be undone.')) {
      try {
        await binsApi.delete(binId);
        toast.success('Bin deleted successfully');
        fetchBins(); // Refresh the list
      } catch (error) {
        toast.error('Failed to delete bin');
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OVERFLOW': return 'bg-red-100 text-red-800';
      case 'FULL': return 'bg-yellow-100 text-yellow-800';
      case 'NORMAL': return 'bg-green-100 text-green-800';
      case 'MAINTENANCE': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OVERFLOW': return ExclamationTriangleIcon;
      case 'FULL': return TrashIcon;
      case 'NORMAL': return CheckCircleIcon;
      default: return TrashIcon;
    }
  };

  const getBatteryColor = (level: number) => {
    if (level > 50) return 'text-green-600';
    if (level > 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredBins = bins.filter(bin => {
    const matchesSearch = bin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bin.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || bin.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Smart Waste Bins Management</h1>
            <p className="mt-2 text-sm text-gray-600">
              Monitor and manage intelligent waste bins across Cameroon
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Add New Bin</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name or location..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Status</option>
              <option value="NORMAL">Normal</option>
              <option value="FULL">Full</option>
              <option value="OVERFLOW">Overflow</option>
              <option value="MAINTENANCE">Maintenance</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrashIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Bins</p>
              <p className="text-2xl font-bold text-gray-900">{bins.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Normal</p>
              <p className="text-2xl font-bold text-gray-900">
                {bins.filter(bin => bin.status === 'NORMAL').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <TrashIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Full</p>
              <p className="text-2xl font-bold text-gray-900">
                {bins.filter(bin => bin.status === 'FULL').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Overflow</p>
              <p className="text-2xl font-bold text-gray-900">
                {bins.filter(bin => bin.status === 'OVERFLOW').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bins Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Bins List ({filteredBins.length})
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bin
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fill Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Battery
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Emptied
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBins.map((bin) => {
                const StatusIcon = getStatusIcon(bin.status);
                const fillPercentage = (bin.currentLevel / bin.capacity) * 100;

                return (
                  <tr key={bin.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                            <TrashIcon className="h-5 w-5 text-gray-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{bin.name}</div>
                          <div className="text-sm text-gray-500">{bin.sensorId}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <MapPinIcon className="h-4 w-4 text-gray-400 mr-1" />
                        {bin.location}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="flex justify-between text-sm mb-1">
                            <span>{bin.currentLevel}L</span>
                            <span>{Math.round(fillPercentage)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                fillPercentage >= 100 ? 'bg-red-600' :
                                fillPercentage >= 80 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${Math.min(fillPercentage, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(bin.status)}`}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {bin.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Battery0Icon className={`h-4 w-4 mr-1 ${getBatteryColor(bin.batteryLevel)}`} />
                        <span className={`text-sm ${getBatteryColor(bin.batteryLevel)}`}>
                          {bin.batteryLevel}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(bin.lastEmptied).toLocaleDateString('fr-FR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-colors"
                          title="View Details"
                          onClick={() => handleViewBin(bin.id)}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-lg transition-colors"
                          title="Edit Bin"
                          onClick={() => handleEditBin(bin.id)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 rounded-lg transition-colors"
                          title="Maintenance"
                          onClick={() => handleMaintenanceBin(bin.id)}
                        >
                          <WrenchScrewdriverIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-purple-600 hover:text-purple-900 hover:bg-purple-50 rounded-lg transition-colors"
                          title="Force Empty"
                          onClick={() => handleForceEmpty(bin.id)}
                        >
                          <ArrowPathIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                          title="Delete Bin"
                          onClick={() => handleDeleteBin(bin.id)}
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredBins.length === 0 && (
          <div className="text-center py-12">
            <TrashIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No bins found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No bins match your search criteria.
            </p>
          </div>
        )}
      </div>

      {/* Add New Bin Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Add New Bin</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bin Name
                </label>
                <input
                  type="text"
                  value={newBin.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., Central Market Bin #1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={newBin.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., Central Market, Yaoundé"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Latitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    value={newBin.latitude}
                    onChange={(e) => handleInputChange('latitude', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="3.8480"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Longitude
                  </label>
                  <input
                    type="number"
                    step="any"
                    value={newBin.longitude}
                    onChange={(e) => handleInputChange('longitude', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="11.5021"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Capacity (Liters)
                </label>
                <input
                  type="number"
                  value={newBin.capacity}
                  onChange={(e) => handleInputChange('capacity', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="240"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sensor ID (Optional)
                </label>
                <input
                  type="text"
                  value={newBin.sensorId}
                  onChange={(e) => handleInputChange('sensorId', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="SENSOR_001"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateBin}
                disabled={!newBin.name || !newBin.location || !newBin.latitude || !newBin.longitude || !newBin.capacity}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Create Bin
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Bin Details Modal */}
      {showViewModal && selectedBin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-medium text-gray-900">Bin Details</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedBin(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Bin Information */}
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Bin Information</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <TrashIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Name</p>
                        <p className="text-sm text-gray-600">{selectedBin.name}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Location</p>
                        <p className="text-sm text-gray-600">{selectedBin.location}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Coordinates</p>
                        <p className="text-sm text-gray-600">
                          {selectedBin.latitude?.toFixed(6)}, {selectedBin.longitude?.toFixed(6)}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Capacity</p>
                        <p className="text-sm text-gray-600">{selectedBin.capacity}L</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Current Level</p>
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className={`h-2 rounded-full ${
                                selectedBin.currentLevel >= 90 ? 'bg-red-500' :
                                selectedBin.currentLevel >= 70 ? 'bg-yellow-500' :
                                'bg-green-500'
                              }`}
                              style={{ width: `${selectedBin.currentLevel}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600">{selectedBin.currentLevel}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Status</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedBin.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                          selectedBin.status === 'FULL' ? 'bg-red-100 text-red-800' :
                          selectedBin.status === 'MAINTENANCE' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedBin.status}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Battery Level</p>
                        <div className="flex items-center">
                          <Battery0Icon className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-600">{selectedBin.batteryLevel || 0}%</span>
                        </div>
                      </div>
                    </div>

                    {selectedBin.sensorId && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Sensor ID</p>
                        <p className="text-sm text-gray-600">{selectedBin.sensorId}</p>
                      </div>
                    )}

                    {selectedBin.temperature && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Temperature</p>
                          <p className="text-sm text-gray-600">{selectedBin.temperature}°C</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Humidity</p>
                          <p className="text-sm text-gray-600">{selectedBin.humidity}%</p>
                        </div>
                      </div>
                    )}

                    <div>
                      <p className="text-sm font-medium text-gray-700">Last Emptied</p>
                      <p className="text-sm text-gray-600">
                        {selectedBin.lastEmptied
                          ? new Date(selectedBin.lastEmptied).toLocaleString()
                          : 'Never'
                        }
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-700">Created</p>
                      <p className="text-sm text-gray-600">
                        {new Date(selectedBin.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Map */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900">Location Map</h4>
                <div className="bg-gray-100 rounded-lg h-96 flex items-center justify-center relative overflow-hidden">
                  {selectedBin.latitude && selectedBin.longitude ? (
                    <iframe
                      src={`https://www.openstreetmap.org/export/embed.html?bbox=${selectedBin.longitude - 0.01},${selectedBin.latitude - 0.01},${selectedBin.longitude + 0.01},${selectedBin.latitude + 0.01}&layer=mapnik&marker=${selectedBin.latitude},${selectedBin.longitude}`}
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="Bin Location Map"
                      className="rounded-lg"
                    />
                  ) : (
                    <div className="text-center">
                      <MapPinIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">No coordinates available</p>
                    </div>
                  )}
                </div>

                {/* Map Actions */}
                <div className="flex space-x-2">
                  <a
                    href={`https://www.google.com/maps?q=${selectedBin.latitude},${selectedBin.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-center hover:bg-blue-700 transition-colors text-sm"
                  >
                    Open in Google Maps
                  </a>
                  <a
                    href={`https://www.openstreetmap.org/?mlat=${selectedBin.latitude}&mlon=${selectedBin.longitude}&zoom=16`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg text-center hover:bg-green-700 transition-colors text-sm"
                  >
                    Open in OSM
                  </a>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedBin(null);
                }}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BinsPage;
