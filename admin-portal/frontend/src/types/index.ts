export interface User {
  id: string;
  email: string;
  phone: string;
  name: string;
  role: 'CITI<PERSON><PERSON>' | 'ADMIN' | 'DRIVER' | 'SUPERVISOR';
  isActive: boolean;
  address?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    pickups: number;
    reports: number;
    assignments?: number;
  };
}

export interface Pickup {
  id: string;
  userId: string;
  location: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  wasteType: 'GENERAL' | 'RECYCLABLE' | 'ORGANIC' | 'HAZARDOUS' | 'ELECTRONIC';
  status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  scheduledAt?: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
  user: Pick<User, 'id' | 'name' | 'phone' | 'email'>;
  assignments?: Assignment[];
  images?: PickupImage[];
}

export interface Bin {
  id: string;
  name: string;
  location: string;
  latitude: number;
  longitude: number;
  capacity: number;
  currentLevel: number;
  status: 'NORMAL' | 'FULL' | 'OVERFLOW' | 'DAMAGED' | 'MAINTENANCE';
  lastEmptied?: string;
  sensorId?: string;
  batteryLevel?: number;
  temperature?: number;
  humidity?: number;
  createdAt: string;
  updatedAt: string;
  alerts?: Alert[];
  reports?: Report[];
  _count?: {
    alerts: number;
    reports: number;
  };
}

export interface Alert {
  id: string;
  binId?: string;
  type: 'BIN_FULL' | 'BIN_OVERFLOW' | 'SENSOR_OFFLINE' | 'MAINTENANCE_REQUIRED' | 'ILLEGAL_DUMPING' | 'SYSTEM_ERROR';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  createdAt: string;
  updatedAt: string;
  bin?: Pick<Bin, 'id' | 'name' | 'location' | 'latitude' | 'longitude' | 'currentLevel' | 'status'>;
}

export interface Report {
  id: string;
  userId: string;
  binId?: string;
  type: 'ILLEGAL_DUMPING' | 'DAMAGED_BIN' | 'MISSED_PICKUP' | 'OVERFLOW' | 'OTHER';
  title: string;
  description: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  status: 'PENDING' | 'INVESTIGATING' | 'RESOLVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  user: Pick<User, 'id' | 'name' | 'phone' | 'email'>;
  bin?: Pick<Bin, 'id' | 'name' | 'location' | 'latitude' | 'longitude'>;
  images?: ReportImage[];
}

export interface Assignment {
  id: string;
  pickupId: string;
  userId: string;
  status: 'ASSIGNED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  pickup?: Pickup;
  user: Pick<User, 'id' | 'name' | 'phone' | 'email'>;
}

export interface PickupImage {
  id: string;
  pickupId: string;
  url: string;
  caption?: string;
  createdAt: string;
}

export interface ReportImage {
  id: string;
  reportId: string;
  url: string;
  caption?: string;
  createdAt: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: User['role'];
  phone: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  user: AuthUser;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface Statistics {
  bins?: {
    totalBins: number;
    statusBreakdown: {
      normal: number;
      full: number;
      overflow: number;
      damaged: number;
    };
    averageLevel: number;
  };
  alerts?: {
    totalAlerts: number;
    unresolvedAlerts: number;
    severityBreakdown: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
    typeBreakdown: Record<string, number>;
  };
  reports?: {
    totalReports: number;
    statusBreakdown: {
      pending: number;
      investigating: number;
      resolved: number;
      rejected: number;
    };
    typeBreakdown: Record<string, number>;
  };
}
