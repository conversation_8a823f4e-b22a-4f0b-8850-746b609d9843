# Cleancity360 – Waste Management Solution

## Project Overview

**Cleancity360** is a comprehensive waste management solution targeting urban sanitation challenges in Cameroon. The system provides a multi-tiered approach to waste collection, monitoring, and eventual recycling, with AI-assisted camera monitoring and IoT sensors.

## System Architecture

The system consists of three main components:

### 1. **Admin Portal** (`admin-portal/`)
- **Backend**: NestJS with PostgreSQL
- **Frontend**: React with TypeScript
- **Purpose**: Monitor waste levels, assign pickup teams, generate analytics

### 2. **Mobile App** (`mobile-app/`)
- **Framework**: React Native with Expo
- **Purpose**: Citizens request waste pickup, view drop-off locations, receive alerts

### 3. **IoT Engine** (`iot-engine/`)
- **Tech Stack**: Python with MQTT/BLE support
- **Purpose**: Interface with IoT sensors, trigger alerts, feed data to ML models

## Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- PostgreSQL 14+
- Docker & Docker Compose

### Development Setup

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd cleancity360
   npm run setup
   ```

2. **Start all services**
   ```bash
   docker-compose up -d
   ```

3. **Access the applications**
   - Admin Portal: http://localhost:3000
   - API Documentation: http://localhost:3001/api
   - Mobile App: Use Expo Go app

## Project Structure

```
cleancity360/
├── admin-portal/
│   ├── backend/          # NestJS API
│   └── frontend/         # React Admin Dashboard
├── mobile-app/           # React Native App
├── iot-engine/           # Python IoT System
├── docker-compose.yml    # Development environment
└── scripts/              # Setup and deployment scripts
```

## Features

### Mobile App
- ✅ User authentication
- ✅ Waste pickup requests
- ✅ Interactive maps for drop-off points
- ✅ Real-time notifications
- ✅ Waste reporting system

### Admin Portal
- ✅ Dashboard with analytics
- ✅ User and pickup management
- ✅ IoT sensor monitoring
- ✅ Team assignment system
- ✅ Report generation

### IoT Engine
- ✅ Ultrasonic sensor integration
- ✅ Camera-based waste detection
- ✅ MQTT communication
- ✅ Real-time alerting system

## Technology Stack

- **Backend**: NestJS, Prisma, PostgreSQL, JWT
- **Frontend**: React, TypeScript, Tailwind CSS
- **Mobile**: React Native, Expo, React Navigation
- **IoT**: Python, OpenCV, MQTT, Raspberry Pi
- **DevOps**: Docker, Docker Compose, GitHub Actions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support, email <EMAIL> or create an issue in this repository.
