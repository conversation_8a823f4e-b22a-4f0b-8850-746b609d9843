#!/usr/bin/env python3
"""
Cleancity360 IoT Engine
Main entry point for the IoT monitoring system
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

from src.sensors.ultrasonic import UltrasonicSensor
from src.camera.detector import WasteDetector
from src.mqtt.client import MQTTClient
from src.api.client import APIClient
from src.utils.config import Config
from src.utils.logger import setup_logger

class IoTEngine:
    def __init__(self):
        self.config = Config()
        self.logger = setup_logger()
        self.running = False
        
        # Initialize components
        self.ultrasonic_sensor = UltrasonicSensor(
            trigger_pin=self.config.ULTRASONIC_TRIGGER_PIN,
            echo_pin=self.config.ULTRASONIC_ECHO_PIN
        )
        
        self.waste_detector = WasteDetector(
            model_path=self.config.YOLO_MODEL_PATH
        )
        
        self.mqtt_client = MQTTClient(
            broker_url=self.config.MQTT_BROKER_URL,
            device_id=self.config.DEVICE_ID
        )
        
        self.api_client = APIClient(
            base_url=self.config.API_BASE_URL,
            device_id=self.config.DEVICE_ID
        )

    async def start(self):
        """Start the IoT engine"""
        self.logger.info("Starting Cleancity360 IoT Engine...")
        self.running = True
        
        try:
            # Initialize MQTT connection
            await self.mqtt_client.connect()
            
            # Start monitoring loop
            await self.monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"Error in IoT engine: {e}")
            raise
        finally:
            await self.cleanup()

    async def monitoring_loop(self):
        """Main monitoring loop"""
        self.logger.info("Starting monitoring loop...")
        
        while self.running:
            try:
                # Read sensor data
                sensor_data = await self.read_sensors()
                
                # Analyze camera feed
                camera_data = await self.analyze_camera()
                
                # Combine data
                combined_data = {
                    **sensor_data,
                    **camera_data,
                    'timestamp': asyncio.get_event_loop().time(),
                    'device_id': self.config.DEVICE_ID
                }
                
                # Send data via MQTT
                await self.mqtt_client.publish_sensor_data(combined_data)
                
                # Send data to API
                await self.api_client.send_sensor_data(combined_data)
                
                # Check for alerts
                await self.check_alerts(combined_data)
                
                # Wait before next reading
                await asyncio.sleep(self.config.MONITORING_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def read_sensors(self):
        """Read data from all sensors"""
        try:
            # Read ultrasonic sensor
            distance = self.ultrasonic_sensor.get_distance()
            fill_level = self.ultrasonic_sensor.calculate_fill_level(
                distance, 
                self.config.BIN_HEIGHT
            )
            
            return {
                'distance': distance,
                'fill_level': fill_level,
                'sensor_status': 'online'
            }
            
        except Exception as e:
            self.logger.error(f"Error reading sensors: {e}")
            return {
                'distance': None,
                'fill_level': None,
                'sensor_status': 'error'
            }

    async def analyze_camera(self):
        """Analyze camera feed for waste detection"""
        try:
            # Capture frame
            frame = self.waste_detector.capture_frame()
            if frame is None:
                return {'camera_status': 'offline'}
            
            # Detect waste/overflow
            detection_result = self.waste_detector.detect_waste(frame)
            
            return {
                'camera_status': 'online',
                'waste_detected': detection_result['waste_detected'],
                'overflow_detected': detection_result['overflow_detected'],
                'confidence': detection_result['confidence'],
                'detection_count': detection_result['detection_count']
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing camera: {e}")
            return {
                'camera_status': 'error',
                'waste_detected': False,
                'overflow_detected': False
            }

    async def check_alerts(self, data):
        """Check if alerts need to be triggered"""
        alerts = []
        
        # Check fill level alerts
        if data.get('fill_level', 0) >= self.config.ALERT_THRESHOLD_FULL:
            if data.get('fill_level', 0) >= self.config.ALERT_THRESHOLD_OVERFLOW:
                alerts.append({
                    'type': 'BIN_OVERFLOW',
                    'severity': 'HIGH',
                    'message': f"Bin overflow detected! Fill level: {data['fill_level']:.1f}%"
                })
            else:
                alerts.append({
                    'type': 'BIN_FULL',
                    'severity': 'MEDIUM',
                    'message': f"Bin is full. Fill level: {data['fill_level']:.1f}%"
                })
        
        # Check camera alerts
        if data.get('overflow_detected'):
            alerts.append({
                'type': 'ILLEGAL_DUMPING',
                'severity': 'HIGH',
                'message': "Illegal dumping or overflow detected by camera"
            })
        
        # Check sensor status alerts
        if data.get('sensor_status') == 'error':
            alerts.append({
                'type': 'SENSOR_OFFLINE',
                'severity': 'MEDIUM',
                'message': "Ultrasonic sensor is offline or malfunctioning"
            })
        
        if data.get('camera_status') == 'error':
            alerts.append({
                'type': 'SENSOR_OFFLINE',
                'severity': 'LOW',
                'message': "Camera is offline or malfunctioning"
            })
        
        # Send alerts
        for alert in alerts:
            await self.send_alert(alert)

    async def send_alert(self, alert):
        """Send alert via MQTT and API"""
        try:
            alert_data = {
                **alert,
                'device_id': self.config.DEVICE_ID,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            # Send via MQTT
            await self.mqtt_client.publish_alert(alert_data)
            
            # Send via API
            await self.api_client.send_alert(alert_data)
            
            self.logger.warning(f"Alert sent: {alert['type']} - {alert['message']}")
            
        except Exception as e:
            self.logger.error(f"Error sending alert: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        self.logger.info("Cleaning up IoT engine...")
        
        try:
            await self.mqtt_client.disconnect()
            self.ultrasonic_sensor.cleanup()
            self.waste_detector.cleanup()
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def stop(self):
        """Stop the IoT engine"""
        self.logger.info("Stopping IoT engine...")
        self.running = False

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print(f"\nReceived signal {signum}. Shutting down gracefully...")
    sys.exit(0)

async def main():
    """Main function"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start IoT engine
    engine = IoTEngine()
    
    try:
        await engine.start()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        engine.stop()

if __name__ == "__main__":
    asyncio.run(main())
