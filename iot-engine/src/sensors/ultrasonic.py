"""
Ultrasonic sensor interface for measuring bin fill levels
"""

import time
import logging
from typing import Optional

try:
    import RPi.GPIO as GPIO
    GPIO_AVAILABLE = True
except ImportError:
    GPIO_AVAILABLE = False
    logging.warning("RPi.GPIO not available. Running in simulation mode.")

class UltrasonicSensor:
    """Ultrasonic sensor for measuring distance and calculating fill levels"""
    
    def __init__(self, trigger_pin: int, echo_pin: int):
        self.trigger_pin = trigger_pin
        self.echo_pin = echo_pin
        self.logger = logging.getLogger(__name__)
        
        if GPIO_AVAILABLE:
            self._setup_gpio()
        else:
            self.logger.warning("GPIO not available. Sensor will return simulated data.")
    
    def _setup_gpio(self):
        """Setup GPIO pins for ultrasonic sensor"""
        try:
            GPIO.setmode(GPIO.BCM)
            GPIO.setup(self.trigger_pin, GPIO.OUT)
            GPIO.setup(self.echo_pin, GPIO.IN)
            GPIO.output(self.trigger_pin, False)
            self.logger.info(f"GPIO setup complete. Trigger: {self.trigger_pin}, Echo: {self.echo_pin}")
        except Exception as e:
            self.logger.error(f"Error setting up GPIO: {e}")
            raise
    
    def get_distance(self) -> Optional[float]:
        """
        Get distance measurement in centimeters
        Returns None if measurement fails
        """
        if not GPIO_AVAILABLE:
            # Return simulated data for testing
            import random
            return random.uniform(10.0, 90.0)
        
        try:
            # Send trigger pulse
            GPIO.output(self.trigger_pin, True)
            time.sleep(0.00001)  # 10 microseconds
            GPIO.output(self.trigger_pin, False)
            
            # Wait for echo start
            pulse_start = time.time()
            timeout = pulse_start + 0.1  # 100ms timeout
            
            while GPIO.input(self.echo_pin) == 0:
                pulse_start = time.time()
                if pulse_start > timeout:
                    self.logger.warning("Timeout waiting for echo start")
                    return None
            
            # Wait for echo end
            pulse_end = time.time()
            timeout = pulse_end + 0.1  # 100ms timeout
            
            while GPIO.input(self.echo_pin) == 1:
                pulse_end = time.time()
                if pulse_end > timeout:
                    self.logger.warning("Timeout waiting for echo end")
                    return None
            
            # Calculate distance
            pulse_duration = pulse_end - pulse_start
            distance = pulse_duration * 17150  # Speed of sound / 2
            
            # Validate distance (typical range: 2cm to 400cm)
            if 2 <= distance <= 400:
                return round(distance, 2)
            else:
                self.logger.warning(f"Distance out of range: {distance}cm")
                return None
                
        except Exception as e:
            self.logger.error(f"Error measuring distance: {e}")
            return None
    
    def calculate_fill_level(self, distance: Optional[float], bin_height: float) -> Optional[float]:
        """
        Calculate fill level percentage based on distance measurement
        
        Args:
            distance: Distance from sensor to waste surface (cm)
            bin_height: Total height of the bin (cm)
        
        Returns:
            Fill level as percentage (0-100), or None if calculation fails
        """
        if distance is None:
            return None
        
        try:
            # Calculate fill level
            # Assuming sensor is mounted at the top of the bin
            fill_height = bin_height - distance
            fill_percentage = (fill_height / bin_height) * 100
            
            # Clamp to valid range
            fill_percentage = max(0, min(100, fill_percentage))
            
            return round(fill_percentage, 1)
            
        except Exception as e:
            self.logger.error(f"Error calculating fill level: {e}")
            return None
    
    def get_multiple_readings(self, count: int = 5, delay: float = 0.1) -> Optional[float]:
        """
        Take multiple distance readings and return the median
        This helps reduce noise and improve accuracy
        """
        readings = []
        
        for _ in range(count):
            distance = self.get_distance()
            if distance is not None:
                readings.append(distance)
            time.sleep(delay)
        
        if not readings:
            return None
        
        # Return median value
        readings.sort()
        n = len(readings)
        if n % 2 == 0:
            return (readings[n//2 - 1] + readings[n//2]) / 2
        else:
            return readings[n//2]
    
    def cleanup(self):
        """Cleanup GPIO resources"""
        if GPIO_AVAILABLE:
            try:
                GPIO.cleanup()
                self.logger.info("GPIO cleanup complete")
            except Exception as e:
                self.logger.error(f"Error during GPIO cleanup: {e}")
    
    def test_sensor(self) -> dict:
        """Test sensor functionality and return status"""
        try:
            distance = self.get_distance()
            
            if distance is not None:
                return {
                    'status': 'ok',
                    'distance': distance,
                    'message': 'Sensor is working correctly'
                }
            else:
                return {
                    'status': 'error',
                    'distance': None,
                    'message': 'Failed to get distance reading'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'distance': None,
                'message': f'Sensor test failed: {e}'
            }
