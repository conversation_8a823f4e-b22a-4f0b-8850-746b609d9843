"""
MQTT client for IoT communication
"""

import asyncio
import json
import logging
from typing import Dict, Optional
from urllib.parse import urlparse

try:
    import paho.mqtt.client as mqtt
    MQTT_AVAILABLE = True
except ImportError:
    MQTT_AVAILABLE = False
    logging.warning("paho-mqtt not available. MQTT functionality disabled.")

class MQTTClient:
    """MQTT client for publishing sensor data and alerts"""
    
    def __init__(self, broker_url: str, device_id: str, username: str = "", password: str = ""):
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # Parse broker URL
        parsed_url = urlparse(broker_url)
        self.broker_host = parsed_url.hostname or 'localhost'
        self.broker_port = parsed_url.port or 1883
        self.username = username
        self.password = password
        
        # MQTT topics
        self.sensor_topic = f"cleancity360/{device_id}/sensors"
        self.alert_topic = f"cleancity360/{device_id}/alerts"
        self.status_topic = f"cleancity360/{device_id}/status"
        self.command_topic = f"cleancity360/{device_id}/commands"
        
        # MQTT client
        self.client = None
        self.connected = False
        
        if MQTT_AVAILABLE:
            self._init_client()
        else:
            self.logger.warning("MQTT not available. Running in simulation mode.")
    
    def _init_client(self):
        """Initialize MQTT client"""
        try:
            self.client = mqtt.Client(client_id=self.device_id)
            
            # Set callbacks
            self.client.on_connect = self._on_connect
            self.client.on_disconnect = self._on_disconnect
            self.client.on_message = self._on_message
            self.client.on_publish = self._on_publish
            
            # Set credentials if provided
            if self.username and self.password:
                self.client.username_pw_set(self.username, self.password)
            
            # Set will message (last will and testament)
            will_payload = json.dumps({
                'device_id': self.device_id,
                'status': 'offline',
                'timestamp': asyncio.get_event_loop().time()
            })
            self.client.will_set(self.status_topic, will_payload, qos=1, retain=True)
            
            self.logger.info("MQTT client initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing MQTT client: {e}")
            self.client = None
    
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when client connects to broker"""
        if rc == 0:
            self.connected = True
            self.logger.info(f"Connected to MQTT broker at {self.broker_host}:{self.broker_port}")
            
            # Subscribe to command topic
            client.subscribe(self.command_topic, qos=1)
            
            # Publish online status
            status_payload = json.dumps({
                'device_id': self.device_id,
                'status': 'online',
                'timestamp': asyncio.get_event_loop().time()
            })
            client.publish(self.status_topic, status_payload, qos=1, retain=True)
            
        else:
            self.connected = False
            self.logger.error(f"Failed to connect to MQTT broker. Return code: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when client disconnects from broker"""
        self.connected = False
        if rc != 0:
            self.logger.warning("Unexpected MQTT disconnection")
        else:
            self.logger.info("Disconnected from MQTT broker")
    
    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received"""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            
            self.logger.info(f"Received message on topic {topic}: {payload}")
            
            # Handle commands
            if topic == self.command_topic:
                self._handle_command(payload)
                
        except Exception as e:
            self.logger.error(f"Error processing MQTT message: {e}")
    
    def _on_publish(self, client, userdata, mid):
        """Callback for when a message is published"""
        self.logger.debug(f"Message published with mid: {mid}")
    
    def _handle_command(self, command: Dict):
        """Handle incoming commands"""
        try:
            command_type = command.get('type')
            
            if command_type == 'ping':
                # Respond to ping
                response = {
                    'type': 'pong',
                    'device_id': self.device_id,
                    'timestamp': asyncio.get_event_loop().time()
                }
                self.publish_message(self.status_topic, response)
                
            elif command_type == 'restart':
                self.logger.info("Restart command received")
                # Handle restart logic here
                
            elif command_type == 'update_config':
                self.logger.info("Config update command received")
                # Handle config update logic here
                
            else:
                self.logger.warning(f"Unknown command type: {command_type}")
                
        except Exception as e:
            self.logger.error(f"Error handling command: {e}")
    
    async def connect(self):
        """Connect to MQTT broker"""
        if not MQTT_AVAILABLE or self.client is None:
            self.logger.warning("MQTT not available. Skipping connection.")
            return
        
        try:
            self.logger.info(f"Connecting to MQTT broker at {self.broker_host}:{self.broker_port}")
            self.client.connect_async(self.broker_host, self.broker_port, 60)
            self.client.loop_start()
            
            # Wait for connection
            for _ in range(50):  # Wait up to 5 seconds
                if self.connected:
                    break
                await asyncio.sleep(0.1)
            
            if not self.connected:
                raise Exception("Failed to connect within timeout")
                
        except Exception as e:
            self.logger.error(f"Error connecting to MQTT broker: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from MQTT broker"""
        if not MQTT_AVAILABLE or self.client is None:
            return
        
        try:
            # Publish offline status
            if self.connected:
                status_payload = json.dumps({
                    'device_id': self.device_id,
                    'status': 'offline',
                    'timestamp': asyncio.get_event_loop().time()
                })
                self.client.publish(self.status_topic, status_payload, qos=1, retain=True)
            
            self.client.loop_stop()
            self.client.disconnect()
            self.connected = False
            self.logger.info("Disconnected from MQTT broker")
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from MQTT broker: {e}")
    
    def publish_message(self, topic: str, payload: Dict, qos: int = 1) -> bool:
        """Publish a message to MQTT broker"""
        if not MQTT_AVAILABLE or self.client is None or not self.connected:
            self.logger.warning("MQTT not available or not connected. Skipping publish.")
            return False
        
        try:
            json_payload = json.dumps(payload)
            result = self.client.publish(topic, json_payload, qos=qos)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.debug(f"Published to {topic}: {payload}")
                return True
            else:
                self.logger.error(f"Failed to publish to {topic}. Return code: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error publishing MQTT message: {e}")
            return False
    
    async def publish_sensor_data(self, data: Dict) -> bool:
        """Publish sensor data"""
        payload = {
            'device_id': self.device_id,
            'type': 'sensor_data',
            'data': data,
            'timestamp': asyncio.get_event_loop().time()
        }
        return self.publish_message(self.sensor_topic, payload)
    
    async def publish_alert(self, alert: Dict) -> bool:
        """Publish alert"""
        payload = {
            'device_id': self.device_id,
            'type': 'alert',
            'alert': alert,
            'timestamp': asyncio.get_event_loop().time()
        }
        return self.publish_message(self.alert_topic, payload)
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return self.connected and MQTT_AVAILABLE
