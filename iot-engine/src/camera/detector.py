"""
Computer vision waste detection using YOLO
"""

import cv2
import numpy as np
import logging
from pathlib import Path
from typing import Optional, Dict, List
import time

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    logging.warning("Ultralytics YOLO not available. Running in simulation mode.")

class WasteDetector:
    """Computer vision detector for waste and overflow detection"""
    
    def __init__(self, model_path: str = "models/waste_detection.pt", camera_index: int = 0):
        self.model_path = model_path
        self.camera_index = camera_index
        self.logger = logging.getLogger(__name__)
        
        # Initialize camera
        self.camera = None
        self._init_camera()
        
        # Initialize YOLO model
        self.model = None
        if YOLO_AVAILABLE and Path(model_path).exists():
            self._init_model()
        else:
            self.logger.warning("YOLO model not available. Running in simulation mode.")
        
        # Detection parameters
        self.confidence_threshold = 0.5
        self.waste_classes = ['waste', 'garbage', 'trash', 'overflow']
        
    def _init_camera(self):
        """Initialize camera capture"""
        try:
            self.camera = cv2.VideoCapture(self.camera_index)
            if not self.camera.isOpened():
                self.logger.error(f"Failed to open camera {self.camera_index}")
                self.camera = None
                return
            
            # Set camera properties
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            self.logger.info(f"Camera {self.camera_index} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing camera: {e}")
            self.camera = None
    
    def _init_model(self):
        """Initialize YOLO model"""
        try:
            self.model = YOLO(self.model_path)
            self.logger.info(f"YOLO model loaded from {self.model_path}")
        except Exception as e:
            self.logger.error(f"Error loading YOLO model: {e}")
            self.model = None
    
    def capture_frame(self) -> Optional[np.ndarray]:
        """Capture a frame from the camera"""
        if self.camera is None:
            return None
        
        try:
            ret, frame = self.camera.read()
            if ret:
                return frame
            else:
                self.logger.warning("Failed to capture frame")
                return None
                
        except Exception as e:
            self.logger.error(f"Error capturing frame: {e}")
            return None
    
    def detect_waste(self, frame: np.ndarray) -> Dict:
        """
        Detect waste and overflow in the frame
        
        Returns:
            Dictionary with detection results
        """
        if frame is None:
            return self._empty_detection_result()
        
        if self.model is None or not YOLO_AVAILABLE:
            # Return simulated detection for testing
            return self._simulate_detection()
        
        try:
            # Run YOLO inference
            results = self.model(frame, conf=self.confidence_threshold)
            
            # Process results
            detections = []
            waste_detected = False
            overflow_detected = False
            max_confidence = 0.0
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class name and confidence
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        class_name = self.model.names[class_id].lower()
                        
                        # Check if it's a waste-related class
                        if any(waste_class in class_name for waste_class in self.waste_classes):
                            waste_detected = True
                            max_confidence = max(max_confidence, confidence)
                            
                            # Check for overflow indicators
                            if 'overflow' in class_name or confidence > 0.8:
                                overflow_detected = True
                            
                            # Get bounding box coordinates
                            x1, y1, x2, y2 = box.xyxy[0].tolist()
                            
                            detections.append({
                                'class': class_name,
                                'confidence': confidence,
                                'bbox': [x1, y1, x2, y2]
                            })
            
            return {
                'waste_detected': waste_detected,
                'overflow_detected': overflow_detected,
                'confidence': max_confidence,
                'detection_count': len(detections),
                'detections': detections,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Error during waste detection: {e}")
            return self._empty_detection_result()
    
    def _simulate_detection(self) -> Dict:
        """Simulate detection results for testing"""
        import random
        
        # Simulate occasional waste detection
        waste_detected = random.random() < 0.3  # 30% chance
        overflow_detected = random.random() < 0.1 if waste_detected else False  # 10% chance if waste detected
        confidence = random.uniform(0.5, 0.9) if waste_detected else 0.0
        
        return {
            'waste_detected': waste_detected,
            'overflow_detected': overflow_detected,
            'confidence': confidence,
            'detection_count': random.randint(0, 3) if waste_detected else 0,
            'detections': [],
            'timestamp': time.time()
        }
    
    def _empty_detection_result(self) -> Dict:
        """Return empty detection result"""
        return {
            'waste_detected': False,
            'overflow_detected': False,
            'confidence': 0.0,
            'detection_count': 0,
            'detections': [],
            'timestamp': time.time()
        }
    
    def save_detection_image(self, frame: np.ndarray, detections: List[Dict], 
                           output_path: str) -> bool:
        """Save frame with detection annotations"""
        try:
            # Draw bounding boxes
            annotated_frame = frame.copy()
            
            for detection in detections:
                bbox = detection['bbox']
                confidence = detection['confidence']
                class_name = detection['class']
                
                # Draw bounding box
                x1, y1, x2, y2 = map(int, bbox)
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw label
                label = f"{class_name}: {confidence:.2f}"
                cv2.putText(annotated_frame, label, (x1, y1 - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Save image
            cv2.imwrite(output_path, annotated_frame)
            self.logger.info(f"Detection image saved to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving detection image: {e}")
            return False
    
    def get_camera_status(self) -> Dict:
        """Get camera status information"""
        if self.camera is None:
            return {
                'status': 'offline',
                'message': 'Camera not initialized'
            }
        
        try:
            # Try to capture a frame to test camera
            ret, _ = self.camera.read()
            if ret:
                return {
                    'status': 'online',
                    'message': 'Camera is working correctly'
                }
            else:
                return {
                    'status': 'error',
                    'message': 'Failed to capture frame'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Camera error: {e}'
            }
    
    def cleanup(self):
        """Cleanup camera resources"""
        try:
            if self.camera is not None:
                self.camera.release()
                self.logger.info("Camera resources cleaned up")
        except Exception as e:
            self.logger.error(f"Error during camera cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
