"""
Configuration management for IoT Engine
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for IoT Engine"""
    
    def __init__(self):
        # Device configuration
        self.DEVICE_ID = os.getenv('IOT_DEVICE_ID', 'cleancity360-iot-001')
        
        # API configuration
        self.API_BASE_URL = os.getenv('IOT_API_BASE_URL', 'http://localhost:3001/api')
        
        # MQTT configuration
        self.MQTT_BROKER_URL = os.getenv('MQTT_BROKER_URL', 'mqtt://localhost:1883')
        self.MQTT_USERNAME = os.getenv('MQTT_USERNAME', '')
        self.MQTT_PASSWORD = os.getenv('MQTT_PASSWORD', '')
        
        # Sensor configuration
        self.ULTRASONIC_TRIGGER_PIN = int(os.getenv('ULTRASONIC_TRIGGER_PIN', '18'))
        self.ULTRASONIC_ECHO_PIN = int(os.getenv('ULTRASONIC_ECHO_PIN', '24'))
        
        # Camera configuration
        self.CAMERA_INDEX = int(os.getenv('CAMERA_INDEX', '0'))
        self.CAMERA_WIDTH = int(os.getenv('CAMERA_WIDTH', '640'))
        self.CAMERA_HEIGHT = int(os.getenv('CAMERA_HEIGHT', '480'))
        
        # AI Model configuration
        self.YOLO_MODEL_PATH = os.getenv('YOLO_MODEL_PATH', 'models/waste_detection.pt')
        self.DETECTION_CONFIDENCE = float(os.getenv('DETECTION_CONFIDENCE', '0.5'))
        
        # Bin configuration
        self.BIN_HEIGHT = float(os.getenv('BIN_HEIGHT', '100.0'))  # cm
        self.BIN_CAPACITY = float(os.getenv('BIN_CAPACITY', '240.0'))  # liters
        
        # Alert thresholds
        self.ALERT_THRESHOLD_FULL = float(os.getenv('ALERT_THRESHOLD_FULL', '80.0'))  # %
        self.ALERT_THRESHOLD_OVERFLOW = float(os.getenv('ALERT_THRESHOLD_OVERFLOW', '95.0'))  # %
        
        # Monitoring configuration
        self.MONITORING_INTERVAL = float(os.getenv('MONITORING_INTERVAL', '30.0'))  # seconds
        self.DATA_RETENTION_DAYS = int(os.getenv('DATA_RETENTION_DAYS', '30'))
        
        # Logging configuration
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_FILE = os.getenv('LOG_FILE', 'logs/iot_engine.log')
        
        # Paths
        self.BASE_DIR = Path(__file__).parent.parent.parent
        self.MODELS_DIR = self.BASE_DIR / 'models'
        self.LOGS_DIR = self.BASE_DIR / 'logs'
        self.CONFIG_DIR = self.BASE_DIR / 'config'
        
        # Create directories if they don't exist
        self.MODELS_DIR.mkdir(exist_ok=True)
        self.LOGS_DIR.mkdir(exist_ok=True)
        self.CONFIG_DIR.mkdir(exist_ok=True)
    
    def validate(self):
        """Validate configuration"""
        errors = []
        
        if not self.DEVICE_ID:
            errors.append("DEVICE_ID is required")
        
        if not self.API_BASE_URL:
            errors.append("API_BASE_URL is required")
        
        if not self.MQTT_BROKER_URL:
            errors.append("MQTT_BROKER_URL is required")
        
        if self.MONITORING_INTERVAL <= 0:
            errors.append("MONITORING_INTERVAL must be positive")
        
        if not (0 <= self.ALERT_THRESHOLD_FULL <= 100):
            errors.append("ALERT_THRESHOLD_FULL must be between 0 and 100")
        
        if not (0 <= self.ALERT_THRESHOLD_OVERFLOW <= 100):
            errors.append("ALERT_THRESHOLD_OVERFLOW must be between 0 and 100")
        
        if self.ALERT_THRESHOLD_OVERFLOW <= self.ALERT_THRESHOLD_FULL:
            errors.append("ALERT_THRESHOLD_OVERFLOW must be greater than ALERT_THRESHOLD_FULL")
        
        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")
        
        return True
    
    def to_dict(self):
        """Convert configuration to dictionary"""
        return {
            'device_id': self.DEVICE_ID,
            'api_base_url': self.API_BASE_URL,
            'mqtt_broker_url': self.MQTT_BROKER_URL,
            'monitoring_interval': self.MONITORING_INTERVAL,
            'bin_height': self.BIN_HEIGHT,
            'bin_capacity': self.BIN_CAPACITY,
            'alert_threshold_full': self.ALERT_THRESHOLD_FULL,
            'alert_threshold_overflow': self.ALERT_THRESHOLD_OVERFLOW,
        }
