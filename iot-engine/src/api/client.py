"""
API client for communicating with Cleancity360 backend
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, Optional

class APIClient:
    """HTTP API client for sending data to backend"""
    
    def __init__(self, base_url: str, device_id: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.device_id = device_id
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.logger = logging.getLogger(__name__)
        
        # API endpoints
        self.sensor_data_endpoint = f"{self.base_url}/bins/sensor/{device_id}/data"
        self.alerts_endpoint = f"{self.base_url}/alerts"
        self.health_endpoint = f"{self.base_url}/health"
        
        # Session will be created when needed
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': f'Cleancity360-IoT/{self.device_id}'
                }
            )
        return self.session
    
    async def send_sensor_data(self, data: Dict) -> bool:
        """Send sensor data to backend API"""
        try:
            session = await self._get_session()
            
            payload = {
                'device_id': self.device_id,
                'timestamp': data.get('timestamp', asyncio.get_event_loop().time()),
                'level': data.get('fill_level'),
                'distance': data.get('distance'),
                'batteryLevel': 85,  # Simulated battery level
                'temperature': 25.0,  # Simulated temperature
                'humidity': 60.0,  # Simulated humidity
                'sensor_status': data.get('sensor_status', 'online'),
                'camera_status': data.get('camera_status', 'online'),
                'waste_detected': data.get('waste_detected', False),
                'overflow_detected': data.get('overflow_detected', False)
            }
            
            async with session.post(self.sensor_data_endpoint, json=payload) as response:
                if response.status == 200:
                    self.logger.debug(f"Sensor data sent successfully: {payload}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Failed to send sensor data. Status: {response.status}, Error: {error_text}")
                    return False
                    
        except asyncio.TimeoutError:
            self.logger.error("Timeout sending sensor data")
            return False
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP client error sending sensor data: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending sensor data: {e}")
            return False
    
    async def send_alert(self, alert: Dict) -> bool:
        """Send alert to backend API"""
        try:
            session = await self._get_session()
            
            # Map IoT alert types to backend alert types
            alert_type_mapping = {
                'BIN_FULL': 'BIN_FULL',
                'BIN_OVERFLOW': 'BIN_OVERFLOW',
                'SENSOR_OFFLINE': 'SENSOR_OFFLINE',
                'ILLEGAL_DUMPING': 'ILLEGAL_DUMPING',
                'MAINTENANCE_REQUIRED': 'MAINTENANCE_REQUIRED'
            }
            
            # Map IoT severity to backend severity
            severity_mapping = {
                'LOW': 'LOW',
                'MEDIUM': 'MEDIUM',
                'HIGH': 'HIGH',
                'CRITICAL': 'CRITICAL'
            }
            
            payload = {
                'type': alert_type_mapping.get(alert.get('type'), 'SYSTEM_ERROR'),
                'severity': severity_mapping.get(alert.get('severity'), 'MEDIUM'),
                'title': f"IoT Alert - {alert.get('type', 'Unknown')}",
                'message': alert.get('message', 'Alert from IoT device'),
                'binId': None  # Will be determined by backend based on sensor ID
            }
            
            async with session.post(self.alerts_endpoint, json=payload) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"Alert sent successfully: {alert['type']}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Failed to send alert. Status: {response.status}, Error: {error_text}")
                    return False
                    
        except asyncio.TimeoutError:
            self.logger.error("Timeout sending alert")
            return False
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP client error sending alert: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending alert: {e}")
            return False
    
    async def check_health(self) -> Dict:
        """Check backend API health"""
        try:
            session = await self._get_session()
            
            async with session.get(self.health_endpoint) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        'status': 'healthy',
                        'response_time': response.headers.get('X-Response-Time'),
                        'data': data
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'status_code': response.status,
                        'error': await response.text()
                    }
                    
        except asyncio.TimeoutError:
            return {
                'status': 'timeout',
                'error': 'Health check timed out'
            }
        except aiohttp.ClientError as e:
            return {
                'status': 'error',
                'error': f'HTTP client error: {e}'
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': f'Unexpected error: {e}'
            }
    
    async def register_device(self) -> bool:
        """Register this IoT device with the backend"""
        try:
            session = await self._get_session()
            
            payload = {
                'device_id': self.device_id,
                'device_type': 'waste_bin_monitor',
                'capabilities': [
                    'ultrasonic_sensor',
                    'camera',
                    'waste_detection'
                ],
                'location': {
                    'latitude': 3.8480,  # Default Yaoundé coordinates
                    'longitude': 11.5021
                },
                'status': 'online'
            }
            
            register_endpoint = f"{self.base_url}/devices/register"
            
            async with session.post(register_endpoint, json=payload) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"Device {self.device_id} registered successfully")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Failed to register device. Status: {response.status}, Error: {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error registering device: {e}")
            return False
    
    async def get_device_config(self) -> Optional[Dict]:
        """Get device configuration from backend"""
        try:
            session = await self._get_session()
            
            config_endpoint = f"{self.base_url}/devices/{self.device_id}/config"
            
            async with session.get(config_endpoint) as response:
                if response.status == 200:
                    config = await response.json()
                    self.logger.info("Device configuration retrieved successfully")
                    return config
                else:
                    self.logger.warning(f"Failed to get device config. Status: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"Error getting device config: {e}")
            return None
    
    async def close(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.logger.info("API client session closed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
