# Mosquitto MQTT Broker Configuration for Cleancity360

# Basic settings
port 1883
listener 9001
protocol websockets

# Persistence
persistence true
persistence_location /mosquitto/data/

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_type error
log_type warning
log_type notice
log_type information
log_timestamp true

# Security (for production, enable authentication)
allow_anonymous true

# Connection settings
max_connections 1000
max_inflight_messages 20
max_queued_messages 100

# Message size limits
message_size_limit 1048576

# Keep alive
keepalive_interval 60

# Cleancity360 specific topics
# Topic pattern: cleancity360/{device_id}/{data_type}
# Examples:
# - cleancity360/device001/sensors
# - cleancity360/device001/alerts
# - cleancity360/device001/status
# - cleancity360/device001/commands
