version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cleancity360-db
    environment:
      POSTGRES_DB: cleancity360
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - cleancity360-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: cleancity360-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cleancity360-network

  # MQTT Broker for IoT communication
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: cleancity360-mqtt
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./iot-engine/config/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    networks:
      - cleancity360-network

  # Backend API
  backend:
    build:
      context: ./admin-portal/backend
      dockerfile: Dockerfile
    container_name: cleancity360-backend
    environment:
      DATABASE_URL: ********************************************/cleancity360
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key
      MQTT_BROKER_URL: mqtt://mosquitto:1883
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
      - mosquitto
    volumes:
      - ./admin-portal/backend:/app
      - /app/node_modules
    networks:
      - cleancity360-network
    command: npm run start:dev

  # Frontend Admin Portal
  frontend:
    build:
      context: ./admin-portal/frontend
      dockerfile: Dockerfile
    container_name: cleancity360-frontend
    environment:
      REACT_APP_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./admin-portal/frontend:/app
      - /app/node_modules
    networks:
      - cleancity360-network
    command: npm run dev

  # IoT Engine
  iot-engine:
    build:
      context: ./iot-engine
      dockerfile: Dockerfile
    container_name: cleancity360-iot
    environment:
      API_BASE_URL: http://backend:3001
      MQTT_BROKER_URL: mqtt://mosquitto:1883
    depends_on:
      - backend
      - mosquitto
    volumes:
      - ./iot-engine:/app
      - /dev:/dev
    privileged: true
    networks:
      - cleancity360-network
    command: python main.py

volumes:
  postgres_data:
  redis_data:
  mosquitto_data:
  mosquitto_logs:

networks:
  cleancity360-network:
    driver: bridge
