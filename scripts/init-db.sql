-- Initialize Cleancity360 Database
-- This script sets up the initial database structure

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create database if it doesn't exist
-- (This is handled by the POSTGRES_DB environment variable)

-- Set timezone
SET timezone = 'Africa/Douala';

-- Create initial admin user (will be handled by Prisma seeding)
-- This file is mainly for database initialization
