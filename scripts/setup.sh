#!/bin/bash

# Cleancity360 Setup Script
# This script sets up the entire development environment

set -e

echo "🚀 Setting up Cleancity360 development environment..."

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.9+ first."
    exit 1
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ All prerequisites are installed!"

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Setup backend
echo "🔧 Setting up backend..."
cd admin-portal/backend
npm install
npx prisma generate
cd ../..

# Setup frontend
echo "🎨 Setting up frontend..."
cd admin-portal/frontend
npm install
cd ../..

# Setup mobile app
echo "📱 Setting up mobile app..."
cd mobile-app
npm install
cd ..

# Setup IoT engine
echo "🔌 Setting up IoT engine..."
cd iot-engine
pip3 install -r requirements.txt
cd ..

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis mosquitto

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🗄️ Running database migrations..."
cd admin-portal/backend
npx prisma migrate dev --name init
npx prisma db seed
cd ../..

echo "🎉 Setup complete!"
echo ""
echo "🚀 To start the development environment:"
echo "   npm run dev"
echo ""
echo "🌐 Access points:"
echo "   - Admin Portal: http://localhost:3000"
echo "   - API Documentation: http://localhost:3001/api"
echo "   - Database Studio: npm run db:studio"
echo ""
echo "📱 For mobile development:"
echo "   cd mobile-app && npm start"
