import asyncio
from src.sensors.ultrasonic import UltrasonicSensor
from src.camera.detector import WasteDetector
from src.transmit.api_client import APIClient

async def main():
    sensor = UltrasonicSensor(trigger_pin=18, echo_pin=24)
    detector = WasteDetector(model_path="models/waste_yolo.pt")
    api_client = APIClient(base_url="http://api.cleancity360.cm")
    
    while True:
        # Monitor bin level
        level = sensor.get_fill_level()
        
        # Detect overflow/illegal dumping
        frame = detector.capture_frame()
        detection = detector.analyze_frame(frame)
        
        # Send alerts if needed
        if level > 0.8 or detection.has_overflow:
            await api_client.send_alert({
                "bin_id": "bin_001",
                "level": level,
                "detection": detection.to_dict()
            })
        
        await asyncio.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    asyncio.run(main())