model User {
  id        String   @id @default(cuid())
  phone     String   @unique
  name      String
  role      Role     @default(CITIZEN)
  pickups   Pickup[]
  reports   Report[]
  createdAt DateTime @default(now())
}

model Pickup {
  id        String       @id @default(cuid())
  userId    String
  location  String
  status    PickupStatus @default(PENDING)
  user      User         @relation(fields: [userId], references: [id])
  createdAt DateTime     @default(now())
}

model Bin {
  id       String    @id @default(cuid())
  location String
  level    Float     @default(0)
  status   BinStatus @default(NORMAL)
  alerts   Alert[]
}

enum Role {
  CITIZEN
  ADMIN
  DRIVER
}

enum PickupStatus {
  PENDING
  ASSIGNED
  COMPLETED
}

enum BinStatus {
  NORMAL
  FULL
  OVERFLOW
  DAMAGED
}