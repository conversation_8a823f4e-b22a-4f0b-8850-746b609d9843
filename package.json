{"name": "cleancity360", "version": "1.0.0", "description": "Comprehensive waste management solution for urban sanitation in Cameroon", "private": true, "workspaces": ["admin-portal/backend", "admin-portal/frontend"], "scripts": {"setup": "npm install && npm run setup:backend && npm run setup:frontend && npm run setup:mobile && npm run setup:iot", "setup:backend": "cd admin-portal/backend && npm install && npx prisma generate", "setup:frontend": "cd admin-portal/frontend && npm install", "setup:mobile": "cd mobile-app && npm install", "setup:iot": "cd iot-engine && pip install -r requirements.txt", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:mobile\"", "dev:backend": "cd admin-portal/backend && npm run start:dev", "dev:frontend": "cd admin-portal/frontend && npm run dev", "dev:mobile": "cd mobile-app && npm start", "build": "npm run build:backend && npm run build:frontend && npm run build:mobile", "build:backend": "cd admin-portal/backend && npm run build", "build:frontend": "cd admin-portal/frontend && npm run build", "build:mobile": "cd mobile-app && npm run build", "test": "npm run test:backend && npm run test:frontend && npm run test:mobile", "test:backend": "cd admin-portal/backend && npm run test", "test:frontend": "cd admin-portal/frontend && npm run test", "test:mobile": "cd mobile-app && npm run test", "lint": "npm run lint:backend && npm run lint:frontend && npm run lint:mobile", "lint:backend": "cd admin-portal/backend && npm run lint", "lint:frontend": "cd admin-portal/frontend && npm run lint", "lint:mobile": "cd mobile-app && npm run lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:migrate": "cd admin-portal/backend && npx prisma migrate dev", "db:seed": "cd admin-portal/backend && npx prisma db seed", "db:studio": "cd admin-portal/backend && npx prisma studio"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/cleancity360.git"}, "keywords": ["waste-management", "iot", "react-native", "<PERSON><PERSON><PERSON>", "cameroon", "urban-sanitation"], "author": "Cleancity360 Team", "license": "MIT"}