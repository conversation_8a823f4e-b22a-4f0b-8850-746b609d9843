{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nexport function isIOS() {\n  return Platform.OS === 'ios';\n}", "map": {"version": 3, "names": ["isIOS", "Platform", "OS"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/utils/platform.js"], "sourcesContent": ["import { Platform } from 'react-native';\nexport function isIOS() {\n    return Platform.OS === 'ios';\n}\n"], "mappings": ";AACA,OAAO,SAASA,KAAKA,CAAA,EAAG;EACpB,OAAOC,QAAQ,CAACC,EAAE,KAAK,KAAK;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}