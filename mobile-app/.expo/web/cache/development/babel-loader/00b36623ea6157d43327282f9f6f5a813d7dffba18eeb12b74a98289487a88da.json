{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\nimport useLatestCallback from 'use-latest-callback';\nimport useNavigation from \"./useNavigation\";\nimport usePreventRemoveContext from \"./usePreventRemoveContext\";\nimport useRoute from \"./useRoute\";\nexport default function usePreventRemove(preventRemove, callback) {\n  var _React$useState = React.useState(function () {\n      return nanoid();\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  var navigation = useNavigation();\n  var _useRoute = useRoute(),\n    routeKey = _useRoute.key;\n  var _usePreventRemoveCont = usePreventRemoveContext(),\n    setPreventRemove = _usePreventRemoveCont.setPreventRemove;\n  React.useEffect(function () {\n    setPreventRemove(id, routeKey, preventRemove);\n    return function () {\n      setPreventRemove(id, routeKey, false);\n    };\n  }, [setPreventRemove, id, routeKey, preventRemove]);\n  var beforeRemoveListener = useLatestCallback(function (e) {\n    if (!preventRemove) {\n      return;\n    }\n    e.preventDefault();\n    callback({\n      data: e.data\n    });\n  });\n  React.useEffect(function () {\n    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('beforeRemove', beforeRemoveListener);\n  }, [navigation, beforeRemoveListener]);\n}", "map": {"version": 3, "names": ["nanoid", "React", "useLatestCallback", "useNavigation", "usePreventRemoveContext", "useRoute", "usePreventRemove", "preventRemove", "callback", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "id", "navigation", "_useRoute", "routeKey", "key", "_usePreventRemoveCont", "setPreventRemove", "useEffect", "beforeRemoveListener", "e", "preventDefault", "data", "addListener"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/usePreventRemove.tsx"], "sourcesContent": ["import type { NavigationAction } from '@react-navigation/routers';\nimport { nanoid } from 'nanoid/non-secure';\nimport * as React from 'react';\nimport useLatestCallback from 'use-latest-callback';\n\nimport type { EventListenerCallback, EventMapCore } from './types';\nimport useNavigation from './useNavigation';\nimport usePreventRemoveContext from './usePreventRemoveContext';\nimport useRoute from './useRoute';\n\n/**\n * Hook to prevent screen from being removed. Can be used to prevent users from leaving the screen.\n *\n * @param preventRemove Boolean indicating whether to prevent screen from being removed.\n * @param callback Function which is executed when screen was prevented from being removed.\n */\nexport default function usePreventRemove(\n  preventRemove: boolean,\n  callback: (options: { data: { action: NavigationAction } }) => void\n) {\n  const [id] = React.useState(() => nanoid());\n\n  const navigation = useNavigation();\n  const { key: routeKey } = useRoute();\n\n  const { setPreventRemove } = usePreventRemoveContext();\n\n  React.useEffect(() => {\n    setPreventRemove(id, routeKey, preventRemove);\n    return () => {\n      setPreventRemove(id, routeKey, false);\n    };\n  }, [setPreventRemove, id, routeKey, preventRemove]);\n\n  const beforeRemoveListener = useLatestCallback<\n    EventListenerCallback<EventMapCore<any>, 'beforeRemove'>\n  >((e) => {\n    if (!preventRemove) {\n      return;\n    }\n\n    e.preventDefault();\n\n    callback({ data: e.data });\n  });\n\n  React.useEffect(\n    () => navigation?.addListener('beforeRemove', beforeRemoveListener),\n    [navigation, beforeRemoveListener]\n  );\n}\n"], "mappings": ";AACA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD,OAAOC,aAAa;AACpB,OAAOC,uBAAuB;AAC9B,OAAOC,QAAQ;AAQf,eAAe,SAASC,gBAAgBA,CACtCC,aAAsB,EACtBC,QAAmE,EACnE;EACA,IAAAC,eAAA,GAAaR,KAAK,CAACS,QAAQ,CAAC;MAAA,OAAMV,MAAM,EAAE;IAAA,EAAC;IAAAW,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAApCI,EAAE,GAAAF,gBAAA;EAET,IAAMG,UAAU,GAAGX,aAAa,EAAE;EAClC,IAAAY,SAAA,GAA0BV,QAAQ,EAAE;IAAvBW,QAAA,GAAAD,SAAA,CAALE,GAAG;EAEX,IAAAC,qBAAA,GAA6Bd,uBAAuB,EAAE;IAA9Ce,gBAAA,GAAAD,qBAAA,CAAAC,gBAAA;EAERlB,KAAK,CAACmB,SAAS,CAAC,YAAM;IACpBD,gBAAgB,CAACN,EAAE,EAAEG,QAAQ,EAAET,aAAa,CAAC;IAC7C,OAAO,YAAM;MACXY,gBAAgB,CAACN,EAAE,EAAEG,QAAQ,EAAE,KAAK,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACG,gBAAgB,EAAEN,EAAE,EAAEG,QAAQ,EAAET,aAAa,CAAC,CAAC;EAEnD,IAAMc,oBAAoB,GAAGnB,iBAAiB,CAE3C,UAAAoB,CAAC,EAAK;IACP,IAAI,CAACf,aAAa,EAAE;MAClB;IACF;IAEAe,CAAC,CAACC,cAAc,EAAE;IAElBf,QAAQ,CAAC;MAAEgB,IAAI,EAAEF,CAAC,CAACE;IAAK,CAAC,CAAC;EAC5B,CAAC,CAAC;EAEFvB,KAAK,CAACmB,SAAS,CACb;IAAA,OAAMN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEW,WAAW,CAAC,cAAc,EAAEJ,oBAAoB,CAAC;EAAA,GACnE,CAACP,UAAU,EAAEO,oBAAoB,CAAC,CACnC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}