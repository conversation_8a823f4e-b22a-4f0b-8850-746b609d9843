{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React from 'react';\nimport Keyboard from \"react-native-web/dist/exports/Keyboard\";\nimport { isIOS } from \"../utils/platform\";\nexport function useKeyboard() {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardHeight = _React$useState2[0],\n    setKeyboardHeight = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    isKeyboardVisible = _React$useState4[0],\n    setIsKeyboardVisible = _React$useState4[1];\n  var onShow = React.useCallback(function (event) {\n    var height = event.endCoordinates.height;\n    setKeyboardHeight(height);\n    setIsKeyboardVisible(true);\n  }, []);\n  var onHide = React.useCallback(function () {\n    setKeyboardHeight(0);\n    setIsKeyboardVisible(false);\n  }, []);\n  React.useEffect(function () {\n    if (!isIOS()) {\n      return function () {};\n    }\n    var didShowListener = Keyboard.addListener('keyboardDidShow', onShow);\n    var didHideListener = Keyboard.addListener('keyboardDidHide', onHide);\n    return function () {\n      didShowListener.remove();\n      didHideListener.remove();\n    };\n  }, [onHide, onShow]);\n  return {\n    keyboardHeight: keyboardHeight,\n    isKeyboardVisible: isKeyboardVisible\n  };\n}", "map": {"version": 3, "names": ["React", "Keyboard", "isIOS", "useKeyboard", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "keyboardHeight", "setKeyboardHeight", "_React$useState3", "_React$useState4", "isKeyboardVisible", "setIsKeyboardVisible", "onShow", "useCallback", "event", "height", "endCoordinates", "onHide", "useEffect", "didShowListener", "addListener", "didHideListener", "remove"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/hooks/useKeyboard.js"], "sourcesContent": ["import React from 'react';\nimport { Keyboard } from 'react-native';\nimport { isIOS } from '../utils/platform';\nexport function useKeyboard() {\n    const [keyboardHeight, setKeyboardHeight] = React.useState(0);\n    const [isKeyboardVisible, setIsKeyboardVisible] = React.useState(false);\n    const onShow = React.useCallback((event) => {\n        const { height } = event.endCoordinates;\n        setKeyboardHeight(height);\n        setIsKeyboardVisible(true);\n    }, []);\n    const onHide = React.useCallback(() => {\n        setKeyboardHeight(0);\n        setIsKeyboardVisible(false);\n    }, []);\n    React.useEffect(() => {\n        if (!isIOS()) {\n            return () => { };\n        }\n        const didShowListener = Keyboard.addListener('keyboardDidShow', onShow);\n        const didHideListener = Keyboard.addListener('keyboardDidHide', onHide);\n        return () => {\n            didShowListener.remove();\n            didHideListener.remove();\n        };\n    }, [onHide, onShow]);\n    return {\n        keyboardHeight,\n        isKeyboardVisible\n    };\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,QAAA;AAE1B,SAASC,KAAK;AACd,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC1B,IAAAC,eAAA,GAA4CJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAtDI,cAAc,GAAAF,gBAAA;IAAEG,iBAAiB,GAAAH,gBAAA;EACxC,IAAAI,gBAAA,GAAkDV,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAAhEE,iBAAiB,GAAAD,gBAAA;IAAEE,oBAAoB,GAAAF,gBAAA;EAC9C,IAAMG,MAAM,GAAGd,KAAK,CAACe,WAAW,CAAC,UAACC,KAAK,EAAK;IACxC,IAAQC,MAAM,GAAKD,KAAK,CAACE,cAAc,CAA/BD,MAAM;IACdR,iBAAiB,CAACQ,MAAM,CAAC;IACzBJ,oBAAoB,CAAC,IAAI,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EACN,IAAMM,MAAM,GAAGnB,KAAK,CAACe,WAAW,CAAC,YAAM;IACnCN,iBAAiB,CAAC,CAAC,CAAC;IACpBI,oBAAoB,CAAC,KAAK,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACNb,KAAK,CAACoB,SAAS,CAAC,YAAM;IAClB,IAAI,CAAClB,KAAK,CAAC,CAAC,EAAE;MACV,OAAO,YAAM,CAAE,CAAC;IACpB;IACA,IAAMmB,eAAe,GAAGpB,QAAQ,CAACqB,WAAW,CAAC,iBAAiB,EAAER,MAAM,CAAC;IACvE,IAAMS,eAAe,GAAGtB,QAAQ,CAACqB,WAAW,CAAC,iBAAiB,EAAEH,MAAM,CAAC;IACvE,OAAO,YAAM;MACTE,eAAe,CAACG,MAAM,CAAC,CAAC;MACxBD,eAAe,CAACC,MAAM,CAAC,CAAC;IAC5B,CAAC;EACL,CAAC,EAAE,CAACL,MAAM,EAAEL,MAAM,CAAC,CAAC;EACpB,OAAO;IACHN,cAAc,EAAdA,cAAc;IACdI,iBAAiB,EAAjBA;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}