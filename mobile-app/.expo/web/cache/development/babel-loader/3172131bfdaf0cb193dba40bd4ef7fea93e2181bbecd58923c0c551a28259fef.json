{"ast": null, "code": "import React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { additiveInverseArray } from \"../utils/array\";\nimport { useKeyboard } from \"./useKeyboard\";\nexport function translateYOutputRangeFor(_ref) {\n  var position = _ref.position,\n    height = _ref.height,\n    topOffset = _ref.topOffset,\n    bottomOffset = _ref.bottomOffset,\n    keyboardHeight = _ref.keyboardHeight,\n    keyboardOffset = _ref.keyboardOffset,\n    avoidKeyboard = _ref.avoidKeyboard;\n  var offset = position === 'bottom' ? bottomOffset : topOffset;\n  var keyboardAwareOffset = position === 'bottom' && avoidKeyboard ? keyboardHeight + keyboardOffset : 0;\n  var range = [-(height * 2), Math.max(offset, keyboardAwareOffset)];\n  var outputRange = position === 'bottom' ? additiveInverseArray(range) : range;\n  return outputRange;\n}\nvar useNativeDriver = Platform.select({\n  ios: true,\n  default: false\n});\nexport function useSlideAnimation(_ref2) {\n  var position = _ref2.position,\n    height = _ref2.height,\n    topOffset = _ref2.topOffset,\n    bottomOffset = _ref2.bottomOffset,\n    keyboardOffset = _ref2.keyboardOffset,\n    avoidKeyboard = _ref2.avoidKeyboard;\n  var animatedValue = React.useRef(new Animated.Value(0));\n  var _useKeyboard = useKeyboard(),\n    keyboardHeight = _useKeyboard.keyboardHeight;\n  var animate = React.useCallback(function (toValue) {\n    Animated.spring(animatedValue.current, {\n      toValue: toValue,\n      useNativeDriver: useNativeDriver,\n      friction: 8\n    }).start();\n  }, []);\n  var translateY = React.useMemo(function () {\n    return animatedValue.current.interpolate({\n      inputRange: [0, 1],\n      outputRange: translateYOutputRangeFor({\n        position: position,\n        height: height,\n        topOffset: topOffset,\n        bottomOffset: bottomOffset,\n        keyboardHeight: keyboardHeight,\n        keyboardOffset: keyboardOffset,\n        avoidKeyboard: avoidKeyboard\n      })\n    });\n  }, [position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset, avoidKeyboard]);\n  var opacity = animatedValue.current.interpolate({\n    inputRange: [0, 0.7, 1],\n    outputRange: [0, 1, 1]\n  });\n  return {\n    animatedValue: animatedValue,\n    animate: animate,\n    animationStyles: {\n      opacity: opacity,\n      transform: [{\n        translateY: translateY\n      }]\n    }\n  };\n}", "map": {"version": 3, "names": ["React", "Animated", "Platform", "additiveInverseArray", "useKeyboard", "translateYOutputRangeFor", "_ref", "position", "height", "topOffset", "bottomOffset", "keyboardHeight", "keyboardOffset", "avoidKeyboard", "offset", "keyboardAwareOffset", "range", "Math", "max", "outputRange", "useNativeDriver", "select", "ios", "default", "useSlideAnimation", "_ref2", "animatedValue", "useRef", "Value", "_useKeyboard", "animate", "useCallback", "toValue", "spring", "current", "friction", "start", "translateY", "useMemo", "interpolate", "inputRange", "opacity", "animationStyles", "transform"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/hooks/useSlideAnimation.js"], "sourcesContent": ["import React from 'react';\nimport { Animated, Platform } from 'react-native';\nimport { additiveInverseArray } from '../utils/array';\nimport { useKeyboard } from './useKeyboard';\nexport function translateYOutputRangeFor({ position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset, avoidKeyboard }) {\n    const offset = position === 'bottom' ? bottomOffset : topOffset;\n    const keyboardAwareOffset = position === 'bottom' && avoidKeyboard ? keyboardHeight + keyboardOffset : 0;\n    const range = [-(height * 2), Math.max(offset, keyboardAwareOffset)];\n    const outputRange = position === 'bottom' ? additiveInverseArray(range) : range;\n    return outputRange;\n}\nconst useNativeDriver = Platform.select({\n    ios: true,\n    default: false\n});\nexport function useSlideAnimation({ position, height, topOffset, bottomOffset, keyboardOffset, avoidKeyboard }) {\n    const animatedValue = React.useRef(new Animated.Value(0));\n    const { keyboardHeight } = useKeyboard();\n    const animate = React.useCallback((toValue) => {\n        Animated.spring(animatedValue.current, {\n            toValue,\n            useNativeDriver,\n            friction: 8\n        }).start();\n    }, []);\n    const translateY = React.useMemo(() => animatedValue.current.interpolate({\n        inputRange: [0, 1],\n        outputRange: translateYOutputRangeFor({\n            position,\n            height,\n            topOffset,\n            bottomOffset,\n            keyboardHeight,\n            keyboardOffset,\n            avoidKeyboard\n        })\n    }), [position, height, topOffset, bottomOffset, keyboardHeight, keyboardOffset, avoidKeyboard]);\n    const opacity = animatedValue.current.interpolate({\n        inputRange: [0, 0.7, 1],\n        outputRange: [0, 1, 1]\n    });\n    return {\n        animatedValue,\n        animate,\n        animationStyles: {\n            opacity,\n            transform: [\n                {\n                    translateY\n                }\n            ]\n        }\n    };\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAE1B,SAASC,oBAAoB;AAC7B,SAASC,WAAW;AACpB,OAAO,SAASC,wBAAwBA,CAAAC,IAAA,EAA+F;EAAA,IAA5FC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAEC,MAAM,GAAAF,IAAA,CAANE,MAAM;IAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS;IAAEC,YAAY,GAAAJ,IAAA,CAAZI,YAAY;IAAEC,cAAc,GAAAL,IAAA,CAAdK,cAAc;IAAEC,cAAc,GAAAN,IAAA,CAAdM,cAAc;IAAEC,aAAa,GAAAP,IAAA,CAAbO,aAAa;EAC/H,IAAMC,MAAM,GAAGP,QAAQ,KAAK,QAAQ,GAAGG,YAAY,GAAGD,SAAS;EAC/D,IAAMM,mBAAmB,GAAGR,QAAQ,KAAK,QAAQ,IAAIM,aAAa,GAAGF,cAAc,GAAGC,cAAc,GAAG,CAAC;EACxG,IAAMI,KAAK,GAAG,CAAC,EAAER,MAAM,GAAG,CAAC,CAAC,EAAES,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAEC,mBAAmB,CAAC,CAAC;EACpE,IAAMI,WAAW,GAAGZ,QAAQ,KAAK,QAAQ,GAAGJ,oBAAoB,CAACa,KAAK,CAAC,GAAGA,KAAK;EAC/E,OAAOG,WAAW;AACtB;AACA,IAAMC,eAAe,GAAGlB,QAAQ,CAACmB,MAAM,CAAC;EACpCC,GAAG,EAAE,IAAI;EACTC,OAAO,EAAE;AACb,CAAC,CAAC;AACF,OAAO,SAASC,iBAAiBA,CAAAC,KAAA,EAA+E;EAAA,IAA5ElB,QAAQ,GAAAkB,KAAA,CAARlB,QAAQ;IAAEC,MAAM,GAAAiB,KAAA,CAANjB,MAAM;IAAEC,SAAS,GAAAgB,KAAA,CAAThB,SAAS;IAAEC,YAAY,GAAAe,KAAA,CAAZf,YAAY;IAAEE,cAAc,GAAAa,KAAA,CAAdb,cAAc;IAAEC,aAAa,GAAAY,KAAA,CAAbZ,aAAa;EACxG,IAAMa,aAAa,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI1B,QAAQ,CAAC2B,KAAK,CAAC,CAAC,CAAC,CAAC;EACzD,IAAAC,YAAA,GAA2BzB,WAAW,CAAC,CAAC;IAAhCO,cAAc,GAAAkB,YAAA,CAAdlB,cAAc;EACtB,IAAMmB,OAAO,GAAG9B,KAAK,CAAC+B,WAAW,CAAC,UAACC,OAAO,EAAK;IAC3C/B,QAAQ,CAACgC,MAAM,CAACP,aAAa,CAACQ,OAAO,EAAE;MACnCF,OAAO,EAAPA,OAAO;MACPZ,eAAe,EAAfA,eAAe;MACfe,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EACN,IAAMC,UAAU,GAAGrC,KAAK,CAACsC,OAAO,CAAC;IAAA,OAAMZ,aAAa,CAACQ,OAAO,CAACK,WAAW,CAAC;MACrEC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBrB,WAAW,EAAEd,wBAAwB,CAAC;QAClCE,QAAQ,EAARA,QAAQ;QACRC,MAAM,EAANA,MAAM;QACNC,SAAS,EAATA,SAAS;QACTC,YAAY,EAAZA,YAAY;QACZC,cAAc,EAAdA,cAAc;QACdC,cAAc,EAAdA,cAAc;QACdC,aAAa,EAAbA;MACJ,CAAC;IACL,CAAC,CAAC;EAAA,GAAE,CAACN,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,aAAa,CAAC,CAAC;EAC/F,IAAM4B,OAAO,GAAGf,aAAa,CAACQ,OAAO,CAACK,WAAW,CAAC;IAC9CC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IACvBrB,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC;EACF,OAAO;IACHO,aAAa,EAAbA,aAAa;IACbI,OAAO,EAAPA,OAAO;IACPY,eAAe,EAAE;MACbD,OAAO,EAAPA,OAAO;MACPE,SAAS,EAAE,CACP;QACIN,UAAU,EAAVA;MACJ,CAAC;IAET;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}