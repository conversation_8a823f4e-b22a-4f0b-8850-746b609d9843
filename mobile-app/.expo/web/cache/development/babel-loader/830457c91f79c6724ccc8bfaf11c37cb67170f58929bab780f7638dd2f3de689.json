{"ast": null, "code": "import * as React from 'react';\nvar NavigationHelpersContext = React.createContext(undefined);\nexport default NavigationHelpersContext;", "map": {"version": 3, "names": ["React", "NavigationHelpersContext", "createContext", "undefined"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/NavigationHelpersContext.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { NavigationHelpers } from './types';\n\n/**\n * Context which holds the navigation helpers of the parent navigator.\n * Navigators should use this context in their view component.\n */\nconst NavigationHelpersContext = React.createContext<\n  NavigationHelpers<ParamListBase> | undefined\n>(undefined);\n\nexport default NavigationHelpersContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAQ9B,IAAMC,wBAAwB,GAAGD,KAAK,CAACE,aAAa,CAElDC,SAAS,CAAC;AAEZ,eAAeF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}