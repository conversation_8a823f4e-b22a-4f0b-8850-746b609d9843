{"ast": null, "code": "import * as React from 'react';\nvar MISSING_CONTEXT_ERROR = \"Couldn't find a schedule context.\";\nexport var ScheduleUpdateContext = React.createContext({\n  scheduleUpdate: function scheduleUpdate() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  flushUpdates: function flushUpdates() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  }\n});\nexport default function useScheduleUpdate(callback) {\n  var _React$useContext = React.useContext(ScheduleUpdateContext),\n    scheduleUpdate = _React$useContext.scheduleUpdate,\n    flushUpdates = _React$useContext.flushUpdates;\n  scheduleUpdate(callback);\n  React.useEffect(flushUpdates);\n}", "map": {"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "ScheduleUpdateContext", "createContext", "scheduleUpdate", "Error", "flushUpdates", "useScheduleUpdate", "callback", "_React$useContext", "useContext", "useEffect"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/useScheduleUpdate.tsx"], "sourcesContent": ["import * as React from 'react';\n\nconst MISSING_CONTEXT_ERROR = \"Couldn't find a schedule context.\";\n\nexport const ScheduleUpdateContext = React.createContext<{\n  scheduleUpdate: (callback: () => void) => void;\n  flushUpdates: () => void;\n}>({\n  scheduleUpdate() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n  flushUpdates() {\n    throw new Error(MISSING_CONTEXT_ERROR);\n  },\n});\n\n/**\n * When screen config changes, we want to update the navigator in the same update phase.\n * However, navigation state is in the root component and React won't let us update it from a child.\n * This is a workaround for that, the scheduled update is stored in the ref without actually calling setState.\n * It lets all subsequent updates access the latest state so it stays correct.\n * Then we call setState during after the component updates.\n */\nexport default function useScheduleUpdate(callback: () => void) {\n  const { scheduleUpdate, flushUpdates } = React.useContext(\n    ScheduleUpdateContext\n  );\n\n  scheduleUpdate(callback);\n\n  React.useEffect(flushUpdates);\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAMC,qBAAqB,GAAG,mCAAmC;AAEjE,OAAO,IAAMC,qBAAqB,GAAGF,KAAK,CAACG,aAAa,CAGrD;EACDC,cAAc,WAAdA,cAAcA,CAAA,EAAG;IACf,MAAM,IAAIC,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACDK,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,MAAM,IAAID,KAAK,CAACJ,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC;AASF,eAAe,SAASM,iBAAiBA,CAACC,QAAoB,EAAE;EAC9D,IAAAC,iBAAA,GAAyCT,KAAK,CAACU,UAAU,CACvDR,qBAAqB,CACtB;IAFOE,cAAc,GAAAK,iBAAA,CAAdL,cAAc;IAAEE,YAAA,GAAAG,iBAAA,CAAAH,YAAA;EAIxBF,cAAc,CAACI,QAAQ,CAAC;EAExBR,KAAK,CAACW,SAAS,CAACL,YAAY,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}