{"ast": null, "code": "import * as React from 'react';\nimport CurrentRenderContext from \"./CurrentRenderContext\";\nexport default function useCurrentRender(_ref) {\n  var state = _ref.state,\n    navigation = _ref.navigation,\n    descriptors = _ref.descriptors;\n  var current = React.useContext(CurrentRenderContext);\n  if (current && navigation.isFocused()) {\n    current.options = descriptors[state.routes[state.index].key].options;\n  }\n}", "map": {"version": 3, "names": ["React", "CurrentRenderContext", "useCurrentRender", "_ref", "state", "navigation", "descriptors", "current", "useContext", "isFocused", "options", "routes", "index", "key"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/useCurrentRender.tsx"], "sourcesContent": ["import type { NavigationState, ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport CurrentRenderContext from './CurrentRenderContext';\nimport type {\n  Descriptor,\n  NavigationHelpers,\n  NavigationProp,\n  RouteProp,\n} from './types';\n\ntype Options = {\n  state: NavigationState;\n  navigation: NavigationHelpers<ParamListBase>;\n  descriptors: Record<\n    string,\n    Descriptor<object, NavigationProp<ParamListBase>, RouteProp<ParamListBase>>\n  >;\n};\n\n/**\n * Write the current options, so that server renderer can get current values\n * Mutating values like this is not safe in async mode, but it doesn't apply to SSR\n */\nexport default function useCurrentRender({\n  state,\n  navigation,\n  descriptors,\n}: Options) {\n  const current = React.useContext(CurrentRenderContext);\n\n  if (current && navigation.isFocused()) {\n    current.options = descriptors[state.routes[state.index].key].options;\n  }\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB;AAqB3B,eAAe,SAASC,gBAAgBA,CAAAC,IAAA,EAI5B;EAAA,IAHVC,KAAK,GAGGD,IAAA,CAHRC,KAAK;IACLC,UAAU,GAEFF,IAAA,CAFRE,UAAU;IACVC,WAAA,GACQH,IAAA,CADRG,WAAA;EAEA,IAAMC,OAAO,GAAGP,KAAK,CAACQ,UAAU,CAACP,oBAAoB,CAAC;EAEtD,IAAIM,OAAO,IAAIF,UAAU,CAACI,SAAS,EAAE,EAAE;IACrCF,OAAO,CAACG,OAAO,GAAGJ,WAAW,CAACF,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAACC,GAAG,CAAC,CAACH,OAAO;EACtE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}