{"ast": null, "code": "import getNamedContext from \"../getNamedContext\";\nvar HeaderShownContext = getNamedContext('HeaderShownContext', false);\nexport default HeaderShownContext;", "map": {"version": 3, "names": ["getNamedContext", "HeaderShownContext"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/elements/src/Header/HeaderShownContext.tsx"], "sourcesContent": ["import getNamedContext from '../getNamedContext';\n\nconst HeaderShownContext = getNamedContext('HeaderShownContext', false);\n\nexport default HeaderShownContext;\n"], "mappings": "AAAA,OAAOA,eAAe;AAEtB,IAAMC,kBAAkB,GAAGD,eAAe,CAAC,oBAAoB,EAAE,KAAK,CAAC;AAEvE,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}