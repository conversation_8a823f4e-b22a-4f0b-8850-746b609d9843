{"ast": null, "code": "import * as React from 'react';\nimport createNavigationContainerRef from \"./createNavigationContainerRef\";\nexport default function useNavigationContainerRef() {\n  var navigation = React.useRef(null);\n  if (navigation.current == null) {\n    navigation.current = createNavigationContainerRef();\n  }\n  return navigation.current;\n}", "map": {"version": 3, "names": ["React", "createNavigationContainerRef", "useNavigationContainerRef", "navigation", "useRef", "current"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/useNavigationContainerRef.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport createNavigationContainerRef from './createNavigationContainerRef';\nimport type { NavigationContainerRefWithCurrent } from './types';\n\nexport default function useNavigationContainerRef<\n  ParamList extends {} = ReactNavigation.RootParamList\n>(): NavigationContainerRefWithCurrent<ParamList> {\n  const navigation =\n    React.useRef<NavigationContainerRefWithCurrent<ParamList> | null>(null);\n\n  if (navigation.current == null) {\n    navigation.current = createNavigationContainerRef<ParamList>();\n  }\n\n  return navigation.current;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,4BAA4B;AAGnC,eAAe,SAASC,yBAAyBA,CAAA,EAEC;EAChD,IAAMC,UAAU,GACdH,KAAK,CAACI,MAAM,CAAsD,IAAI,CAAC;EAEzE,IAAID,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;IAC9BF,UAAU,CAACE,OAAO,GAAGJ,4BAA4B,EAAa;EAChE;EAEA,OAAOE,UAAU,CAACE,OAAO;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}