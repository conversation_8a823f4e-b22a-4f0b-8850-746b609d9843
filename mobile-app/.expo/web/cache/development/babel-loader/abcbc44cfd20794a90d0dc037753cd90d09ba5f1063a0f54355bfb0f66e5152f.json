{"ast": null, "code": "export default function checkDuplicateRouteNames(state) {\n  var duplicates = [];\n  var _getRouteNames = function getRouteNames(location, state) {\n    state.routes.forEach(function (route) {\n      var _route$state, _route$state$routeNam;\n      var currentLocation = location ? `${location} > ${route.name}` : route.name;\n      (_route$state = route.state) === null || _route$state === void 0 ? void 0 : (_route$state$routeNam = _route$state.routeNames) === null || _route$state$routeNam === void 0 ? void 0 : _route$state$routeNam.forEach(function (routeName) {\n        if (routeName === route.name) {\n          duplicates.push([currentLocation, `${currentLocation} > ${route.name}`]);\n        }\n      });\n      if (route.state) {\n        _getRouteNames(currentLocation, route.state);\n      }\n    });\n  };\n  _getRouteNames('', state);\n  return duplicates;\n}", "map": {"version": 3, "names": ["checkDuplicateRouteNames", "state", "duplicates", "getRouteNames", "location", "routes", "for<PERSON>ach", "route", "_route$state", "_route$state$routeNam", "currentLocation", "name", "routeNames", "routeName", "push"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/checkDuplicateRouteNames.tsx"], "sourcesContent": ["import type { NavigationState, PartialState } from '@react-navigation/routers';\n\nexport default function checkDuplicateRouteNames(state: NavigationState) {\n  const duplicates: string[][] = [];\n\n  const getRouteNames = (\n    location: string,\n    state: NavigationState | PartialState<NavigationState>\n  ) => {\n    state.routes.forEach((route: (typeof state.routes)[0]) => {\n      const currentLocation = location\n        ? `${location} > ${route.name}`\n        : route.name;\n\n      route.state?.routeNames?.forEach((routeName) => {\n        if (routeName === route.name) {\n          duplicates.push([\n            currentLocation,\n            `${currentLocation} > ${route.name}`,\n          ]);\n        }\n      });\n\n      if (route.state) {\n        getRouteNames(currentLocation, route.state);\n      }\n    });\n  };\n\n  getRouteNames('', state);\n\n  return duplicates;\n}\n"], "mappings": "AAEA,eAAe,SAASA,wBAAwBA,CAACC,KAAsB,EAAE;EACvE,IAAMC,UAAsB,GAAG,EAAE;EAEjC,IAAMC,cAAa,GAAG,SAAhBA,aAAaA,CACjBC,QAAgB,EAChBH,KAAsD,EACnD;IACHA,KAAK,CAACI,MAAM,CAACC,OAAO,CAAE,UAAAC,KAA+B,EAAK;MAAA,IAAAC,YAAA,EAAAC,qBAAA;MACxD,IAAMC,eAAe,GAAGN,QAAQ,GAC3B,GAAEA,QAAS,MAAKG,KAAK,CAACI,IAAK,EAAC,GAC7BJ,KAAK,CAACI,IAAI;MAEd,CAAAH,YAAA,GAAAD,KAAK,CAACN,KAAK,cAAAO,YAAA,wBAAAC,qBAAA,GAAXD,YAAA,CAAaI,UAAU,cAAAH,qBAAA,uBAAvBA,qBAAA,CAAyBH,OAAO,CAAE,UAAAO,SAAS,EAAK;QAC9C,IAAIA,SAAS,KAAKN,KAAK,CAACI,IAAI,EAAE;UAC5BT,UAAU,CAACY,IAAI,CAAC,CACdJ,eAAe,EACd,GAAEA,eAAgB,MAAKH,KAAK,CAACI,IAAK,EAAC,CACrC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAIJ,KAAK,CAACN,KAAK,EAAE;QACfE,cAAa,CAACO,eAAe,EAAEH,KAAK,CAACN,KAAK,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EAEDE,cAAa,CAAC,EAAE,EAAEF,KAAK,CAAC;EAExB,OAAOC,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}