{"ast": null, "code": "import StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport var styles = StyleSheet.create({\n  base: {\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  top: {\n    top: 0\n  },\n  bottom: {\n    bottom: 0\n  }\n});", "map": {"version": 3, "names": ["styles", "StyleSheet", "create", "base", "position", "left", "right", "alignItems", "justifyContent", "top", "bottom"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/components/AnimatedContainer.styles.js"], "sourcesContent": ["import { StyleSheet } from 'react-native';\nexport const styles = StyleSheet.create({\n    base: {\n        position: 'absolute',\n        left: 0,\n        right: 0,\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    top: {\n        top: 0\n    },\n    bottom: {\n        bottom: 0\n    }\n});\n"], "mappings": ";AACA,OAAO,IAAMA,MAAM,GAAGC,UAAU,CAACC,MAAM,CAAC;EACpCC,IAAI,EAAE;IACFC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EACpB,CAAC;EACDC,GAAG,EAAE;IACDA,GAAG,EAAE;EACT,CAAC;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE;EACZ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}