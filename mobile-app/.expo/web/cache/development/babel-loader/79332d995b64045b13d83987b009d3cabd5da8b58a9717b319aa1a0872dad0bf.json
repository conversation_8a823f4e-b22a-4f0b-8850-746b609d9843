{"ast": null, "code": "import React from 'react';\nimport { useLogger } from \"../contexts\";\nfunction useTimeout(cb) {\n  var delayMs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var _useLogger = useLogger(),\n    log = _useLogger.log;\n  var ref = React.useRef();\n  var clearTimer = React.useCallback(function () {\n    if (ref.current) {\n      log('Clearing timer', ref.current);\n      clearTimeout(ref.current);\n      ref.current = undefined;\n    }\n  }, [log]);\n  var startTimer = React.useCallback(function () {\n    clearTimer();\n    ref.current = setTimeout(function () {\n      cb();\n      log('Running timer', ref.current);\n      ref.current = undefined;\n    }, delayMs);\n    log('Starting timer', ref.current);\n  }, [clearTimer, delayMs, log, cb]);\n  React.useEffect(function () {\n    return function () {\n      return clearTimer();\n    };\n  }, [clearTimer]);\n  return {\n    startTimer: startTimer,\n    clearTimer: clearTimer,\n    isActive: ref.current !== undefined\n  };\n}\nexport { useTimeout };", "map": {"version": 3, "names": ["React", "useLogger", "useTimeout", "cb", "delayMs", "arguments", "length", "undefined", "_useLogger", "log", "ref", "useRef", "clearTimer", "useCallback", "current", "clearTimeout", "startTimer", "setTimeout", "useEffect", "isActive"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/hooks/useTimeout.js"], "sourcesContent": ["import React from 'react';\nimport { useLogger } from '../contexts';\nfunction useTimeout(cb, delayMs = 0) {\n    const { log } = useLogger();\n    const ref = React.useRef();\n    const clearTimer = React.useCallback(() => {\n        if (ref.current) {\n            log('Clearing timer', ref.current);\n            clearTimeout(ref.current);\n            ref.current = undefined;\n        }\n    }, [log]);\n    const startTimer = React.useCallback(() => {\n        clearTimer();\n        ref.current = setTimeout(() => {\n            cb();\n            log('Running timer', ref.current);\n            ref.current = undefined;\n        }, delayMs);\n        log('Starting timer', ref.current);\n    }, [clearTimer, delayMs, log, cb]);\n    React.useEffect(() => () => clearTimer(), [clearTimer]);\n    return {\n        startTimer,\n        clearTimer,\n        isActive: ref.current !== undefined\n    };\n}\nexport { useTimeout };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS;AAClB,SAASC,UAAUA,CAACC,EAAE,EAAe;EAAA,IAAbC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC/B,IAAAG,UAAA,GAAgBP,SAAS,CAAC,CAAC;IAAnBQ,GAAG,GAAAD,UAAA,CAAHC,GAAG;EACX,IAAMC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,CAAC;EAC1B,IAAMC,UAAU,GAAGZ,KAAK,CAACa,WAAW,CAAC,YAAM;IACvC,IAAIH,GAAG,CAACI,OAAO,EAAE;MACbL,GAAG,CAAC,gBAAgB,EAAEC,GAAG,CAACI,OAAO,CAAC;MAClCC,YAAY,CAACL,GAAG,CAACI,OAAO,CAAC;MACzBJ,GAAG,CAACI,OAAO,GAAGP,SAAS;IAC3B;EACJ,CAAC,EAAE,CAACE,GAAG,CAAC,CAAC;EACT,IAAMO,UAAU,GAAGhB,KAAK,CAACa,WAAW,CAAC,YAAM;IACvCD,UAAU,CAAC,CAAC;IACZF,GAAG,CAACI,OAAO,GAAGG,UAAU,CAAC,YAAM;MAC3Bd,EAAE,CAAC,CAAC;MACJM,GAAG,CAAC,eAAe,EAAEC,GAAG,CAACI,OAAO,CAAC;MACjCJ,GAAG,CAACI,OAAO,GAAGP,SAAS;IAC3B,CAAC,EAAEH,OAAO,CAAC;IACXK,GAAG,CAAC,gBAAgB,EAAEC,GAAG,CAACI,OAAO,CAAC;EACtC,CAAC,EAAE,CAACF,UAAU,EAAER,OAAO,EAAEK,GAAG,EAAEN,EAAE,CAAC,CAAC;EAClCH,KAAK,CAACkB,SAAS,CAAC;IAAA,OAAM;MAAA,OAAMN,UAAU,CAAC,CAAC;IAAA;EAAA,GAAE,CAACA,UAAU,CAAC,CAAC;EACvD,OAAO;IACHI,UAAU,EAAVA,UAAU;IACVJ,UAAU,EAAVA,UAAU;IACVO,QAAQ,EAAET,GAAG,CAACI,OAAO,KAAKP;EAC9B,CAAC;AACL;AACA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}