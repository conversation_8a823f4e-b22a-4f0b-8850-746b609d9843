{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"state\"];\nimport * as React from 'react';\nexport var CHILD_STATE = Symbol('CHILD_STATE');\nexport default function useRouteCache(routes) {\n  var cache = React.useMemo(function () {\n    return {\n      current: new Map()\n    };\n  }, []);\n  if (process.env.NODE_ENV === 'production') {\n    return routes;\n  }\n  cache.current = routes.reduce(function (acc, route) {\n    var previous = cache.current.get(route);\n    if (previous) {\n      acc.set(route, previous);\n    } else {\n      var state = route.state,\n        proxy = _objectWithoutProperties(route, _excluded);\n      Object.defineProperty(proxy, CHILD_STATE, {\n        enumerable: false,\n        value: state\n      });\n      acc.set(route, proxy);\n    }\n    return acc;\n  }, new Map());\n  return Array.from(cache.current.values());\n}", "map": {"version": 3, "names": ["React", "CHILD_STATE", "Symbol", "useRouteCache", "routes", "cache", "useMemo", "current", "Map", "process", "env", "NODE_ENV", "reduce", "acc", "route", "previous", "get", "set", "state", "proxy", "_objectWithoutProperties", "_excluded", "Object", "defineProperty", "enumerable", "value", "Array", "from", "values"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/useRouteCache.tsx"], "sourcesContent": ["import type {\n  NavigationState,\n  ParamListBase,\n  Route,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport type { RouteProp } from './types';\n\ntype RouteCache = Map<Route<string>, RouteProp<ParamListBase>>;\n\n/**\n * Utilites such as `getFocusedRouteNameFromRoute` need to access state.\n * So we need a way to suppress the warning for those use cases.\n * This is fine since they are internal utilities and this is not public API.\n */\nexport const CHILD_STATE = Symbol('CHILD_STATE');\n\n/**\n * Hook to cache route props for each screen in the navigator.\n * This lets add warnings and modifications to the route object but keep references between renders.\n */\nexport default function useRouteCache<State extends NavigationState>(\n  routes: State['routes']\n) {\n  // Cache object which holds route objects for each screen\n  const cache = React.useMemo(() => ({ current: new Map() as RouteCache }), []);\n\n  if (process.env.NODE_ENV === 'production') {\n    // We don't want the overhead of creating extra maps every render in prod\n    return routes;\n  }\n\n  cache.current = routes.reduce((acc, route) => {\n    const previous = cache.current.get(route);\n\n    if (previous) {\n      // If a cached route object already exists, reuse it\n      acc.set(route, previous);\n    } else {\n      const { state, ...proxy } = route;\n\n      Object.defineProperty(proxy, CHILD_STATE, {\n        enumerable: false,\n        value: state,\n      });\n\n      acc.set(route, proxy);\n    }\n\n    return acc;\n  }, new Map() as RouteCache);\n\n  return Array.from(cache.current.values());\n}\n"], "mappings": ";;AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAW9B,OAAO,IAAMC,WAAW,GAAGC,MAAM,CAAC,aAAa,CAAC;AAMhD,eAAe,SAASC,aAAaA,CACnCC,MAAuB,EACvB;EAEA,IAAMC,KAAK,GAAGL,KAAK,CAACM,OAAO,CAAC;IAAA,OAAO;MAAEC,OAAO,EAAE,IAAIC,GAAG;IAAiB,CAAC;EAAA,CAAC,EAAE,EAAE,CAAC;EAE7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAEzC,OAAOP,MAAM;EACf;EAEAC,KAAK,CAACE,OAAO,GAAGH,MAAM,CAACQ,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAK;IAC5C,IAAMC,QAAQ,GAAGV,KAAK,CAACE,OAAO,CAACS,GAAG,CAACF,KAAK,CAAC;IAEzC,IAAIC,QAAQ,EAAE;MAEZF,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEC,QAAQ,CAAC;IAC1B,CAAC,MAAM;MACL,IAAQG,KAAK,GAAeJ,KAAK,CAAzBI,KAAK;QAAKC,KAAA,GAAAC,wBAAA,CAAUN,KAAK,EAAAO,SAAA;MAEjCC,MAAM,CAACC,cAAc,CAACJ,KAAK,EAAElB,WAAW,EAAE;QACxCuB,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAEP;MACT,CAAC,CAAC;MAEFL,GAAG,CAACI,GAAG,CAACH,KAAK,EAAEK,KAAK,CAAC;IACvB;IAEA,OAAON,GAAG;EACZ,CAAC,EAAE,IAAIL,GAAG,EAAE,CAAe;EAE3B,OAAOkB,KAAK,CAACC,IAAI,CAACtB,KAAK,CAACE,OAAO,CAACqB,MAAM,EAAE,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}