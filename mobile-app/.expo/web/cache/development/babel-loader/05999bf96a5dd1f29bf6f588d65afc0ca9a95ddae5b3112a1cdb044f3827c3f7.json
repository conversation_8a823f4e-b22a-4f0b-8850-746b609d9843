{"ast": null, "code": "import createResponderEvent from \"./createResponderEvent\";\nimport { isCancelish, isEndish, isMoveish, isScroll, isSelectionChange, isStartish } from \"./ResponderEventTypes\";\nimport { getLowestCommonAncestor, getResponderPaths, hasTargetTouches, hasValidSelection, isPrimaryPointerDown, setResponderId } from \"./utils\";\nimport { ResponderTouchHistoryStore } from \"./ResponderTouchHistoryStore\";\nimport canUseDOM from \"../canUseDom\";\nvar emptyObject = {};\nvar startRegistration = ['onStartShouldSetResponderCapture', 'onStartShouldSetResponder', {\n  bubbles: true\n}];\nvar moveRegistration = ['onMoveShouldSetResponderCapture', 'onMoveShouldSetResponder', {\n  bubbles: true\n}];\nvar scrollRegistration = ['onScrollShouldSetResponderCapture', 'onScrollShouldSetResponder', {\n  bubbles: false\n}];\nvar shouldSetResponderEvents = {\n  touchstart: startRegistration,\n  mousedown: startRegistration,\n  touchmove: moveRegistration,\n  mousemove: moveRegistration,\n  scroll: scrollRegistration\n};\nvar emptyResponder = {\n  id: null,\n  idPath: null,\n  node: null\n};\nvar responderListenersMap = new Map();\nvar isEmulatingMouseEvents = false;\nvar trackedTouchCount = 0;\nvar currentResponder = {\n  id: null,\n  node: null,\n  idPath: null\n};\nvar responderTouchHistoryStore = new ResponderTouchHistoryStore();\nfunction changeCurrentResponder(responder) {\n  currentResponder = responder;\n}\nfunction getResponderConfig(id) {\n  var config = responderListenersMap.get(id);\n  return config != null ? config : emptyObject;\n}\nfunction eventListener(domEvent) {\n  var eventType = domEvent.type;\n  var eventTarget = domEvent.target;\n  if (eventType === 'touchstart') {\n    isEmulatingMouseEvents = true;\n  }\n  if (eventType === 'touchmove' || trackedTouchCount > 1) {\n    isEmulatingMouseEvents = false;\n  }\n  if (eventType === 'mousedown' && isEmulatingMouseEvents || eventType === 'mousemove' && isEmulatingMouseEvents || eventType === 'mousemove' && trackedTouchCount < 1) {\n    return;\n  }\n  if (isEmulatingMouseEvents && eventType === 'mouseup') {\n    if (trackedTouchCount === 0) {\n      isEmulatingMouseEvents = false;\n    }\n    return;\n  }\n  var isStartEvent = isStartish(eventType) && isPrimaryPointerDown(domEvent);\n  var isMoveEvent = isMoveish(eventType);\n  var isEndEvent = isEndish(eventType);\n  var isScrollEvent = isScroll(eventType);\n  var isSelectionChangeEvent = isSelectionChange(eventType);\n  var responderEvent = createResponderEvent(domEvent, responderTouchHistoryStore);\n  if (isStartEvent || isMoveEvent || isEndEvent) {\n    if (domEvent.touches) {\n      trackedTouchCount = domEvent.touches.length;\n    } else {\n      if (isStartEvent) {\n        trackedTouchCount = 1;\n      } else if (isEndEvent) {\n        trackedTouchCount = 0;\n      }\n    }\n    responderTouchHistoryStore.recordTouchTrack(eventType, responderEvent.nativeEvent);\n  }\n  var eventPaths = getResponderPaths(domEvent);\n  var wasNegotiated = false;\n  var wantsResponder;\n  if (isStartEvent || isMoveEvent || isScrollEvent && trackedTouchCount > 0) {\n    var currentResponderIdPath = currentResponder.idPath;\n    var eventIdPath = eventPaths.idPath;\n    if (currentResponderIdPath != null && eventIdPath != null) {\n      var lowestCommonAncestor = getLowestCommonAncestor(currentResponderIdPath, eventIdPath);\n      if (lowestCommonAncestor != null) {\n        var indexOfLowestCommonAncestor = eventIdPath.indexOf(lowestCommonAncestor);\n        var index = indexOfLowestCommonAncestor + (lowestCommonAncestor === currentResponder.id ? 1 : 0);\n        eventPaths = {\n          idPath: eventIdPath.slice(index),\n          nodePath: eventPaths.nodePath.slice(index)\n        };\n      } else {\n        eventPaths = null;\n      }\n    }\n    if (eventPaths != null) {\n      wantsResponder = findWantsResponder(eventPaths, domEvent, responderEvent);\n      if (wantsResponder != null) {\n        attemptTransfer(responderEvent, wantsResponder);\n        wasNegotiated = true;\n      }\n    }\n  }\n  if (currentResponder.id != null && currentResponder.node != null) {\n    var _currentResponder = currentResponder,\n      id = _currentResponder.id,\n      node = _currentResponder.node;\n    var _getResponderConfig = getResponderConfig(id),\n      onResponderStart = _getResponderConfig.onResponderStart,\n      onResponderMove = _getResponderConfig.onResponderMove,\n      onResponderEnd = _getResponderConfig.onResponderEnd,\n      onResponderRelease = _getResponderConfig.onResponderRelease,\n      onResponderTerminate = _getResponderConfig.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig.onResponderTerminationRequest;\n    responderEvent.bubbles = false;\n    responderEvent.cancelable = false;\n    responderEvent.currentTarget = node;\n    if (isStartEvent) {\n      if (onResponderStart != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderStart';\n        onResponderStart(responderEvent);\n      }\n    } else if (isMoveEvent) {\n      if (onResponderMove != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderMove';\n        onResponderMove(responderEvent);\n      }\n    } else {\n      var isTerminateEvent = isCancelish(eventType) || eventType === 'contextmenu' || eventType === 'blur' && eventTarget === window || eventType === 'blur' && eventTarget.contains(node) && domEvent.relatedTarget !== node || isScrollEvent && trackedTouchCount === 0 || isScrollEvent && eventTarget.contains(node) && eventTarget !== node || isSelectionChangeEvent && hasValidSelection(domEvent);\n      var isReleaseEvent = isEndEvent && !isTerminateEvent && !hasTargetTouches(node, domEvent.touches);\n      if (isEndEvent) {\n        if (onResponderEnd != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderEnd';\n          onResponderEnd(responderEvent);\n        }\n      }\n      if (isReleaseEvent) {\n        if (onResponderRelease != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderRelease';\n          onResponderRelease(responderEvent);\n        }\n        changeCurrentResponder(emptyResponder);\n      }\n      if (isTerminateEvent) {\n        var shouldTerminate = true;\n        if (eventType === 'contextmenu' || eventType === 'scroll' || eventType === 'selectionchange') {\n          if (wasNegotiated) {\n            shouldTerminate = false;\n          } else if (onResponderTerminationRequest != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n            if (onResponderTerminationRequest(responderEvent) === false) {\n              shouldTerminate = false;\n            }\n          }\n        }\n        if (shouldTerminate) {\n          if (onResponderTerminate != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n            onResponderTerminate(responderEvent);\n          }\n          changeCurrentResponder(emptyResponder);\n          isEmulatingMouseEvents = false;\n          trackedTouchCount = 0;\n        }\n      }\n    }\n  }\n}\nfunction findWantsResponder(eventPaths, domEvent, responderEvent) {\n  var shouldSetCallbacks = shouldSetResponderEvents[domEvent.type];\n  if (shouldSetCallbacks != null) {\n    var idPath = eventPaths.idPath,\n      nodePath = eventPaths.nodePath;\n    var shouldSetCallbackCaptureName = shouldSetCallbacks[0];\n    var shouldSetCallbackBubbleName = shouldSetCallbacks[1];\n    var bubbles = shouldSetCallbacks[2].bubbles;\n    var check = function check(id, node, callbackName) {\n      var config = getResponderConfig(id);\n      var shouldSetCallback = config[callbackName];\n      if (shouldSetCallback != null) {\n        responderEvent.currentTarget = node;\n        if (shouldSetCallback(responderEvent) === true) {\n          var prunedIdPath = idPath.slice(idPath.indexOf(id));\n          return {\n            id: id,\n            node: node,\n            idPath: prunedIdPath\n          };\n        }\n      }\n    };\n    for (var i = idPath.length - 1; i >= 0; i--) {\n      var id = idPath[i];\n      var node = nodePath[i];\n      var result = check(id, node, shouldSetCallbackCaptureName);\n      if (result != null) {\n        return result;\n      }\n      if (responderEvent.isPropagationStopped() === true) {\n        return;\n      }\n    }\n    if (bubbles) {\n      for (var _i = 0; _i < idPath.length; _i++) {\n        var _id = idPath[_i];\n        var _node = nodePath[_i];\n        var _result = check(_id, _node, shouldSetCallbackBubbleName);\n        if (_result != null) {\n          return _result;\n        }\n        if (responderEvent.isPropagationStopped() === true) {\n          return;\n        }\n      }\n    } else {\n      var _id2 = idPath[0];\n      var _node2 = nodePath[0];\n      var target = domEvent.target;\n      if (target === _node2) {\n        return check(_id2, _node2, shouldSetCallbackBubbleName);\n      }\n    }\n  }\n}\nfunction attemptTransfer(responderEvent, wantsResponder) {\n  var _currentResponder2 = currentResponder,\n    currentId = _currentResponder2.id,\n    currentNode = _currentResponder2.node;\n  var id = wantsResponder.id,\n    node = wantsResponder.node;\n  var _getResponderConfig2 = getResponderConfig(id),\n    onResponderGrant = _getResponderConfig2.onResponderGrant,\n    onResponderReject = _getResponderConfig2.onResponderReject;\n  responderEvent.bubbles = false;\n  responderEvent.cancelable = false;\n  responderEvent.currentTarget = node;\n  if (currentId == null) {\n    if (onResponderGrant != null) {\n      responderEvent.currentTarget = node;\n      responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n      onResponderGrant(responderEvent);\n    }\n    changeCurrentResponder(wantsResponder);\n  } else {\n    var _getResponderConfig3 = getResponderConfig(currentId),\n      onResponderTerminate = _getResponderConfig3.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig3.onResponderTerminationRequest;\n    var allowTransfer = true;\n    if (onResponderTerminationRequest != null) {\n      responderEvent.currentTarget = currentNode;\n      responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n      if (onResponderTerminationRequest(responderEvent) === false) {\n        allowTransfer = false;\n      }\n    }\n    if (allowTransfer) {\n      if (onResponderTerminate != null) {\n        responderEvent.currentTarget = currentNode;\n        responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n        onResponderTerminate(responderEvent);\n      }\n      if (onResponderGrant != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n        onResponderGrant(responderEvent);\n      }\n      changeCurrentResponder(wantsResponder);\n    } else {\n      if (onResponderReject != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderReject';\n        onResponderReject(responderEvent);\n      }\n    }\n  }\n}\nvar documentEventsCapturePhase = ['blur', 'scroll'];\nvar documentEventsBubblePhase = ['mousedown', 'mousemove', 'mouseup', 'dragstart', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'contextmenu', 'select', 'selectionchange'];\nexport function attachListeners() {\n  if (canUseDOM && window.__reactResponderSystemActive == null) {\n    window.addEventListener('blur', eventListener);\n    documentEventsBubblePhase.forEach(function (eventType) {\n      document.addEventListener(eventType, eventListener);\n    });\n    documentEventsCapturePhase.forEach(function (eventType) {\n      document.addEventListener(eventType, eventListener, true);\n    });\n    window.__reactResponderSystemActive = true;\n  }\n}\nexport function addNode(id, node, config) {\n  setResponderId(node, id);\n  responderListenersMap.set(id, config);\n}\nexport function removeNode(id) {\n  if (currentResponder.id === id) {\n    terminateResponder();\n  }\n  if (responderListenersMap.has(id)) {\n    responderListenersMap.delete(id);\n  }\n}\nexport function terminateResponder() {\n  var _currentResponder3 = currentResponder,\n    id = _currentResponder3.id,\n    node = _currentResponder3.node;\n  if (id != null && node != null) {\n    var _getResponderConfig4 = getResponderConfig(id),\n      onResponderTerminate = _getResponderConfig4.onResponderTerminate;\n    if (onResponderTerminate != null) {\n      var event = createResponderEvent({}, responderTouchHistoryStore);\n      event.currentTarget = node;\n      onResponderTerminate(event);\n    }\n    changeCurrentResponder(emptyResponder);\n  }\n  isEmulatingMouseEvents = false;\n  trackedTouchCount = 0;\n}\nexport function getResponderNode() {\n  return currentResponder.node;\n}", "map": {"version": 3, "names": ["createResponderEvent", "isCancelish", "<PERSON><PERSON><PERSON><PERSON>", "isMoveish", "isScroll", "isSelectionChange", "isStartish", "getLowestCommonAncestor", "getResponderPaths", "hasTargetTouches", "hasValidSelection", "isPrimaryPointerDown", "setResponderId", "ResponderTouchHistoryStore", "canUseDOM", "emptyObject", "startRegistration", "bubbles", "moveRegistration", "scrollRegistration", "shouldSetResponderEvents", "touchstart", "mousedown", "touchmove", "mousemove", "scroll", "emptyResponder", "id", "idPath", "node", "responderListenersMap", "Map", "isEmulatingMouseEvents", "trackedTouchCount", "currentResponder", "responderTouchHistoryStore", "changeCurrentResponder", "responder", "getResponderConfig", "config", "get", "eventListener", "domEvent", "eventType", "type", "eventTarget", "target", "isStartEvent", "isMoveEvent", "isEndEvent", "isScrollEvent", "isSelectionChangeEvent", "responderEvent", "touches", "length", "recordTouchTrack", "nativeEvent", "eventPaths", "wasNegotiated", "wantsResponder", "currentResponderIdPath", "eventIdPath", "lowestCommonAncestor", "indexOfLowestCommonAncestor", "indexOf", "index", "slice", "nodePath", "findWantsResponder", "attemptTransfer", "_currentResponder", "_getResponderConfig", "onResponderStart", "onResponderMove", "onResponderEnd", "onResponderRelease", "onResponderTerminate", "onResponderTerminationRequest", "cancelable", "currentTarget", "dispatchConfig", "registrationName", "isTerminateEvent", "window", "contains", "relatedTarget", "isReleaseEvent", "shouldTerminate", "shouldSetCallbacks", "shouldSetCallbackCaptureName", "shouldSetCallbackBubbleName", "check", "callback<PERSON><PERSON>", "shouldSetCallback", "pruned<PERSON>d<PERSON><PERSON>", "i", "result", "isPropagationStopped", "_i", "_id", "_node", "_result", "_id2", "_node2", "_currentResponder2", "currentId", "currentNode", "_getResponderConfig2", "onResponderGrant", "onResponderReject", "_getResponderConfig3", "allowTransfer", "documentEventsCapturePhase", "documentEventsBubblePhase", "attachListeners", "__reactResponderSystemActive", "addEventListener", "for<PERSON>ach", "document", "addNode", "set", "removeNode", "terminateResponder", "has", "delete", "_currentResponder3", "_getResponderConfig4", "event", "getResponderNode"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/modules/useResponderEvents/ResponderSystem.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * RESPONDER EVENT SYSTEM\n *\n * A single, global \"interaction lock\" on views. For a view to be the \"responder\" means\n * that pointer interactions are exclusive to that view and none other. The \"interaction\n * lock\" can be transferred (only) to ancestors of the current \"responder\" as long as\n * pointers continue to be active.\n *\n * Responder being granted:\n *\n * A view can become the \"responder\" after the following events:\n *  * \"pointerdown\" (implemented using \"touchstart\", \"mousedown\")\n *  * \"pointermove\" (implemented using \"touchmove\", \"mousemove\")\n *  * \"scroll\" (while a pointer is down)\n *  * \"selectionchange\" (while a pointer is down)\n *\n * If nothing is already the \"responder\", the event propagates to (capture) and from\n * (bubble) the event target until a view returns `true` for\n * `on*ShouldSetResponder(Capture)`.\n *\n * If something is already the responder, the event propagates to (capture) and from\n * (bubble) the lowest common ancestor of the event target and the current \"responder\".\n * Then negotiation happens between the current \"responder\" and a view that wants to\n * become the \"responder\": see the timing diagram below.\n *\n * (NOTE: Scrolled views either automatically become the \"responder\" or release the\n * \"interaction lock\". A native scroll view that isn't built on top of the responder\n * system must result in the current \"responder\" being notified that it no longer has\n * the \"interaction lock\" - the native system has taken over.\n *\n * Responder being released:\n *\n * As soon as there are no more active pointers that *started* inside descendants\n * of the *current* \"responder\", an `onResponderRelease` event is dispatched to the\n * current \"responder\", and the responder lock is released.\n *\n * Typical sequence of events:\n *  * startShouldSetResponder\n *  * responderGrant/Reject\n *  * responderStart\n *  * responderMove\n *  * responderEnd\n *  * responderRelease\n */\n\n/*                                             Negotiation Performed\n                                             +-----------------------+\n                                            /                         \\\nProcess low level events to    +     Current Responder      +   wantsResponderID\ndetermine who to perform negot-|   (if any exists at all)   |\niation/transition              | Otherwise just pass through|\n-------------------------------+----------------------------+------------------+\nBubble to find first ID        |                            |\nto return true:wantsResponderID|                            |\n                               |                            |\n     +--------------+          |                            |\n     | onTouchStart |          |                            |\n     +------+-------+    none  |                            |\n            |            return|                            |\n+-----------v-------------+true| +------------------------+ |\n|onStartShouldSetResponder|----->| onResponderStart (cur) |<-----------+\n+-----------+-------------+    | +------------------------+ |          |\n            |                  |                            | +--------+-------+\n            | returned true for|       false:REJECT +-------->|onResponderReject\n            | wantsResponderID |                    |       | +----------------+\n            | (now attempt     | +------------------+-----+ |\n            |  handoff)        | | onResponder            | |\n            +------------------->|    TerminationRequest  | |\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |         true:GRANT +-------->|onResponderGrant|\n                               |                            | +--------+-------+\n                               | +------------------------+ |          |\n                               | | onResponderTerminate   |<-----------+\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |                    +-------->|onResponderStart|\n                               |                            | +----------------+\nBubble to find first ID        |                            |\nto return true:wantsResponderID|                            |\n                               |                            |\n     +-------------+           |                            |\n     | onTouchMove |           |                            |\n     +------+------+     none  |                            |\n            |            return|                            |\n+-----------v-------------+true| +------------------------+ |\n|onMoveShouldSetResponder |----->| onResponderMove (cur)  |<-----------+\n+-----------+-------------+    | +------------------------+ |          |\n            |                  |                            | +--------+-------+\n            | returned true for|       false:REJECT +-------->|onResponderReject\n            | wantsResponderID |                    |       | +----------------+\n            | (now attempt     | +------------------+-----+ |\n            |  handoff)        | |   onResponder          | |\n            +------------------->|      TerminationRequest| |\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |         true:GRANT +-------->|onResponderGrant|\n                               |                            | +--------+-------+\n                               | +------------------------+ |          |\n                               | |   onResponderTerminate |<-----------+\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |                    +-------->|onResponderMove |\n                               |                            | +----------------+\n                               |                            |\n                               |                            |\n      Some active touch started|                            |\n      inside current responder | +------------------------+ |\n      +------------------------->|      onResponderEnd    | |\n      |                        | +------------------------+ |\n  +---+---------+              |                            |\n  | onTouchEnd  |              |                            |\n  +---+---------+              |                            |\n      |                        | +------------------------+ |\n      +------------------------->|     onResponderEnd     | |\n      No active touches started| +-----------+------------+ |\n      inside current responder |             |              |\n                               |             v              |\n                               | +------------------------+ |\n                               | |    onResponderRelease  | |\n                               | +------------------------+ |\n                               |                            |\n                               +                            + */\n\nimport createResponderEvent from './createResponderEvent';\nimport { isCancelish, isEndish, isMoveish, isScroll, isSelectionChange, isStartish } from './ResponderEventTypes';\nimport { getLowestCommonAncestor, getResponderPaths, hasTargetTouches, hasValidSelection, isPrimaryPointerDown, setResponderId } from './utils';\nimport { ResponderTouchHistoryStore } from './ResponderTouchHistoryStore';\nimport canUseDOM from '../canUseDom';\n\n/* ------------ TYPES ------------ */\n\nvar emptyObject = {};\n\n/* ------------ IMPLEMENTATION ------------ */\n\nvar startRegistration = ['onStartShouldSetResponderCapture', 'onStartShouldSetResponder', {\n  bubbles: true\n}];\nvar moveRegistration = ['onMoveShouldSetResponderCapture', 'onMoveShouldSetResponder', {\n  bubbles: true\n}];\nvar scrollRegistration = ['onScrollShouldSetResponderCapture', 'onScrollShouldSetResponder', {\n  bubbles: false\n}];\nvar shouldSetResponderEvents = {\n  touchstart: startRegistration,\n  mousedown: startRegistration,\n  touchmove: moveRegistration,\n  mousemove: moveRegistration,\n  scroll: scrollRegistration\n};\nvar emptyResponder = {\n  id: null,\n  idPath: null,\n  node: null\n};\nvar responderListenersMap = new Map();\nvar isEmulatingMouseEvents = false;\nvar trackedTouchCount = 0;\nvar currentResponder = {\n  id: null,\n  node: null,\n  idPath: null\n};\nvar responderTouchHistoryStore = new ResponderTouchHistoryStore();\nfunction changeCurrentResponder(responder) {\n  currentResponder = responder;\n}\nfunction getResponderConfig(id) {\n  var config = responderListenersMap.get(id);\n  return config != null ? config : emptyObject;\n}\n\n/**\n * Process native events\n *\n * A single event listener is used to manage the responder system.\n * All pointers are tracked in the ResponderTouchHistoryStore. Native events\n * are interpreted in terms of the Responder System and checked to see if\n * the responder should be transferred. Each host node that is attached to\n * the Responder System has an ID, which is used to look up its associated\n * callbacks.\n */\nfunction eventListener(domEvent) {\n  var eventType = domEvent.type;\n  var eventTarget = domEvent.target;\n\n  /**\n   * Manage emulated events and early bailout.\n   * Since PointerEvent is not used yet (lack of support in older Safari), it's\n   * necessary to manually manage the mess of browser touch/mouse events.\n   * And bailout early for termination events when there is no active responder.\n   */\n\n  // Flag when browser may produce emulated events\n  if (eventType === 'touchstart') {\n    isEmulatingMouseEvents = true;\n  }\n  // Remove flag when browser will not produce emulated events\n  if (eventType === 'touchmove' || trackedTouchCount > 1) {\n    isEmulatingMouseEvents = false;\n  }\n  // Ignore various events in particular circumstances\n  if (\n  // Ignore browser emulated mouse events\n  eventType === 'mousedown' && isEmulatingMouseEvents || eventType === 'mousemove' && isEmulatingMouseEvents ||\n  // Ignore mousemove if a mousedown didn't occur first\n  eventType === 'mousemove' && trackedTouchCount < 1) {\n    return;\n  }\n  // Remove flag after emulated events are finished\n  if (isEmulatingMouseEvents && eventType === 'mouseup') {\n    if (trackedTouchCount === 0) {\n      isEmulatingMouseEvents = false;\n    }\n    return;\n  }\n  var isStartEvent = isStartish(eventType) && isPrimaryPointerDown(domEvent);\n  var isMoveEvent = isMoveish(eventType);\n  var isEndEvent = isEndish(eventType);\n  var isScrollEvent = isScroll(eventType);\n  var isSelectionChangeEvent = isSelectionChange(eventType);\n  var responderEvent = createResponderEvent(domEvent, responderTouchHistoryStore);\n\n  /**\n   * Record the state of active pointers\n   */\n\n  if (isStartEvent || isMoveEvent || isEndEvent) {\n    if (domEvent.touches) {\n      trackedTouchCount = domEvent.touches.length;\n    } else {\n      if (isStartEvent) {\n        trackedTouchCount = 1;\n      } else if (isEndEvent) {\n        trackedTouchCount = 0;\n      }\n    }\n    responderTouchHistoryStore.recordTouchTrack(eventType, responderEvent.nativeEvent);\n  }\n\n  /**\n   * Responder System logic\n   */\n\n  var eventPaths = getResponderPaths(domEvent);\n  var wasNegotiated = false;\n  var wantsResponder;\n\n  // If an event occured that might change the current responder...\n  if (isStartEvent || isMoveEvent || isScrollEvent && trackedTouchCount > 0) {\n    // If there is already a responder, prune the event paths to the lowest common ancestor\n    // of the existing responder and deepest target of the event.\n    var currentResponderIdPath = currentResponder.idPath;\n    var eventIdPath = eventPaths.idPath;\n    if (currentResponderIdPath != null && eventIdPath != null) {\n      var lowestCommonAncestor = getLowestCommonAncestor(currentResponderIdPath, eventIdPath);\n      if (lowestCommonAncestor != null) {\n        var indexOfLowestCommonAncestor = eventIdPath.indexOf(lowestCommonAncestor);\n        // Skip the current responder so it doesn't receive unexpected \"shouldSet\" events.\n        var index = indexOfLowestCommonAncestor + (lowestCommonAncestor === currentResponder.id ? 1 : 0);\n        eventPaths = {\n          idPath: eventIdPath.slice(index),\n          nodePath: eventPaths.nodePath.slice(index)\n        };\n      } else {\n        eventPaths = null;\n      }\n    }\n    if (eventPaths != null) {\n      // If a node wants to become the responder, attempt to transfer.\n      wantsResponder = findWantsResponder(eventPaths, domEvent, responderEvent);\n      if (wantsResponder != null) {\n        // Sets responder if none exists, or negotates with existing responder.\n        attemptTransfer(responderEvent, wantsResponder);\n        wasNegotiated = true;\n      }\n    }\n  }\n\n  // If there is now a responder, invoke its callbacks for the lifecycle of the gesture.\n  if (currentResponder.id != null && currentResponder.node != null) {\n    var _currentResponder = currentResponder,\n      id = _currentResponder.id,\n      node = _currentResponder.node;\n    var _getResponderConfig = getResponderConfig(id),\n      onResponderStart = _getResponderConfig.onResponderStart,\n      onResponderMove = _getResponderConfig.onResponderMove,\n      onResponderEnd = _getResponderConfig.onResponderEnd,\n      onResponderRelease = _getResponderConfig.onResponderRelease,\n      onResponderTerminate = _getResponderConfig.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig.onResponderTerminationRequest;\n    responderEvent.bubbles = false;\n    responderEvent.cancelable = false;\n    responderEvent.currentTarget = node;\n\n    // Start\n    if (isStartEvent) {\n      if (onResponderStart != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderStart';\n        onResponderStart(responderEvent);\n      }\n    }\n    // Move\n    else if (isMoveEvent) {\n      if (onResponderMove != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderMove';\n        onResponderMove(responderEvent);\n      }\n    } else {\n      var isTerminateEvent = isCancelish(eventType) ||\n      // native context menu\n      eventType === 'contextmenu' ||\n      // window blur\n      eventType === 'blur' && eventTarget === window ||\n      // responder (or ancestors) blur\n      eventType === 'blur' && eventTarget.contains(node) && domEvent.relatedTarget !== node ||\n      // native scroll without using a pointer\n      isScrollEvent && trackedTouchCount === 0 ||\n      // native scroll on node that is parent of the responder (allow siblings to scroll)\n      isScrollEvent && eventTarget.contains(node) && eventTarget !== node ||\n      // native select/selectionchange on node\n      isSelectionChangeEvent && hasValidSelection(domEvent);\n      var isReleaseEvent = isEndEvent && !isTerminateEvent && !hasTargetTouches(node, domEvent.touches);\n\n      // End\n      if (isEndEvent) {\n        if (onResponderEnd != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderEnd';\n          onResponderEnd(responderEvent);\n        }\n      }\n      // Release\n      if (isReleaseEvent) {\n        if (onResponderRelease != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderRelease';\n          onResponderRelease(responderEvent);\n        }\n        changeCurrentResponder(emptyResponder);\n      }\n      // Terminate\n      if (isTerminateEvent) {\n        var shouldTerminate = true;\n\n        // Responders can still avoid termination but only for these events.\n        if (eventType === 'contextmenu' || eventType === 'scroll' || eventType === 'selectionchange') {\n          // Only call this function is it wasn't already called during negotiation.\n          if (wasNegotiated) {\n            shouldTerminate = false;\n          } else if (onResponderTerminationRequest != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n            if (onResponderTerminationRequest(responderEvent) === false) {\n              shouldTerminate = false;\n            }\n          }\n        }\n        if (shouldTerminate) {\n          if (onResponderTerminate != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n            onResponderTerminate(responderEvent);\n          }\n          changeCurrentResponder(emptyResponder);\n          isEmulatingMouseEvents = false;\n          trackedTouchCount = 0;\n        }\n      }\n    }\n  }\n}\n\n/**\n * Walk the event path to/from the target node. At each node, stop and call the\n * relevant \"shouldSet\" functions for the given event type. If any of those functions\n * call \"stopPropagation\" on the event, stop searching for a responder.\n */\nfunction findWantsResponder(eventPaths, domEvent, responderEvent) {\n  var shouldSetCallbacks = shouldSetResponderEvents[domEvent.type]; // for Flow\n\n  if (shouldSetCallbacks != null) {\n    var idPath = eventPaths.idPath,\n      nodePath = eventPaths.nodePath;\n    var shouldSetCallbackCaptureName = shouldSetCallbacks[0];\n    var shouldSetCallbackBubbleName = shouldSetCallbacks[1];\n    var bubbles = shouldSetCallbacks[2].bubbles;\n    var check = function check(id, node, callbackName) {\n      var config = getResponderConfig(id);\n      var shouldSetCallback = config[callbackName];\n      if (shouldSetCallback != null) {\n        responderEvent.currentTarget = node;\n        if (shouldSetCallback(responderEvent) === true) {\n          // Start the path from the potential responder\n          var prunedIdPath = idPath.slice(idPath.indexOf(id));\n          return {\n            id,\n            node,\n            idPath: prunedIdPath\n          };\n        }\n      }\n    };\n\n    // capture\n    for (var i = idPath.length - 1; i >= 0; i--) {\n      var id = idPath[i];\n      var node = nodePath[i];\n      var result = check(id, node, shouldSetCallbackCaptureName);\n      if (result != null) {\n        return result;\n      }\n      if (responderEvent.isPropagationStopped() === true) {\n        return;\n      }\n    }\n\n    // bubble\n    if (bubbles) {\n      for (var _i = 0; _i < idPath.length; _i++) {\n        var _id = idPath[_i];\n        var _node = nodePath[_i];\n        var _result = check(_id, _node, shouldSetCallbackBubbleName);\n        if (_result != null) {\n          return _result;\n        }\n        if (responderEvent.isPropagationStopped() === true) {\n          return;\n        }\n      }\n    } else {\n      var _id2 = idPath[0];\n      var _node2 = nodePath[0];\n      var target = domEvent.target;\n      if (target === _node2) {\n        return check(_id2, _node2, shouldSetCallbackBubbleName);\n      }\n    }\n  }\n}\n\n/**\n * Attempt to transfer the responder.\n */\nfunction attemptTransfer(responderEvent, wantsResponder) {\n  var _currentResponder2 = currentResponder,\n    currentId = _currentResponder2.id,\n    currentNode = _currentResponder2.node;\n  var id = wantsResponder.id,\n    node = wantsResponder.node;\n  var _getResponderConfig2 = getResponderConfig(id),\n    onResponderGrant = _getResponderConfig2.onResponderGrant,\n    onResponderReject = _getResponderConfig2.onResponderReject;\n  responderEvent.bubbles = false;\n  responderEvent.cancelable = false;\n  responderEvent.currentTarget = node;\n\n  // Set responder\n  if (currentId == null) {\n    if (onResponderGrant != null) {\n      responderEvent.currentTarget = node;\n      responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n      onResponderGrant(responderEvent);\n    }\n    changeCurrentResponder(wantsResponder);\n  }\n  // Negotiate with current responder\n  else {\n    var _getResponderConfig3 = getResponderConfig(currentId),\n      onResponderTerminate = _getResponderConfig3.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig3.onResponderTerminationRequest;\n    var allowTransfer = true;\n    if (onResponderTerminationRequest != null) {\n      responderEvent.currentTarget = currentNode;\n      responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n      if (onResponderTerminationRequest(responderEvent) === false) {\n        allowTransfer = false;\n      }\n    }\n    if (allowTransfer) {\n      // Terminate existing responder\n      if (onResponderTerminate != null) {\n        responderEvent.currentTarget = currentNode;\n        responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n        onResponderTerminate(responderEvent);\n      }\n      // Grant next responder\n      if (onResponderGrant != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n        onResponderGrant(responderEvent);\n      }\n      changeCurrentResponder(wantsResponder);\n    } else {\n      // Reject responder request\n      if (onResponderReject != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderReject';\n        onResponderReject(responderEvent);\n      }\n    }\n  }\n}\n\n/* ------------ PUBLIC API ------------ */\n\n/**\n * Attach Listeners\n *\n * Use native events as ReactDOM doesn't have a non-plugin API to implement\n * this system.\n */\nvar documentEventsCapturePhase = ['blur', 'scroll'];\nvar documentEventsBubblePhase = [\n// mouse\n'mousedown', 'mousemove', 'mouseup', 'dragstart',\n// touch\n'touchstart', 'touchmove', 'touchend', 'touchcancel',\n// other\n'contextmenu', 'select', 'selectionchange'];\nexport function attachListeners() {\n  if (canUseDOM && window.__reactResponderSystemActive == null) {\n    window.addEventListener('blur', eventListener);\n    documentEventsBubblePhase.forEach(eventType => {\n      document.addEventListener(eventType, eventListener);\n    });\n    documentEventsCapturePhase.forEach(eventType => {\n      document.addEventListener(eventType, eventListener, true);\n    });\n    window.__reactResponderSystemActive = true;\n  }\n}\n\n/**\n * Register a node with the ResponderSystem.\n */\nexport function addNode(id, node, config) {\n  setResponderId(node, id);\n  responderListenersMap.set(id, config);\n}\n\n/**\n * Unregister a node with the ResponderSystem.\n */\nexport function removeNode(id) {\n  if (currentResponder.id === id) {\n    terminateResponder();\n  }\n  if (responderListenersMap.has(id)) {\n    responderListenersMap.delete(id);\n  }\n}\n\n/**\n * Allow the current responder to be terminated from within components to support\n * more complex requirements, such as use with other React libraries for working\n * with scroll views, input views, etc.\n */\nexport function terminateResponder() {\n  var _currentResponder3 = currentResponder,\n    id = _currentResponder3.id,\n    node = _currentResponder3.node;\n  if (id != null && node != null) {\n    var _getResponderConfig4 = getResponderConfig(id),\n      onResponderTerminate = _getResponderConfig4.onResponderTerminate;\n    if (onResponderTerminate != null) {\n      var event = createResponderEvent({}, responderTouchHistoryStore);\n      event.currentTarget = node;\n      onResponderTerminate(event);\n    }\n    changeCurrentResponder(emptyResponder);\n  }\n  isEmulatingMouseEvents = false;\n  trackedTouchCount = 0;\n}\n\n/**\n * Allow unit tests to inspect the current responder in the system.\n * FOR TESTING ONLY.\n */\nexport function getResponderNode() {\n  return currentResponder.node;\n}"], "mappings": "AAqIA,OAAOA,oBAAoB;AAC3B,SAASC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,UAAU;AAClF,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,cAAc;AAC9H,SAASC,0BAA0B;AACnC,OAAOC,SAAS;AAIhB,IAAIC,WAAW,GAAG,CAAC,CAAC;AAIpB,IAAIC,iBAAiB,GAAG,CAAC,kCAAkC,EAAE,2BAA2B,EAAE;EACxFC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIC,gBAAgB,GAAG,CAAC,iCAAiC,EAAE,0BAA0B,EAAE;EACrFD,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIE,kBAAkB,GAAG,CAAC,mCAAmC,EAAE,4BAA4B,EAAE;EAC3FF,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIG,wBAAwB,GAAG;EAC7BC,UAAU,EAAEL,iBAAiB;EAC7BM,SAAS,EAAEN,iBAAiB;EAC5BO,SAAS,EAAEL,gBAAgB;EAC3BM,SAAS,EAAEN,gBAAgB;EAC3BO,MAAM,EAAEN;AACV,CAAC;AACD,IAAIO,cAAc,GAAG;EACnBC,EAAE,EAAE,IAAI;EACRC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,qBAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC,IAAIC,sBAAsB,GAAG,KAAK;AAClC,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,gBAAgB,GAAG;EACrBP,EAAE,EAAE,IAAI;EACRE,IAAI,EAAE,IAAI;EACVD,MAAM,EAAE;AACV,CAAC;AACD,IAAIO,0BAA0B,GAAG,IAAItB,0BAA0B,CAAC,CAAC;AACjE,SAASuB,sBAAsBA,CAACC,SAAS,EAAE;EACzCH,gBAAgB,GAAGG,SAAS;AAC9B;AACA,SAASC,kBAAkBA,CAACX,EAAE,EAAE;EAC9B,IAAIY,MAAM,GAAGT,qBAAqB,CAACU,GAAG,CAACb,EAAE,CAAC;EAC1C,OAAOY,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGxB,WAAW;AAC9C;AAYA,SAAS0B,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAIC,SAAS,GAAGD,QAAQ,CAACE,IAAI;EAC7B,IAAIC,WAAW,GAAGH,QAAQ,CAACI,MAAM;EAUjC,IAAIH,SAAS,KAAK,YAAY,EAAE;IAC9BX,sBAAsB,GAAG,IAAI;EAC/B;EAEA,IAAIW,SAAS,KAAK,WAAW,IAAIV,iBAAiB,GAAG,CAAC,EAAE;IACtDD,sBAAsB,GAAG,KAAK;EAChC;EAEA,IAEAW,SAAS,KAAK,WAAW,IAAIX,sBAAsB,IAAIW,SAAS,KAAK,WAAW,IAAIX,sBAAsB,IAE1GW,SAAS,KAAK,WAAW,IAAIV,iBAAiB,GAAG,CAAC,EAAE;IAClD;EACF;EAEA,IAAID,sBAAsB,IAAIW,SAAS,KAAK,SAAS,EAAE;IACrD,IAAIV,iBAAiB,KAAK,CAAC,EAAE;MAC3BD,sBAAsB,GAAG,KAAK;IAChC;IACA;EACF;EACA,IAAIe,YAAY,GAAGzC,UAAU,CAACqC,SAAS,CAAC,IAAIhC,oBAAoB,CAAC+B,QAAQ,CAAC;EAC1E,IAAIM,WAAW,GAAG7C,SAAS,CAACwC,SAAS,CAAC;EACtC,IAAIM,UAAU,GAAG/C,QAAQ,CAACyC,SAAS,CAAC;EACpC,IAAIO,aAAa,GAAG9C,QAAQ,CAACuC,SAAS,CAAC;EACvC,IAAIQ,sBAAsB,GAAG9C,iBAAiB,CAACsC,SAAS,CAAC;EACzD,IAAIS,cAAc,GAAGpD,oBAAoB,CAAC0C,QAAQ,EAAEP,0BAA0B,CAAC;EAM/E,IAAIY,YAAY,IAAIC,WAAW,IAAIC,UAAU,EAAE;IAC7C,IAAIP,QAAQ,CAACW,OAAO,EAAE;MACpBpB,iBAAiB,GAAGS,QAAQ,CAACW,OAAO,CAACC,MAAM;IAC7C,CAAC,MAAM;MACL,IAAIP,YAAY,EAAE;QAChBd,iBAAiB,GAAG,CAAC;MACvB,CAAC,MAAM,IAAIgB,UAAU,EAAE;QACrBhB,iBAAiB,GAAG,CAAC;MACvB;IACF;IACAE,0BAA0B,CAACoB,gBAAgB,CAACZ,SAAS,EAAES,cAAc,CAACI,WAAW,CAAC;EACpF;EAMA,IAAIC,UAAU,GAAGjD,iBAAiB,CAACkC,QAAQ,CAAC;EAC5C,IAAIgB,aAAa,GAAG,KAAK;EACzB,IAAIC,cAAc;EAGlB,IAAIZ,YAAY,IAAIC,WAAW,IAAIE,aAAa,IAAIjB,iBAAiB,GAAG,CAAC,EAAE;IAGzE,IAAI2B,sBAAsB,GAAG1B,gBAAgB,CAACN,MAAM;IACpD,IAAIiC,WAAW,GAAGJ,UAAU,CAAC7B,MAAM;IACnC,IAAIgC,sBAAsB,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI,EAAE;MACzD,IAAIC,oBAAoB,GAAGvD,uBAAuB,CAACqD,sBAAsB,EAAEC,WAAW,CAAC;MACvF,IAAIC,oBAAoB,IAAI,IAAI,EAAE;QAChC,IAAIC,2BAA2B,GAAGF,WAAW,CAACG,OAAO,CAACF,oBAAoB,CAAC;QAE3E,IAAIG,KAAK,GAAGF,2BAA2B,IAAID,oBAAoB,KAAK5B,gBAAgB,CAACP,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG8B,UAAU,GAAG;UACX7B,MAAM,EAAEiC,WAAW,CAACK,KAAK,CAACD,KAAK,CAAC;UAChCE,QAAQ,EAAEV,UAAU,CAACU,QAAQ,CAACD,KAAK,CAACD,KAAK;QAC3C,CAAC;MACH,CAAC,MAAM;QACLR,UAAU,GAAG,IAAI;MACnB;IACF;IACA,IAAIA,UAAU,IAAI,IAAI,EAAE;MAEtBE,cAAc,GAAGS,kBAAkB,CAACX,UAAU,EAAEf,QAAQ,EAAEU,cAAc,CAAC;MACzE,IAAIO,cAAc,IAAI,IAAI,EAAE;QAE1BU,eAAe,CAACjB,cAAc,EAAEO,cAAc,CAAC;QAC/CD,aAAa,GAAG,IAAI;MACtB;IACF;EACF;EAGA,IAAIxB,gBAAgB,CAACP,EAAE,IAAI,IAAI,IAAIO,gBAAgB,CAACL,IAAI,IAAI,IAAI,EAAE;IAChE,IAAIyC,iBAAiB,GAAGpC,gBAAgB;MACtCP,EAAE,GAAG2C,iBAAiB,CAAC3C,EAAE;MACzBE,IAAI,GAAGyC,iBAAiB,CAACzC,IAAI;IAC/B,IAAI0C,mBAAmB,GAAGjC,kBAAkB,CAACX,EAAE,CAAC;MAC9C6C,gBAAgB,GAAGD,mBAAmB,CAACC,gBAAgB;MACvDC,eAAe,GAAGF,mBAAmB,CAACE,eAAe;MACrDC,cAAc,GAAGH,mBAAmB,CAACG,cAAc;MACnDC,kBAAkB,GAAGJ,mBAAmB,CAACI,kBAAkB;MAC3DC,oBAAoB,GAAGL,mBAAmB,CAACK,oBAAoB;MAC/DC,6BAA6B,GAAGN,mBAAmB,CAACM,6BAA6B;IACnFzB,cAAc,CAACnC,OAAO,GAAG,KAAK;IAC9BmC,cAAc,CAAC0B,UAAU,GAAG,KAAK;IACjC1B,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;IAGnC,IAAIkB,YAAY,EAAE;MAChB,IAAIyB,gBAAgB,IAAI,IAAI,EAAE;QAC5BpB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;QACnET,gBAAgB,CAACpB,cAAc,CAAC;MAClC;IACF,CAAC,MAEI,IAAIJ,WAAW,EAAE;MACpB,IAAIyB,eAAe,IAAI,IAAI,EAAE;QAC3BrB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,iBAAiB;QAClER,eAAe,CAACrB,cAAc,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAI8B,gBAAgB,GAAGjF,WAAW,CAAC0C,SAAS,CAAC,IAE7CA,SAAS,KAAK,aAAa,IAE3BA,SAAS,KAAK,MAAM,IAAIE,WAAW,KAAKsC,MAAM,IAE9CxC,SAAS,KAAK,MAAM,IAAIE,WAAW,CAACuC,QAAQ,CAACvD,IAAI,CAAC,IAAIa,QAAQ,CAAC2C,aAAa,KAAKxD,IAAI,IAErFqB,aAAa,IAAIjB,iBAAiB,KAAK,CAAC,IAExCiB,aAAa,IAAIL,WAAW,CAACuC,QAAQ,CAACvD,IAAI,CAAC,IAAIgB,WAAW,KAAKhB,IAAI,IAEnEsB,sBAAsB,IAAIzC,iBAAiB,CAACgC,QAAQ,CAAC;MACrD,IAAI4C,cAAc,GAAGrC,UAAU,IAAI,CAACiC,gBAAgB,IAAI,CAACzE,gBAAgB,CAACoB,IAAI,EAAEa,QAAQ,CAACW,OAAO,CAAC;MAGjG,IAAIJ,UAAU,EAAE;QACd,IAAIyB,cAAc,IAAI,IAAI,EAAE;UAC1BtB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,gBAAgB;UACjEP,cAAc,CAACtB,cAAc,CAAC;QAChC;MACF;MAEA,IAAIkC,cAAc,EAAE;QAClB,IAAIX,kBAAkB,IAAI,IAAI,EAAE;UAC9BvB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,oBAAoB;UACrEN,kBAAkB,CAACvB,cAAc,CAAC;QACpC;QACAhB,sBAAsB,CAACV,cAAc,CAAC;MACxC;MAEA,IAAIwD,gBAAgB,EAAE;QACpB,IAAIK,eAAe,GAAG,IAAI;QAG1B,IAAI5C,SAAS,KAAK,aAAa,IAAIA,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,iBAAiB,EAAE;UAE5F,IAAIe,aAAa,EAAE;YACjB6B,eAAe,GAAG,KAAK;UACzB,CAAC,MAAM,IAAIV,6BAA6B,IAAI,IAAI,EAAE;YAChDzB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,+BAA+B;YAChF,IAAIJ,6BAA6B,CAACzB,cAAc,CAAC,KAAK,KAAK,EAAE;cAC3DmC,eAAe,GAAG,KAAK;YACzB;UACF;QACF;QACA,IAAIA,eAAe,EAAE;UACnB,IAAIX,oBAAoB,IAAI,IAAI,EAAE;YAChCxB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,sBAAsB;YACvEL,oBAAoB,CAACxB,cAAc,CAAC;UACtC;UACAhB,sBAAsB,CAACV,cAAc,CAAC;UACtCM,sBAAsB,GAAG,KAAK;UAC9BC,iBAAiB,GAAG,CAAC;QACvB;MACF;IACF;EACF;AACF;AAOA,SAASmC,kBAAkBA,CAACX,UAAU,EAAEf,QAAQ,EAAEU,cAAc,EAAE;EAChE,IAAIoC,kBAAkB,GAAGpE,wBAAwB,CAACsB,QAAQ,CAACE,IAAI,CAAC;EAEhE,IAAI4C,kBAAkB,IAAI,IAAI,EAAE;IAC9B,IAAI5D,MAAM,GAAG6B,UAAU,CAAC7B,MAAM;MAC5BuC,QAAQ,GAAGV,UAAU,CAACU,QAAQ;IAChC,IAAIsB,4BAA4B,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IACxD,IAAIE,2BAA2B,GAAGF,kBAAkB,CAAC,CAAC,CAAC;IACvD,IAAIvE,OAAO,GAAGuE,kBAAkB,CAAC,CAAC,CAAC,CAACvE,OAAO;IAC3C,IAAI0E,KAAK,GAAG,SAASA,KAAKA,CAAChE,EAAE,EAAEE,IAAI,EAAE+D,YAAY,EAAE;MACjD,IAAIrD,MAAM,GAAGD,kBAAkB,CAACX,EAAE,CAAC;MACnC,IAAIkE,iBAAiB,GAAGtD,MAAM,CAACqD,YAAY,CAAC;MAC5C,IAAIC,iBAAiB,IAAI,IAAI,EAAE;QAC7BzC,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;QACnC,IAAIgE,iBAAiB,CAACzC,cAAc,CAAC,KAAK,IAAI,EAAE;UAE9C,IAAI0C,YAAY,GAAGlE,MAAM,CAACsC,KAAK,CAACtC,MAAM,CAACoC,OAAO,CAACrC,EAAE,CAAC,CAAC;UACnD,OAAO;YACLA,EAAE,EAAFA,EAAE;YACFE,IAAI,EAAJA,IAAI;YACJD,MAAM,EAAEkE;UACV,CAAC;QACH;MACF;IACF,CAAC;IAGD,KAAK,IAAIC,CAAC,GAAGnE,MAAM,CAAC0B,MAAM,GAAG,CAAC,EAAEyC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAIpE,EAAE,GAAGC,MAAM,CAACmE,CAAC,CAAC;MAClB,IAAIlE,IAAI,GAAGsC,QAAQ,CAAC4B,CAAC,CAAC;MACtB,IAAIC,MAAM,GAAGL,KAAK,CAAChE,EAAE,EAAEE,IAAI,EAAE4D,4BAA4B,CAAC;MAC1D,IAAIO,MAAM,IAAI,IAAI,EAAE;QAClB,OAAOA,MAAM;MACf;MACA,IAAI5C,cAAc,CAAC6C,oBAAoB,CAAC,CAAC,KAAK,IAAI,EAAE;QAClD;MACF;IACF;IAGA,IAAIhF,OAAO,EAAE;MACX,KAAK,IAAIiF,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGtE,MAAM,CAAC0B,MAAM,EAAE4C,EAAE,EAAE,EAAE;QACzC,IAAIC,GAAG,GAAGvE,MAAM,CAACsE,EAAE,CAAC;QACpB,IAAIE,KAAK,GAAGjC,QAAQ,CAAC+B,EAAE,CAAC;QACxB,IAAIG,OAAO,GAAGV,KAAK,CAACQ,GAAG,EAAEC,KAAK,EAAEV,2BAA2B,CAAC;QAC5D,IAAIW,OAAO,IAAI,IAAI,EAAE;UACnB,OAAOA,OAAO;QAChB;QACA,IAAIjD,cAAc,CAAC6C,oBAAoB,CAAC,CAAC,KAAK,IAAI,EAAE;UAClD;QACF;MACF;IACF,CAAC,MAAM;MACL,IAAIK,IAAI,GAAG1E,MAAM,CAAC,CAAC,CAAC;MACpB,IAAI2E,MAAM,GAAGpC,QAAQ,CAAC,CAAC,CAAC;MACxB,IAAIrB,MAAM,GAAGJ,QAAQ,CAACI,MAAM;MAC5B,IAAIA,MAAM,KAAKyD,MAAM,EAAE;QACrB,OAAOZ,KAAK,CAACW,IAAI,EAAEC,MAAM,EAAEb,2BAA2B,CAAC;MACzD;IACF;EACF;AACF;AAKA,SAASrB,eAAeA,CAACjB,cAAc,EAAEO,cAAc,EAAE;EACvD,IAAI6C,kBAAkB,GAAGtE,gBAAgB;IACvCuE,SAAS,GAAGD,kBAAkB,CAAC7E,EAAE;IACjC+E,WAAW,GAAGF,kBAAkB,CAAC3E,IAAI;EACvC,IAAIF,EAAE,GAAGgC,cAAc,CAAChC,EAAE;IACxBE,IAAI,GAAG8B,cAAc,CAAC9B,IAAI;EAC5B,IAAI8E,oBAAoB,GAAGrE,kBAAkB,CAACX,EAAE,CAAC;IAC/CiF,gBAAgB,GAAGD,oBAAoB,CAACC,gBAAgB;IACxDC,iBAAiB,GAAGF,oBAAoB,CAACE,iBAAiB;EAC5DzD,cAAc,CAACnC,OAAO,GAAG,KAAK;EAC9BmC,cAAc,CAAC0B,UAAU,GAAG,KAAK;EACjC1B,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;EAGnC,IAAI4E,SAAS,IAAI,IAAI,EAAE;IACrB,IAAIG,gBAAgB,IAAI,IAAI,EAAE;MAC5BxD,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;MACnCuB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;MACnE2B,gBAAgB,CAACxD,cAAc,CAAC;IAClC;IACAhB,sBAAsB,CAACuB,cAAc,CAAC;EACxC,CAAC,MAEI;IACH,IAAImD,oBAAoB,GAAGxE,kBAAkB,CAACmE,SAAS,CAAC;MACtD7B,oBAAoB,GAAGkC,oBAAoB,CAAClC,oBAAoB;MAChEC,6BAA6B,GAAGiC,oBAAoB,CAACjC,6BAA6B;IACpF,IAAIkC,aAAa,GAAG,IAAI;IACxB,IAAIlC,6BAA6B,IAAI,IAAI,EAAE;MACzCzB,cAAc,CAAC2B,aAAa,GAAG2B,WAAW;MAC1CtD,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,+BAA+B;MAChF,IAAIJ,6BAA6B,CAACzB,cAAc,CAAC,KAAK,KAAK,EAAE;QAC3D2D,aAAa,GAAG,KAAK;MACvB;IACF;IACA,IAAIA,aAAa,EAAE;MAEjB,IAAInC,oBAAoB,IAAI,IAAI,EAAE;QAChCxB,cAAc,CAAC2B,aAAa,GAAG2B,WAAW;QAC1CtD,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,sBAAsB;QACvEL,oBAAoB,CAACxB,cAAc,CAAC;MACtC;MAEA,IAAIwD,gBAAgB,IAAI,IAAI,EAAE;QAC5BxD,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;QACnCuB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;QACnE2B,gBAAgB,CAACxD,cAAc,CAAC;MAClC;MACAhB,sBAAsB,CAACuB,cAAc,CAAC;IACxC,CAAC,MAAM;MAEL,IAAIkD,iBAAiB,IAAI,IAAI,EAAE;QAC7BzD,cAAc,CAAC2B,aAAa,GAAGlD,IAAI;QACnCuB,cAAc,CAAC4B,cAAc,CAACC,gBAAgB,GAAG,mBAAmB;QACpE4B,iBAAiB,CAACzD,cAAc,CAAC;MACnC;IACF;EACF;AACF;AAUA,IAAI4D,0BAA0B,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;AACnD,IAAIC,yBAAyB,GAAG,CAEhC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAEhD,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAEpD,aAAa,EAAE,QAAQ,EAAE,iBAAiB,CAAC;AAC3C,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,IAAIpG,SAAS,IAAIqE,MAAM,CAACgC,4BAA4B,IAAI,IAAI,EAAE;IAC5DhC,MAAM,CAACiC,gBAAgB,CAAC,MAAM,EAAE3E,aAAa,CAAC;IAC9CwE,yBAAyB,CAACI,OAAO,CAAC,UAAA1E,SAAS,EAAI;MAC7C2E,QAAQ,CAACF,gBAAgB,CAACzE,SAAS,EAAEF,aAAa,CAAC;IACrD,CAAC,CAAC;IACFuE,0BAA0B,CAACK,OAAO,CAAC,UAAA1E,SAAS,EAAI;MAC9C2E,QAAQ,CAACF,gBAAgB,CAACzE,SAAS,EAAEF,aAAa,EAAE,IAAI,CAAC;IAC3D,CAAC,CAAC;IACF0C,MAAM,CAACgC,4BAA4B,GAAG,IAAI;EAC5C;AACF;AAKA,OAAO,SAASI,OAAOA,CAAC5F,EAAE,EAAEE,IAAI,EAAEU,MAAM,EAAE;EACxC3B,cAAc,CAACiB,IAAI,EAAEF,EAAE,CAAC;EACxBG,qBAAqB,CAAC0F,GAAG,CAAC7F,EAAE,EAAEY,MAAM,CAAC;AACvC;AAKA,OAAO,SAASkF,UAAUA,CAAC9F,EAAE,EAAE;EAC7B,IAAIO,gBAAgB,CAACP,EAAE,KAAKA,EAAE,EAAE;IAC9B+F,kBAAkB,CAAC,CAAC;EACtB;EACA,IAAI5F,qBAAqB,CAAC6F,GAAG,CAAChG,EAAE,CAAC,EAAE;IACjCG,qBAAqB,CAAC8F,MAAM,CAACjG,EAAE,CAAC;EAClC;AACF;AAOA,OAAO,SAAS+F,kBAAkBA,CAAA,EAAG;EACnC,IAAIG,kBAAkB,GAAG3F,gBAAgB;IACvCP,EAAE,GAAGkG,kBAAkB,CAAClG,EAAE;IAC1BE,IAAI,GAAGgG,kBAAkB,CAAChG,IAAI;EAChC,IAAIF,EAAE,IAAI,IAAI,IAAIE,IAAI,IAAI,IAAI,EAAE;IAC9B,IAAIiG,oBAAoB,GAAGxF,kBAAkB,CAACX,EAAE,CAAC;MAC/CiD,oBAAoB,GAAGkD,oBAAoB,CAAClD,oBAAoB;IAClE,IAAIA,oBAAoB,IAAI,IAAI,EAAE;MAChC,IAAImD,KAAK,GAAG/H,oBAAoB,CAAC,CAAC,CAAC,EAAEmC,0BAA0B,CAAC;MAChE4F,KAAK,CAAChD,aAAa,GAAGlD,IAAI;MAC1B+C,oBAAoB,CAACmD,KAAK,CAAC;IAC7B;IACA3F,sBAAsB,CAACV,cAAc,CAAC;EACxC;EACAM,sBAAsB,GAAG,KAAK;EAC9BC,iBAAiB,GAAG,CAAC;AACvB;AAMA,OAAO,SAAS+F,gBAAgBA,CAAA,EAAG;EACjC,OAAO9F,gBAAgB,CAACL,IAAI;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}