{"ast": null, "code": "'use client';\n\nimport FlatList from \"../../vendor/react-native/FlatList\";\nexport default FlatList;", "map": {"version": 3, "names": ["FlatList"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/exports/FlatList/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport FlatList from '../../vendor/react-native/FlatList';\nexport default FlatList;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,QAAQ;AACf,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}