{"ast": null, "code": "import Group from \"./Group\";\nimport Screen from \"./Screen\";\nexport default function createNavigatorFactory(Navigator) {\n  return function () {\n    if (arguments[0] !== undefined) {\n      throw new Error(\"Creating a navigator doesn't take an argument. Maybe you are trying to use React Navigation 4 API? See https://reactnavigation.org/docs/hello-react-navigation for the latest API and guides.\");\n    }\n    return {\n      Navigator: Navigator,\n      Group: Group,\n      Screen: Screen\n    };\n  };\n}", "map": {"version": 3, "names": ["Group", "Screen", "createNavigatorFactory", "Navigator", "arguments", "undefined", "Error"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/createNavigatorFactory.tsx"], "sourcesContent": ["import type { NavigationState, ParamListBase } from '@react-navigation/routers';\nimport type * as React from 'react';\n\nimport Group from './Group';\nimport Screen from './Screen';\nimport type { EventMapBase, TypedNavigator } from './types';\n\n/**\n * Higher order component to create a `Navigator` and `Screen` pair.\n * Custom navigators should wrap the navigator component in `createNavigator` before exporting.\n *\n * @param Navigator The navigtor component to wrap.\n * @returns Factory method to create a `Navigator` and `Screen` pair.\n */\nexport default function createNavigatorFactory<\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase,\n  NavigatorComponent extends React.ComponentType<any>\n>(Navigator: NavigatorComponent) {\n  return function <ParamList extends ParamListBase>(): TypedNavigator<\n    ParamList,\n    State,\n    ScreenOptions,\n    EventMap,\n    typeof Navigator\n  > {\n    if (arguments[0] !== undefined) {\n      throw new Error(\n        \"Creating a navigator doesn't take an argument. Maybe you are trying to use React Navigation 4 API? See https://reactnavigation.org/docs/hello-react-navigation for the latest API and guides.\"\n      );\n    }\n\n    return {\n      Navigator,\n      Group,\n      Screen,\n    };\n  };\n}\n"], "mappings": "AAGA,OAAOA,KAAK;AACZ,OAAOC,MAAM;AAUb,eAAe,SAASC,sBAAsBA,CAK5CC,SAA6B,EAAE;EAC/B,OAAO,YAML;IACA,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;MAC9B,MAAM,IAAIC,KAAK,CACb,+LAA+L,CAChM;IACH;IAEA,OAAO;MACLH,SAAS,EAATA,SAAS;MACTH,KAAK,EAALA,KAAK;MACLC,MAAA,EAAAA;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}