{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedNode from \"./AnimatedNode\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nvar AnimatedTransform = function (_AnimatedWithChildren) {\n  function AnimatedTransform(transforms) {\n    var _this;\n    _classCallCheck(this, AnimatedTransform);\n    _this = _callSuper(this, AnimatedTransform);\n    _this._transforms = transforms;\n    return _this;\n  }\n  _inherits(AnimatedTransform, _AnimatedWithChildren);\n  return _createClass(AnimatedTransform, [{\n    key: \"__makeNative\",\n    value: function __makeNative() {\n      this._transforms.forEach(function (transform) {\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            value.__makeNative();\n          }\n        }\n      });\n      _superPropGet(AnimatedTransform, \"__makeNative\", this, 3)([]);\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return this._transforms.map(function (transform) {\n        var result = {};\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            result[key] = value.__getValue();\n          } else {\n            result[key] = value;\n          }\n        }\n        return result;\n      });\n    }\n  }, {\n    key: \"__getAnimatedValue\",\n    value: function __getAnimatedValue() {\n      return this._transforms.map(function (transform) {\n        var result = {};\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            result[key] = value.__getAnimatedValue();\n          } else {\n            result[key] = value;\n          }\n        }\n        return result;\n      });\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      var _this2 = this;\n      this._transforms.forEach(function (transform) {\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            value.__addChild(_this2);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      var _this3 = this;\n      this._transforms.forEach(function (transform) {\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            value.__removeChild(_this3);\n          }\n        }\n      });\n      _superPropGet(AnimatedTransform, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      var transConfigs = [];\n      this._transforms.forEach(function (transform) {\n        for (var key in transform) {\n          var value = transform[key];\n          if (value instanceof AnimatedNode) {\n            transConfigs.push({\n              type: 'animated',\n              property: key,\n              nodeTag: value.__getNativeTag()\n            });\n          } else {\n            transConfigs.push({\n              type: 'static',\n              property: key,\n              value: NativeAnimatedHelper.transformDataType(value)\n            });\n          }\n        }\n      });\n      NativeAnimatedHelper.validateTransform(transConfigs);\n      return {\n        type: 'transform',\n        transforms: transConfigs\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedTransform;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedNode", "AnimatedWithChildren", "NativeAnimatedHelper", "AnimatedTransform", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "transforms", "_this", "_transforms", "key", "value", "__makeNative", "for<PERSON>ach", "transform", "__getValue", "map", "result", "__getAnimatedValue", "__attach", "_this2", "__add<PERSON><PERSON>d", "__detach", "_this3", "__remove<PERSON><PERSON>d", "__getNativeConfig", "transConfigs", "push", "type", "property", "nodeTag", "__getNativeTag", "transformDataType", "validateTransform"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedTransform.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedNode from './AnimatedNode';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nclass AnimatedTransform extends AnimatedWithChildren {\n  constructor(transforms) {\n    super();\n    this._transforms = transforms;\n  }\n  __makeNative() {\n    this._transforms.forEach(transform => {\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          value.__makeNative();\n        }\n      }\n    });\n    super.__makeNative();\n  }\n  __getValue() {\n    return this._transforms.map(transform => {\n      var result = {};\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          result[key] = value.__getValue();\n        } else {\n          result[key] = value;\n        }\n      }\n      return result;\n    });\n  }\n  __getAnimatedValue() {\n    return this._transforms.map(transform => {\n      var result = {};\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          result[key] = value.__getAnimatedValue();\n        } else {\n          // All transform components needed to recompose matrix\n          result[key] = value;\n        }\n      }\n      return result;\n    });\n  }\n  __attach() {\n    this._transforms.forEach(transform => {\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          value.__addChild(this);\n        }\n      }\n    });\n  }\n  __detach() {\n    this._transforms.forEach(transform => {\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          value.__removeChild(this);\n        }\n      }\n    });\n    super.__detach();\n  }\n  __getNativeConfig() {\n    var transConfigs = [];\n    this._transforms.forEach(transform => {\n      for (var key in transform) {\n        var value = transform[key];\n        if (value instanceof AnimatedNode) {\n          transConfigs.push({\n            type: 'animated',\n            property: key,\n            nodeTag: value.__getNativeTag()\n          });\n        } else {\n          transConfigs.push({\n            type: 'static',\n            property: key,\n            value: NativeAnimatedHelper.transformDataType(value)\n          });\n        }\n      }\n    });\n    NativeAnimatedHelper.validateTransform(transConfigs);\n    return {\n      type: 'transform',\n      transforms: transConfigs\n    };\n  }\n}\nexport default AnimatedTransform;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,YAAY;AACnB,OAAOC,oBAAoB;AAC3B,OAAOC,oBAAoB;AAAgC,IACrDC,iBAAiB,aAAAC,qBAAA;EACrB,SAAAD,kBAAYE,UAAU,EAAE;IAAA,IAAAC,KAAA;IAAA5B,eAAA,OAAAyB,iBAAA;IACtBG,KAAA,GAAAtB,UAAA,OAAAmB,iBAAA;IACAG,KAAA,CAAKC,WAAW,GAAGF,UAAU;IAAC,OAAAC,KAAA;EAChC;EAACvB,SAAA,CAAAoB,iBAAA,EAAAC,qBAAA;EAAA,OAAAzB,YAAA,CAAAwB,iBAAA;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAAA,EAAG;MACb,IAAI,CAACH,WAAW,CAACI,OAAO,CAAC,UAAAC,SAAS,EAAI;QACpC,KAAK,IAAIJ,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCS,KAAK,CAACC,YAAY,CAAC,CAAC;UACtB;QACF;MACF,CAAC,CAAC;MACFb,aAAA,CAAAM,iBAAA;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAI,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,UAAAF,SAAS,EAAI;QACvC,IAAIG,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAIP,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCe,MAAM,CAACP,GAAG,CAAC,GAAGC,KAAK,CAACI,UAAU,CAAC,CAAC;UAClC,CAAC,MAAM;YACLE,MAAM,CAACP,GAAG,CAAC,GAAGC,KAAK;UACrB;QACF;QACA,OAAOM,MAAM;MACf,CAAC,CAAC;IACJ;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACT,WAAW,CAACO,GAAG,CAAC,UAAAF,SAAS,EAAI;QACvC,IAAIG,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAIP,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCe,MAAM,CAACP,GAAG,CAAC,GAAGC,KAAK,CAACO,kBAAkB,CAAC,CAAC;UAC1C,CAAC,MAAM;YAELD,MAAM,CAACP,GAAG,CAAC,GAAGC,KAAK;UACrB;QACF;QACA,OAAOM,MAAM;MACf,CAAC,CAAC;IACJ;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAQ,QAAQA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACT,IAAI,CAACX,WAAW,CAACI,OAAO,CAAC,UAAAC,SAAS,EAAI;QACpC,KAAK,IAAIJ,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCS,KAAK,CAACU,UAAU,CAACD,MAAI,CAAC;UACxB;QACF;MACF,CAAC,CAAC;IACJ;EAAC;IAAAV,GAAA;IAAAC,KAAA,EACD,SAAAW,QAAQA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACT,IAAI,CAACd,WAAW,CAACI,OAAO,CAAC,UAAAC,SAAS,EAAI;QACpC,KAAK,IAAIJ,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCS,KAAK,CAACa,aAAa,CAACD,MAAI,CAAC;UAC3B;QACF;MACF,CAAC,CAAC;MACFxB,aAAA,CAAAM,iBAAA;IACF;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAc,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAI,CAACjB,WAAW,CAACI,OAAO,CAAC,UAAAC,SAAS,EAAI;QACpC,KAAK,IAAIJ,GAAG,IAAII,SAAS,EAAE;UACzB,IAAIH,KAAK,GAAGG,SAAS,CAACJ,GAAG,CAAC;UAC1B,IAAIC,KAAK,YAAYT,YAAY,EAAE;YACjCwB,YAAY,CAACC,IAAI,CAAC;cAChBC,IAAI,EAAE,UAAU;cAChBC,QAAQ,EAAEnB,GAAG;cACboB,OAAO,EAAEnB,KAAK,CAACoB,cAAc,CAAC;YAChC,CAAC,CAAC;UACJ,CAAC,MAAM;YACLL,YAAY,CAACC,IAAI,CAAC;cAChBC,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAEnB,GAAG;cACbC,KAAK,EAAEP,oBAAoB,CAAC4B,iBAAiB,CAACrB,KAAK;YACrD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MACFP,oBAAoB,CAAC6B,iBAAiB,CAACP,YAAY,CAAC;MACpD,OAAO;QACLE,IAAI,EAAE,WAAW;QACjBrB,UAAU,EAAEmB;MACd,CAAC;IACH;EAAC;AAAA,EA3F6BvB,oBAAoB;AA6FpD,eAAeE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}