{"ast": null, "code": "function additiveInverseArray(arr) {\n  return arr.map(function (i) {\n    return -i;\n  });\n}\nexport { additiveInverseArray };", "map": {"version": 3, "names": ["additiveInverseArray", "arr", "map", "i"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/utils/array.js"], "sourcesContent": ["function additiveInverseArray(arr) {\n    return arr.map((i) => -i);\n}\nexport { additiveInverseArray };\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,GAAG,EAAE;EAC/B,OAAOA,GAAG,CAACC,GAAG,CAAC,UAACC,CAAC;IAAA,OAAK,CAACA,CAAC;EAAA,EAAC;AAC7B;AACA,SAASH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}