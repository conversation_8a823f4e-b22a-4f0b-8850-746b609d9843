{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React from 'react';\nvar getLayoutValue = function getLayoutValue(key) {\n  return function (event) {\n    var _event$nativeEvent$la, _event$nativeEvent, _event$nativeEvent$la2;\n    return (_event$nativeEvent$la = event == null ? void 0 : (_event$nativeEvent = event.nativeEvent) == null ? void 0 : (_event$nativeEvent$la2 = _event$nativeEvent.layout) == null ? void 0 : _event$nativeEvent$la2[key]) != null ? _event$nativeEvent$la : 0;\n  };\n};\nexport function useViewDimensions() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    _ref$heightOffset = _ref.heightOffset,\n    heightOffset = _ref$heightOffset === void 0 ? 0 : _ref$heightOffset,\n    _ref$widthOffset = _ref.widthOffset,\n    widthOffset = _ref$widthOffset === void 0 ? 0 : _ref$widthOffset;\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    height = _React$useState2[0],\n    setHeight = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    width = _React$useState4[0],\n    setWidth = _React$useState4[1];\n  var computeViewDimensions = React.useCallback(function (event) {\n    var h = getLayoutValue('height')(event);\n    var w = getLayoutValue('width')(event);\n    setHeight(h + heightOffset);\n    setWidth(w + widthOffset);\n  }, [heightOffset, widthOffset]);\n  return {\n    computeViewDimensions: computeViewDimensions,\n    height: height,\n    width: width\n  };\n}", "map": {"version": 3, "names": ["React", "getLayoutValue", "key", "event", "_event$nativeEvent$la", "_event$nativeEvent", "_event$nativeEvent$la2", "nativeEvent", "layout", "useViewDimensions", "_ref", "arguments", "length", "undefined", "_ref$heightOffset", "heightOffset", "_ref$widthOffset", "widthOffset", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "height", "setHeight", "_React$useState3", "_React$useState4", "width", "<PERSON><PERSON><PERSON><PERSON>", "computeViewDimensions", "useCallback", "h", "w"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/hooks/useViewDimensions.js"], "sourcesContent": ["import React from 'react';\nconst getLayoutValue = (key) => (event) => event?.nativeEvent?.layout?.[key] ?? 0;\n/**\n * Retrieves View dimensions (height, width) from a LayoutChangeEvent and sets them on state\n */\nexport function useViewDimensions({ heightOffset = 0, widthOffset = 0 } = {}) {\n    const [height, setHeight] = React.useState(0);\n    const [width, setWidth] = React.useState(0);\n    const computeViewDimensions = React.useCallback((event) => {\n        const h = getLayoutValue('height')(event);\n        const w = getLayoutValue('width')(event);\n        setHeight(h + heightOffset);\n        setWidth(w + widthOffset);\n    }, [heightOffset, widthOffset]);\n    return {\n        computeViewDimensions,\n        height,\n        width\n    };\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,GAAG;EAAA,OAAK,UAACC,KAAK;IAAA,IAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA;IAAA,QAAAF,qBAAA,GAAKD,KAAK,qBAAAE,kBAAA,GAALF,KAAK,CAAEI,WAAW,sBAAAD,sBAAA,GAAlBD,kBAAA,CAAoBG,MAAM,qBAA1BF,sBAAA,CAA6BJ,GAAG,CAAC,YAAAE,qBAAA,GAAI,CAAC;EAAA;AAAA;AAIjF,OAAO,SAASK,iBAAiBA,CAAA,EAA6C;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;IAAAG,iBAAA,GAAAJ,IAAA,CAAxCK,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;IAAAE,gBAAA,GAAAN,IAAA,CAAEO,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,CAAC,GAAAA,gBAAA;EACjE,IAAAE,eAAA,GAA4BlB,KAAK,CAACmB,QAAQ,CAAC,CAAC,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAtCI,MAAM,GAAAF,gBAAA;IAAEG,SAAS,GAAAH,gBAAA;EACxB,IAAAI,gBAAA,GAA0BxB,KAAK,CAACmB,QAAQ,CAAC,CAAC,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAApCE,KAAK,GAAAD,gBAAA;IAAEE,QAAQ,GAAAF,gBAAA;EACtB,IAAMG,qBAAqB,GAAG5B,KAAK,CAAC6B,WAAW,CAAC,UAAC1B,KAAK,EAAK;IACvD,IAAM2B,CAAC,GAAG7B,cAAc,CAAC,QAAQ,CAAC,CAACE,KAAK,CAAC;IACzC,IAAM4B,CAAC,GAAG9B,cAAc,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC;IACxCoB,SAAS,CAACO,CAAC,GAAGf,YAAY,CAAC;IAC3BY,QAAQ,CAACI,CAAC,GAAGd,WAAW,CAAC;EAC7B,CAAC,EAAE,CAACF,YAAY,EAAEE,WAAW,CAAC,CAAC;EAC/B,OAAO;IACHW,qBAAqB,EAArBA,qBAAqB;IACrBN,MAAM,EAANA,MAAM;IACNI,KAAK,EAALA;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}