{"ast": null, "code": "export function upperBound(n, max) {\n  return n > max ? max : n;\n}\nexport function lowerBound(n, min) {\n  return n < min ? min : n;\n}\nexport function bound(n, min, max) {\n  return upperBound(lowerBound(n, min), max);\n}", "map": {"version": 3, "names": ["upperBound", "n", "max", "lowerBound", "min", "bound"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/utils/number.js"], "sourcesContent": ["export function upperBound(n, max) {\n    return n > max ? max : n;\n}\nexport function lowerBound(n, min) {\n    return n < min ? min : n;\n}\nexport function bound(n, min, max) {\n    return upperBound(lowerBound(n, min), max);\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC/B,OAAOD,CAAC,GAAGC,GAAG,GAAGA,GAAG,GAAGD,CAAC;AAC5B;AACA,OAAO,SAASE,UAAUA,CAACF,CAAC,EAAEG,GAAG,EAAE;EAC/B,OAAOH,CAAC,GAAGG,GAAG,GAAGA,GAAG,GAAGH,CAAC;AAC5B;AACA,OAAO,SAASI,KAAKA,CAACJ,CAAC,EAAEG,GAAG,EAAEF,GAAG,EAAE;EAC/B,OAAOF,UAAU,CAACG,UAAU,CAACF,CAAC,EAAEG,GAAG,CAAC,EAAEF,GAAG,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}