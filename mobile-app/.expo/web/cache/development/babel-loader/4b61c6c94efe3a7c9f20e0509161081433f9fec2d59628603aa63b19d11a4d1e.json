{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"height\", \"minHeight\", \"maxHeight\", \"backgroundColor\", \"borderBottomColor\", \"borderBottomEndRadius\", \"borderBottomLeftRadius\", \"borderBottomRightRadius\", \"borderBottomStartRadius\", \"borderBottomWidth\", \"borderColor\", \"borderEndColor\", \"borderEndWidth\", \"borderLeftColor\", \"borderLeftWidth\", \"borderRadius\", \"borderRightColor\", \"borderRightWidth\", \"borderStartColor\", \"borderStartWidth\", \"borderStyle\", \"borderTopColor\", \"borderTopEndRadius\", \"borderTopLeftRadius\", \"borderTopRightRadius\", \"borderTopStartRadius\", \"borderTopWidth\", \"borderWidth\", \"boxShadow\", \"elevation\", \"shadowColor\", \"shadowOffset\", \"shadowOpacity\", \"shadowRadius\", \"opacity\", \"transform\"];\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context';\nimport getDefaultHeaderHeight from \"./getDefaultHeaderHeight\";\nimport HeaderBackground from \"./HeaderBackground\";\nimport HeaderShownContext from \"./HeaderShownContext\";\nimport HeaderTitle from \"./HeaderTitle\";\nvar warnIfHeaderStylesDefined = function warnIfHeaderStylesDefined(styles) {\n  Object.keys(styles).forEach(function (styleProp) {\n    var value = styles[styleProp];\n    if (styleProp === 'position' && value === 'absolute') {\n      console.warn(\"position: 'absolute' is not supported on headerStyle. If you would like to render content under the header, use the 'headerTransparent' option.\");\n    } else if (value !== undefined) {\n      console.warn(`${styleProp} was given a value of ${value}, this has no effect on headerStyle.`);\n    }\n  });\n};\nexport default function Header(props) {\n  var insets = useSafeAreaInsets();\n  var frame = useSafeAreaFrame();\n  var isParentHeaderShown = React.useContext(HeaderShownContext);\n  var hasDynamicIsland = Platform.OS === 'ios' && insets.top > 50;\n  var statusBarHeight = hasDynamicIsland ? insets.top - 5 : insets.top;\n  var _props$layout = props.layout,\n    layout = _props$layout === void 0 ? frame : _props$layout,\n    _props$modal = props.modal,\n    modal = _props$modal === void 0 ? false : _props$modal,\n    title = props.title,\n    customTitle = props.headerTitle,\n    _props$headerTitleAli = props.headerTitleAlign,\n    headerTitleAlign = _props$headerTitleAli === void 0 ? Platform.select({\n      ios: 'center',\n      default: 'left'\n    }) : _props$headerTitleAli,\n    headerLeft = props.headerLeft,\n    headerLeftLabelVisible = props.headerLeftLabelVisible,\n    headerTransparent = props.headerTransparent,\n    headerTintColor = props.headerTintColor,\n    headerBackground = props.headerBackground,\n    headerRight = props.headerRight,\n    titleAllowFontScaling = props.headerTitleAllowFontScaling,\n    titleStyle = props.headerTitleStyle,\n    leftContainerStyle = props.headerLeftContainerStyle,\n    rightContainerStyle = props.headerRightContainerStyle,\n    titleContainerStyle = props.headerTitleContainerStyle,\n    backgroundContainerStyle = props.headerBackgroundContainerStyle,\n    customHeaderStyle = props.headerStyle,\n    headerShadowVisible = props.headerShadowVisible,\n    headerPressColor = props.headerPressColor,\n    headerPressOpacity = props.headerPressOpacity,\n    _props$headerStatusBa = props.headerStatusBarHeight,\n    headerStatusBarHeight = _props$headerStatusBa === void 0 ? isParentHeaderShown ? 0 : statusBarHeight : _props$headerStatusBa;\n  var defaultHeight = getDefaultHeaderHeight(layout, modal, headerStatusBarHeight);\n  var _StyleSheet$flatten = StyleSheet.flatten(customHeaderStyle || {}),\n    _StyleSheet$flatten$h = _StyleSheet$flatten.height,\n    height = _StyleSheet$flatten$h === void 0 ? defaultHeight : _StyleSheet$flatten$h,\n    minHeight = _StyleSheet$flatten.minHeight,\n    maxHeight = _StyleSheet$flatten.maxHeight,\n    backgroundColor = _StyleSheet$flatten.backgroundColor,\n    borderBottomColor = _StyleSheet$flatten.borderBottomColor,\n    borderBottomEndRadius = _StyleSheet$flatten.borderBottomEndRadius,\n    borderBottomLeftRadius = _StyleSheet$flatten.borderBottomLeftRadius,\n    borderBottomRightRadius = _StyleSheet$flatten.borderBottomRightRadius,\n    borderBottomStartRadius = _StyleSheet$flatten.borderBottomStartRadius,\n    borderBottomWidth = _StyleSheet$flatten.borderBottomWidth,\n    borderColor = _StyleSheet$flatten.borderColor,\n    borderEndColor = _StyleSheet$flatten.borderEndColor,\n    borderEndWidth = _StyleSheet$flatten.borderEndWidth,\n    borderLeftColor = _StyleSheet$flatten.borderLeftColor,\n    borderLeftWidth = _StyleSheet$flatten.borderLeftWidth,\n    borderRadius = _StyleSheet$flatten.borderRadius,\n    borderRightColor = _StyleSheet$flatten.borderRightColor,\n    borderRightWidth = _StyleSheet$flatten.borderRightWidth,\n    borderStartColor = _StyleSheet$flatten.borderStartColor,\n    borderStartWidth = _StyleSheet$flatten.borderStartWidth,\n    borderStyle = _StyleSheet$flatten.borderStyle,\n    borderTopColor = _StyleSheet$flatten.borderTopColor,\n    borderTopEndRadius = _StyleSheet$flatten.borderTopEndRadius,\n    borderTopLeftRadius = _StyleSheet$flatten.borderTopLeftRadius,\n    borderTopRightRadius = _StyleSheet$flatten.borderTopRightRadius,\n    borderTopStartRadius = _StyleSheet$flatten.borderTopStartRadius,\n    borderTopWidth = _StyleSheet$flatten.borderTopWidth,\n    borderWidth = _StyleSheet$flatten.borderWidth,\n    boxShadow = _StyleSheet$flatten.boxShadow,\n    elevation = _StyleSheet$flatten.elevation,\n    shadowColor = _StyleSheet$flatten.shadowColor,\n    shadowOffset = _StyleSheet$flatten.shadowOffset,\n    shadowOpacity = _StyleSheet$flatten.shadowOpacity,\n    shadowRadius = _StyleSheet$flatten.shadowRadius,\n    opacity = _StyleSheet$flatten.opacity,\n    transform = _StyleSheet$flatten.transform,\n    unsafeStyles = _objectWithoutProperties(_StyleSheet$flatten, _excluded);\n  if (process.env.NODE_ENV !== 'production') {\n    warnIfHeaderStylesDefined(unsafeStyles);\n  }\n  var safeStyles = {\n    backgroundColor: backgroundColor,\n    borderBottomColor: borderBottomColor,\n    borderBottomEndRadius: borderBottomEndRadius,\n    borderBottomLeftRadius: borderBottomLeftRadius,\n    borderBottomRightRadius: borderBottomRightRadius,\n    borderBottomStartRadius: borderBottomStartRadius,\n    borderBottomWidth: borderBottomWidth,\n    borderColor: borderColor,\n    borderEndColor: borderEndColor,\n    borderEndWidth: borderEndWidth,\n    borderLeftColor: borderLeftColor,\n    borderLeftWidth: borderLeftWidth,\n    borderRadius: borderRadius,\n    borderRightColor: borderRightColor,\n    borderRightWidth: borderRightWidth,\n    borderStartColor: borderStartColor,\n    borderStartWidth: borderStartWidth,\n    borderStyle: borderStyle,\n    borderTopColor: borderTopColor,\n    borderTopEndRadius: borderTopEndRadius,\n    borderTopLeftRadius: borderTopLeftRadius,\n    borderTopRightRadius: borderTopRightRadius,\n    borderTopStartRadius: borderTopStartRadius,\n    borderTopWidth: borderTopWidth,\n    borderWidth: borderWidth,\n    boxShadow: boxShadow,\n    elevation: elevation,\n    shadowColor: shadowColor,\n    shadowOffset: shadowOffset,\n    shadowOpacity: shadowOpacity,\n    shadowRadius: shadowRadius,\n    opacity: opacity,\n    transform: transform\n  };\n  for (var styleProp in safeStyles) {\n    if (safeStyles[styleProp] === undefined) {\n      delete safeStyles[styleProp];\n    }\n  }\n  var backgroundStyle = [safeStyles, headerShadowVisible === false && {\n    elevation: 0,\n    shadowOpacity: 0,\n    borderBottomWidth: 0\n  }];\n  var leftButton = headerLeft ? headerLeft({\n    tintColor: headerTintColor,\n    pressColor: headerPressColor,\n    pressOpacity: headerPressOpacity,\n    labelVisible: headerLeftLabelVisible\n  }) : null;\n  var rightButton = headerRight ? headerRight({\n    tintColor: headerTintColor,\n    pressColor: headerPressColor,\n    pressOpacity: headerPressOpacity\n  }) : null;\n  var headerTitle = typeof customTitle !== 'function' ? function (props) {\n    return React.createElement(HeaderTitle, props);\n  } : customTitle;\n  return React.createElement(React.Fragment, null, React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [StyleSheet.absoluteFill, {\n      zIndex: 0\n    }, backgroundContainerStyle]\n  }, headerBackground ? headerBackground({\n    style: backgroundStyle\n  }) : headerTransparent ? null : React.createElement(HeaderBackground, {\n    style: backgroundStyle\n  })), React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [{\n      height: height,\n      minHeight: minHeight,\n      maxHeight: maxHeight,\n      opacity: opacity,\n      transform: transform\n    }]\n  }, React.createElement(View, {\n    pointerEvents: \"none\",\n    style: {\n      height: headerStatusBarHeight\n    }\n  }), React.createElement(View, {\n    pointerEvents: \"box-none\",\n    style: styles.content\n  }, React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [styles.left, headerTitleAlign === 'center' && styles.expand, {\n      marginStart: insets.left\n    }, leftContainerStyle]\n  }, leftButton), React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [styles.title, {\n      maxWidth: headerTitleAlign === 'center' ? layout.width - ((leftButton ? headerLeftLabelVisible !== false ? 80 : 32 : 16) + Math.max(insets.left, insets.right)) * 2 : layout.width - ((leftButton ? 72 : 16) + (rightButton ? 72 : 16) + insets.left - insets.right)\n    }, titleContainerStyle]\n  }, headerTitle({\n    children: title,\n    allowFontScaling: titleAllowFontScaling,\n    tintColor: headerTintColor,\n    style: titleStyle\n  })), React.createElement(Animated.View, {\n    pointerEvents: \"box-none\",\n    style: [styles.right, styles.expand, {\n      marginEnd: insets.right\n    }, rightContainerStyle]\n  }, rightButton))));\n}\nvar styles = StyleSheet.create({\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'stretch'\n  },\n  title: {\n    marginHorizontal: 16,\n    justifyContent: 'center'\n  },\n  left: {\n    justifyContent: 'center',\n    alignItems: 'flex-start'\n  },\n  right: {\n    justifyContent: 'center',\n    alignItems: 'flex-end'\n  },\n  expand: {\n    flexGrow: 1,\n    flexBasis: 0\n  }\n});", "map": {"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "useSafeAreaFrame", "useSafeAreaInsets", "getDefaultHeaderHeight", "HeaderBackground", "HeaderShownContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warnIfHeaderStylesDefined", "styles", "Object", "keys", "for<PERSON>ach", "styleProp", "value", "console", "warn", "undefined", "Header", "props", "insets", "frame", "isParentHeaderShown", "useContext", "hasDynamicIsland", "OS", "top", "statusBarHeight", "_props$layout", "layout", "_props$modal", "modal", "title", "customTitle", "headerTitle", "_props$headerTitleAli", "headerTitleAlign", "select", "ios", "default", "headerLeft", "headerLeftLabelVisible", "headerTransparent", "headerTintColor", "headerBackground", "headerRight", "titleAllowFontScaling", "headerTitleAllowFontScaling", "titleStyle", "headerTitleStyle", "leftContainerStyle", "headerLeftContainerStyle", "rightContainerStyle", "headerRightContainerStyle", "titleContainerStyle", "headerTitleContainerStyle", "backgroundContainerStyle", "headerBackgroundContainerStyle", "customHeaderStyle", "headerStyle", "headerShadowVisible", "headerPressColor", "headerPressOpacity", "_props$headerStatusBa", "headerStatusBarHeight", "defaultHeight", "_StyleSheet$flatten", "flatten", "_StyleSheet$flatten$h", "height", "minHeight", "maxHeight", "backgroundColor", "borderBottomColor", "borderBottomEndRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderBottomStartRadius", "borderBottomWidth", "borderColor", "borderEndColor", "borderEndWidth", "borderLeftColor", "borderLeftWidth", "borderRadius", "borderRightColor", "borderRightWidth", "borderStartColor", "borderStartWidth", "borderStyle", "borderTopColor", "borderTopEndRadius", "borderTopLeftRadius", "borderTopRightRadius", "borderTopStartRadius", "borderTopWidth", "borderWidth", "boxShadow", "elevation", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "opacity", "transform", "unsafeStyles", "_objectWithoutProperties", "_excluded", "process", "env", "NODE_ENV", "safeStyles", "backgroundStyle", "leftButton", "tintColor", "pressColor", "pressOpacity", "labelVisible", "rightB<PERSON>on", "createElement", "Fragment", "pointerEvents", "style", "absoluteFill", "zIndex", "content", "left", "expand", "marginStart", "max<PERSON><PERSON><PERSON>", "width", "Math", "max", "right", "children", "allowFontScaling", "marginEnd", "create", "flex", "flexDirection", "alignItems", "marginHorizontal", "justifyContent", "flexGrow", "flexBasis"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/elements/src/Header/Header.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Animated, Platform, StyleSheet, View, ViewStyle } from 'react-native';\nimport {\n  useSafeAreaFrame,\n  useSafeAreaInsets,\n} from 'react-native-safe-area-context';\n\nimport type { HeaderOptions, Layout } from '../types';\nimport getDefaultHeaderHeight from './getDefaultHeaderHeight';\nimport HeaderBackground from './HeaderBackground';\nimport HeaderShownContext from './HeaderShownContext';\nimport HeaderTitle from './HeaderTitle';\n\ntype Props = HeaderOptions & {\n  /**\n   * Whether the header is in a modal\n   */\n  modal?: boolean;\n  /**\n   * Layout of the screen.\n   */\n  layout?: Layout;\n  /**\n   * Title text for the header.\n   */\n  title: string;\n};\n\nconst warnIfHeaderStylesDefined = (styles: Record<string, any>) => {\n  Object.keys(styles).forEach((styleProp) => {\n    const value = styles[styleProp];\n\n    if (styleProp === 'position' && value === 'absolute') {\n      console.warn(\n        \"position: 'absolute' is not supported on headerStyle. If you would like to render content under the header, use the 'headerTransparent' option.\"\n      );\n    } else if (value !== undefined) {\n      console.warn(\n        `${styleProp} was given a value of ${value}, this has no effect on headerStyle.`\n      );\n    }\n  });\n};\n\nexport default function Header(props: Props) {\n  const insets = useSafeAreaInsets();\n  const frame = useSafeAreaFrame();\n\n  const isParentHeaderShown = React.useContext(HeaderShownContext);\n\n  // On models with Dynamic Island the status bar height is smaller than the safe area top inset.\n  const hasDynamicIsland = Platform.OS === 'ios' && insets.top > 50;\n  const statusBarHeight = hasDynamicIsland ? insets.top - 5 : insets.top;\n\n  const {\n    layout = frame,\n    modal = false,\n    title,\n    headerTitle: customTitle,\n    headerTitleAlign = Platform.select({\n      ios: 'center',\n      default: 'left',\n    }),\n    headerLeft,\n    headerLeftLabelVisible,\n    headerTransparent,\n    headerTintColor,\n    headerBackground,\n    headerRight,\n    headerTitleAllowFontScaling: titleAllowFontScaling,\n    headerTitleStyle: titleStyle,\n    headerLeftContainerStyle: leftContainerStyle,\n    headerRightContainerStyle: rightContainerStyle,\n    headerTitleContainerStyle: titleContainerStyle,\n    headerBackgroundContainerStyle: backgroundContainerStyle,\n    headerStyle: customHeaderStyle,\n    headerShadowVisible,\n    headerPressColor,\n    headerPressOpacity,\n    headerStatusBarHeight = isParentHeaderShown ? 0 : statusBarHeight,\n  } = props;\n\n  const defaultHeight = getDefaultHeaderHeight(\n    layout,\n    modal,\n    headerStatusBarHeight\n  );\n\n  const {\n    height = defaultHeight,\n    minHeight,\n    maxHeight,\n    backgroundColor,\n    borderBottomColor,\n    borderBottomEndRadius,\n    borderBottomLeftRadius,\n    borderBottomRightRadius,\n    borderBottomStartRadius,\n    borderBottomWidth,\n    borderColor,\n    borderEndColor,\n    borderEndWidth,\n    borderLeftColor,\n    borderLeftWidth,\n    borderRadius,\n    borderRightColor,\n    borderRightWidth,\n    borderStartColor,\n    borderStartWidth,\n    borderStyle,\n    borderTopColor,\n    borderTopEndRadius,\n    borderTopLeftRadius,\n    borderTopRightRadius,\n    borderTopStartRadius,\n    borderTopWidth,\n    borderWidth,\n    // @ts-expect-error: web support for shadow\n    boxShadow,\n    elevation,\n    shadowColor,\n    shadowOffset,\n    shadowOpacity,\n    shadowRadius,\n    opacity,\n    transform,\n    ...unsafeStyles\n  } = StyleSheet.flatten(customHeaderStyle || {}) as ViewStyle;\n\n  if (process.env.NODE_ENV !== 'production') {\n    warnIfHeaderStylesDefined(unsafeStyles);\n  }\n\n  const safeStyles: ViewStyle = {\n    backgroundColor,\n    borderBottomColor,\n    borderBottomEndRadius,\n    borderBottomLeftRadius,\n    borderBottomRightRadius,\n    borderBottomStartRadius,\n    borderBottomWidth,\n    borderColor,\n    borderEndColor,\n    borderEndWidth,\n    borderLeftColor,\n    borderLeftWidth,\n    borderRadius,\n    borderRightColor,\n    borderRightWidth,\n    borderStartColor,\n    borderStartWidth,\n    borderStyle,\n    borderTopColor,\n    borderTopEndRadius,\n    borderTopLeftRadius,\n    borderTopRightRadius,\n    borderTopStartRadius,\n    borderTopWidth,\n    borderWidth,\n    // @ts-expect-error: boxShadow is only for Web\n    boxShadow,\n    elevation,\n    shadowColor,\n    shadowOffset,\n    shadowOpacity,\n    shadowRadius,\n    opacity,\n    transform,\n  };\n\n  // Setting a property to undefined triggers default style\n  // So we need to filter them out\n  // Users can use `null` instead\n  for (const styleProp in safeStyles) {\n    // @ts-expect-error: typescript wrongly complains that styleProp cannot be used to index safeStyles\n    if (safeStyles[styleProp] === undefined) {\n      // @ts-expect-error\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete safeStyles[styleProp];\n    }\n  }\n\n  const backgroundStyle = [\n    safeStyles,\n    headerShadowVisible === false && {\n      elevation: 0,\n      shadowOpacity: 0,\n      borderBottomWidth: 0,\n    },\n  ];\n\n  const leftButton = headerLeft\n    ? headerLeft({\n        tintColor: headerTintColor,\n        pressColor: headerPressColor,\n        pressOpacity: headerPressOpacity,\n        labelVisible: headerLeftLabelVisible,\n      })\n    : null;\n\n  const rightButton = headerRight\n    ? headerRight({\n        tintColor: headerTintColor,\n        pressColor: headerPressColor,\n        pressOpacity: headerPressOpacity,\n      })\n    : null;\n\n  const headerTitle =\n    typeof customTitle !== 'function'\n      ? (props: React.ComponentProps<typeof HeaderTitle>) => (\n          <HeaderTitle {...props} />\n        )\n      : customTitle;\n\n  return (\n    <React.Fragment>\n      <Animated.View\n        pointerEvents=\"box-none\"\n        style={[\n          StyleSheet.absoluteFill,\n          { zIndex: 0 },\n          backgroundContainerStyle,\n        ]}\n      >\n        {headerBackground ? (\n          headerBackground({ style: backgroundStyle })\n        ) : headerTransparent ? null : (\n          <HeaderBackground style={backgroundStyle} />\n        )}\n      </Animated.View>\n      <Animated.View\n        pointerEvents=\"box-none\"\n        style={[{ height, minHeight, maxHeight, opacity, transform }]}\n      >\n        <View pointerEvents=\"none\" style={{ height: headerStatusBarHeight }} />\n        <View pointerEvents=\"box-none\" style={styles.content}>\n          <Animated.View\n            pointerEvents=\"box-none\"\n            style={[\n              styles.left,\n              headerTitleAlign === 'center' && styles.expand,\n              { marginStart: insets.left },\n              leftContainerStyle,\n            ]}\n          >\n            {leftButton}\n          </Animated.View>\n          <Animated.View\n            pointerEvents=\"box-none\"\n            style={[\n              styles.title,\n              {\n                // Avoid the title from going offscreen or overlapping buttons\n                maxWidth:\n                  headerTitleAlign === 'center'\n                    ? layout.width -\n                      ((leftButton\n                        ? headerLeftLabelVisible !== false\n                          ? 80\n                          : 32\n                        : 16) +\n                        Math.max(insets.left, insets.right)) *\n                        2\n                    : layout.width -\n                      ((leftButton ? 72 : 16) +\n                        (rightButton ? 72 : 16) +\n                        insets.left -\n                        insets.right),\n              },\n              titleContainerStyle,\n            ]}\n          >\n            {headerTitle({\n              children: title,\n              allowFontScaling: titleAllowFontScaling,\n              tintColor: headerTintColor,\n              style: titleStyle,\n            })}\n          </Animated.View>\n          <Animated.View\n            pointerEvents=\"box-none\"\n            style={[\n              styles.right,\n              styles.expand,\n              { marginEnd: insets.right },\n              rightContainerStyle,\n            ]}\n          >\n            {rightButton}\n          </Animated.View>\n        </View>\n      </Animated.View>\n    </React.Fragment>\n  );\n}\n\nconst styles = StyleSheet.create({\n  content: {\n    flex: 1,\n    flexDirection: 'row',\n    alignItems: 'stretch',\n  },\n  title: {\n    marginHorizontal: 16,\n    justifyContent: 'center',\n  },\n  left: {\n    justifyContent: 'center',\n    alignItems: 'flex-start',\n  },\n  right: {\n    justifyContent: 'center',\n    alignItems: 'flex-end',\n  },\n  expand: {\n    flexGrow: 1,\n    flexBasis: 0,\n  },\n});\n"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAE9B,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAGvC,OAAOC,sBAAsB;AAC7B,OAAOC,gBAAgB;AACvB,OAAOC,kBAAkB;AACzB,OAAOC,WAAW;AAiBlB,IAAMC,yBAAyB,GAAI,SAA7BA,yBAAyBA,CAAIC,MAA2B,EAAK;EACjEC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAE,UAAAC,SAAS,EAAK;IACzC,IAAMC,KAAK,GAAGL,MAAM,CAACI,SAAS,CAAC;IAE/B,IAAIA,SAAS,KAAK,UAAU,IAAIC,KAAK,KAAK,UAAU,EAAE;MACpDC,OAAO,CAACC,IAAI,CACV,iJAAiJ,CAClJ;IACH,CAAC,MAAM,IAAIF,KAAK,KAAKG,SAAS,EAAE;MAC9BF,OAAO,CAACC,IAAI,CACT,GAAEH,SAAU,yBAAwBC,KAAM,sCAAqC,CACjF;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAe,SAASI,MAAMA,CAACC,KAAY,EAAE;EAC3C,IAAMC,MAAM,GAAGjB,iBAAiB,EAAE;EAClC,IAAMkB,KAAK,GAAGnB,gBAAgB,EAAE;EAEhC,IAAMoB,mBAAmB,GAAGzB,KAAK,CAAC0B,UAAU,CAACjB,kBAAkB,CAAC;EAGhE,IAAMkB,gBAAgB,GAAGzB,QAAQ,CAAC0B,EAAE,KAAK,KAAK,IAAIL,MAAM,CAACM,GAAG,GAAG,EAAE;EACjE,IAAMC,eAAe,GAAGH,gBAAgB,GAAGJ,MAAM,CAACM,GAAG,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG;EAEtE,IAAAE,aAAA,GA0BIT,KAAK,CAzBPU,MAAM;IAANA,MAAM,GAAAD,aAAA,cAAGP,KAAK,GAAAO,aAAA;IAAAE,YAAA,GAyBZX,KAAK,CAxBPY,KAAK;IAALA,KAAK,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IACbE,KAAK,GAuBHb,KAAK,CAvBPa,KAAK;IACQC,WAAW,GAsBtBd,KAAK,CAtBPe,WAAW;IAAAC,qBAAA,GAsBThB,KAAK,CArBPiB,gBAAgB;IAAhBA,gBAAgB,GAAAD,qBAAA,cAAGpC,QAAQ,CAACsC,MAAM,CAAC;MACjCC,GAAG,EAAE,QAAQ;MACbC,OAAO,EAAE;IACX,CAAC,CAAC,GAAAJ,qBAAA;IACFK,UAAU,GAiBRrB,KAAK,CAjBPqB,UAAU;IACVC,sBAAsB,GAgBpBtB,KAAK,CAhBPsB,sBAAsB;IACtBC,iBAAiB,GAefvB,KAAK,CAfPuB,iBAAiB;IACjBC,eAAe,GAcbxB,KAAK,CAdPwB,eAAe;IACfC,gBAAgB,GAadzB,KAAK,CAbPyB,gBAAgB;IAChBC,WAAW,GAYT1B,KAAK,CAZP0B,WAAW;IACkBC,qBAAqB,GAWhD3B,KAAK,CAXP4B,2BAA2B;IACTC,UAAU,GAU1B7B,KAAK,CAVP8B,gBAAgB;IACUC,kBAAkB,GAS1C/B,KAAK,CATPgC,wBAAwB;IACGC,mBAAmB,GAQ5CjC,KAAK,CARPkC,yBAAyB;IACEC,mBAAmB,GAO5CnC,KAAK,CAPPoC,yBAAyB;IACOC,wBAAwB,GAMtDrC,KAAK,CANPsC,8BAA8B;IACjBC,iBAAiB,GAK5BvC,KAAK,CALPwC,WAAW;IACXC,mBAAmB,GAIjBzC,KAAK,CAJPyC,mBAAmB;IACnBC,gBAAgB,GAGd1C,KAAK,CAHP0C,gBAAgB;IAChBC,kBAAkB,GAEhB3C,KAAK,CAFP2C,kBAAkB;IAAAC,qBAAA,GAEhB5C,KAAK,CADP6C,qBAAqB;IAArBA,qBAAqB,GAAAD,qBAAA,cAAGzC,mBAAmB,GAAG,CAAC,GAAGK,eAAA,GAAAoC,qBAAA;EAGpD,IAAME,aAAa,GAAG7D,sBAAsB,CAC1CyB,MAAM,EACNE,KAAK,EACLiC,qBAAqB,CACtB;EAED,IAAAE,mBAAA,GAuCIlE,UAAU,CAACmE,OAAO,CAACT,iBAAiB,IAAI,CAAC,CAAC,CAAc;IAAAU,qBAAA,GAAAF,mBAAA,CAtC1DG,MAAM;IAANA,MAAM,GAAAD,qBAAA,cAAGH,aAAa,GAAAG,qBAAA;IACtBE,SAAS,GAAAJ,mBAAA,CAATI,SAAS;IACTC,SAAS,GAAAL,mBAAA,CAATK,SAAS;IACTC,eAAe,GAAAN,mBAAA,CAAfM,eAAe;IACfC,iBAAiB,GAAAP,mBAAA,CAAjBO,iBAAiB;IACjBC,qBAAqB,GAAAR,mBAAA,CAArBQ,qBAAqB;IACrBC,sBAAsB,GAAAT,mBAAA,CAAtBS,sBAAsB;IACtBC,uBAAuB,GAAAV,mBAAA,CAAvBU,uBAAuB;IACvBC,uBAAuB,GAAAX,mBAAA,CAAvBW,uBAAuB;IACvBC,iBAAiB,GAAAZ,mBAAA,CAAjBY,iBAAiB;IACjBC,WAAW,GAAAb,mBAAA,CAAXa,WAAW;IACXC,cAAc,GAAAd,mBAAA,CAAdc,cAAc;IACdC,cAAc,GAAAf,mBAAA,CAAde,cAAc;IACdC,eAAe,GAAAhB,mBAAA,CAAfgB,eAAe;IACfC,eAAe,GAAAjB,mBAAA,CAAfiB,eAAe;IACfC,YAAY,GAAAlB,mBAAA,CAAZkB,YAAY;IACZC,gBAAgB,GAAAnB,mBAAA,CAAhBmB,gBAAgB;IAChBC,gBAAgB,GAAApB,mBAAA,CAAhBoB,gBAAgB;IAChBC,gBAAgB,GAAArB,mBAAA,CAAhBqB,gBAAgB;IAChBC,gBAAgB,GAAAtB,mBAAA,CAAhBsB,gBAAgB;IAChBC,WAAW,GAAAvB,mBAAA,CAAXuB,WAAW;IACXC,cAAc,GAAAxB,mBAAA,CAAdwB,cAAc;IACdC,kBAAkB,GAAAzB,mBAAA,CAAlByB,kBAAkB;IAClBC,mBAAmB,GAAA1B,mBAAA,CAAnB0B,mBAAmB;IACnBC,oBAAoB,GAAA3B,mBAAA,CAApB2B,oBAAoB;IACpBC,oBAAoB,GAAA5B,mBAAA,CAApB4B,oBAAoB;IACpBC,cAAc,GAAA7B,mBAAA,CAAd6B,cAAc;IACdC,WAAW,GAAA9B,mBAAA,CAAX8B,WAAW;IAEXC,SAAS,GAAA/B,mBAAA,CAAT+B,SAAS;IACTC,SAAS,GAAAhC,mBAAA,CAATgC,SAAS;IACTC,WAAW,GAAAjC,mBAAA,CAAXiC,WAAW;IACXC,YAAY,GAAAlC,mBAAA,CAAZkC,YAAY;IACZC,aAAa,GAAAnC,mBAAA,CAAbmC,aAAa;IACbC,YAAY,GAAApC,mBAAA,CAAZoC,YAAY;IACZC,OAAO,GAAArC,mBAAA,CAAPqC,OAAO;IACPC,SAAS,GAAAtC,mBAAA,CAATsC,SAAS;IACNC,YAAA,GAAAC,wBAAA,CAAAxC,mBAAA,EAAAyC,SAAA;EAGL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCtG,yBAAyB,CAACiG,YAAY,CAAC;EACzC;EAEA,IAAMM,UAAqB,GAAG;IAC5BvC,eAAe,EAAfA,eAAe;IACfC,iBAAiB,EAAjBA,iBAAiB;IACjBC,qBAAqB,EAArBA,qBAAqB;IACrBC,sBAAsB,EAAtBA,sBAAsB;IACtBC,uBAAuB,EAAvBA,uBAAuB;IACvBC,uBAAuB,EAAvBA,uBAAuB;IACvBC,iBAAiB,EAAjBA,iBAAiB;IACjBC,WAAW,EAAXA,WAAW;IACXC,cAAc,EAAdA,cAAc;IACdC,cAAc,EAAdA,cAAc;IACdC,eAAe,EAAfA,eAAe;IACfC,eAAe,EAAfA,eAAe;IACfC,YAAY,EAAZA,YAAY;IACZC,gBAAgB,EAAhBA,gBAAgB;IAChBC,gBAAgB,EAAhBA,gBAAgB;IAChBC,gBAAgB,EAAhBA,gBAAgB;IAChBC,gBAAgB,EAAhBA,gBAAgB;IAChBC,WAAW,EAAXA,WAAW;IACXC,cAAc,EAAdA,cAAc;IACdC,kBAAkB,EAAlBA,kBAAkB;IAClBC,mBAAmB,EAAnBA,mBAAmB;IACnBC,oBAAoB,EAApBA,oBAAoB;IACpBC,oBAAoB,EAApBA,oBAAoB;IACpBC,cAAc,EAAdA,cAAc;IACdC,WAAW,EAAXA,WAAW;IAEXC,SAAS,EAATA,SAAS;IACTC,SAAS,EAATA,SAAS;IACTC,WAAW,EAAXA,WAAW;IACXC,YAAY,EAAZA,YAAY;IACZC,aAAa,EAAbA,aAAa;IACbC,YAAY,EAAZA,YAAY;IACZC,OAAO,EAAPA,OAAO;IACPC,SAAA,EAAAA;EACF,CAAC;EAKD,KAAK,IAAM3F,SAAS,IAAIkG,UAAU,EAAE;IAElC,IAAIA,UAAU,CAAClG,SAAS,CAAC,KAAKI,SAAS,EAAE;MAGvC,OAAO8F,UAAU,CAAClG,SAAS,CAAC;IAC9B;EACF;EAEA,IAAMmG,eAAe,GAAG,CACtBD,UAAU,EACVnD,mBAAmB,KAAK,KAAK,IAAI;IAC/BsC,SAAS,EAAE,CAAC;IACZG,aAAa,EAAE,CAAC;IAChBvB,iBAAiB,EAAE;EACrB,CAAC,CACF;EAED,IAAMmC,UAAU,GAAGzE,UAAU,GACzBA,UAAU,CAAC;IACT0E,SAAS,EAAEvE,eAAe;IAC1BwE,UAAU,EAAEtD,gBAAgB;IAC5BuD,YAAY,EAAEtD,kBAAkB;IAChCuD,YAAY,EAAE5E;EAChB,CAAC,CAAC,GACF,IAAI;EAER,IAAM6E,WAAW,GAAGzE,WAAW,GAC3BA,WAAW,CAAC;IACVqE,SAAS,EAAEvE,eAAe;IAC1BwE,UAAU,EAAEtD,gBAAgB;IAC5BuD,YAAY,EAAEtD;EAChB,CAAC,CAAC,GACF,IAAI;EAER,IAAM5B,WAAW,GACf,OAAOD,WAAW,KAAK,UAAU,GAC5B,UAAAd,KAA+C;IAAA,OAC9CtB,KAAA,CAAA0H,aAAA,CAAChH,WAAW,EAAKY,KAAK,CACvB;EAAA,IACDc,WAAW;EAEjB,OACEpC,KAAA,CAAA0H,aAAA,CAAC1H,KAAK,CAAC2H,QAAQ,QACb3H,KAAA,CAAA0H,aAAA,CAACzH,QAAQ,CAACG,IAAI;IACZwH,aAAa,EAAC,UAAU;IACxBC,KAAK,EAAE,CACL1H,UAAU,CAAC2H,YAAY,EACvB;MAAEC,MAAM,EAAE;IAAE,CAAC,EACbpE,wBAAwB;EACxB,GAEDZ,gBAAgB,GACfA,gBAAgB,CAAC;IAAE8E,KAAK,EAAEV;EAAgB,CAAC,CAAC,GAC1CtE,iBAAiB,GAAG,IAAI,GAC1B7C,KAAA,CAAA0H,aAAA,CAAClH,gBAAgB;IAACqH,KAAK,EAAEV;EAAgB,EAC1C,CACa,EAChBnH,KAAA,CAAA0H,aAAA,CAACzH,QAAQ,CAACG,IAAI;IACZwH,aAAa,EAAC,UAAU;IACxBC,KAAK,EAAE,CAAC;MAAErD,MAAM,EAANA,MAAM;MAAEC,SAAS,EAATA,SAAS;MAAEC,SAAS,EAATA,SAAS;MAAEgC,OAAO,EAAPA,OAAO;MAAEC,SAAA,EAAAA;IAAU,CAAC;EAAE,GAE9D3G,KAAA,CAAA0H,aAAA,CAACtH,IAAI;IAACwH,aAAa,EAAC,MAAM;IAACC,KAAK,EAAE;MAAErD,MAAM,EAAEL;IAAsB;EAAE,EAAG,EACvEnE,KAAA,CAAA0H,aAAA,CAACtH,IAAI;IAACwH,aAAa,EAAC,UAAU;IAACC,KAAK,EAAEjH,MAAM,CAACoH;EAAQ,GACnDhI,KAAA,CAAA0H,aAAA,CAACzH,QAAQ,CAACG,IAAI;IACZwH,aAAa,EAAC,UAAU;IACxBC,KAAK,EAAE,CACLjH,MAAM,CAACqH,IAAI,EACX1F,gBAAgB,KAAK,QAAQ,IAAI3B,MAAM,CAACsH,MAAM,EAC9C;MAAEC,WAAW,EAAE5G,MAAM,CAAC0G;IAAK,CAAC,EAC5B5E,kBAAkB;EAClB,GAED+D,UAAU,CACG,EAChBpH,KAAA,CAAA0H,aAAA,CAACzH,QAAQ,CAACG,IAAI;IACZwH,aAAa,EAAC,UAAU;IACxBC,KAAK,EAAE,CACLjH,MAAM,CAACuB,KAAK,EACZ;MAEEiG,QAAQ,EACN7F,gBAAgB,KAAK,QAAQ,GACzBP,MAAM,CAACqG,KAAK,GACZ,CAAC,CAACjB,UAAU,GACRxE,sBAAsB,KAAK,KAAK,GAC9B,EAAE,GACF,EAAE,GACJ,EAAE,IACJ0F,IAAI,CAACC,GAAG,CAAChH,MAAM,CAAC0G,IAAI,EAAE1G,MAAM,CAACiH,KAAK,CAAC,IACnC,CAAC,GACHxG,MAAM,CAACqG,KAAK,IACX,CAACjB,UAAU,GAAG,EAAE,GAAG,EAAE,KACnBK,WAAW,GAAG,EAAE,GAAG,EAAE,CAAC,GACvBlG,MAAM,CAAC0G,IAAI,GACX1G,MAAM,CAACiH,KAAK;IACtB,CAAC,EACD/E,mBAAmB;EACnB,GAEDpB,WAAW,CAAC;IACXoG,QAAQ,EAAEtG,KAAK;IACfuG,gBAAgB,EAAEzF,qBAAqB;IACvCoE,SAAS,EAAEvE,eAAe;IAC1B+E,KAAK,EAAE1E;EACT,CAAC,CAAC,CACY,EAChBnD,KAAA,CAAA0H,aAAA,CAACzH,QAAQ,CAACG,IAAI;IACZwH,aAAa,EAAC,UAAU;IACxBC,KAAK,EAAE,CACLjH,MAAM,CAAC4H,KAAK,EACZ5H,MAAM,CAACsH,MAAM,EACb;MAAES,SAAS,EAAEpH,MAAM,CAACiH;IAAM,CAAC,EAC3BjF,mBAAmB;EACnB,GAEDkE,WAAW,CACE,CACX,CACO,CACD;AAErB;AAEA,IAAM7G,MAAM,GAAGT,UAAU,CAACyI,MAAM,CAAC;EAC/BZ,OAAO,EAAE;IACPa,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd,CAAC;EACD5G,KAAK,EAAE;IACL6G,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDhB,IAAI,EAAE;IACJgB,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDP,KAAK,EAAE;IACLS,cAAc,EAAE,QAAQ;IACxBF,UAAU,EAAE;EACd,CAAC;EACDb,MAAM,EAAE;IACNgB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}