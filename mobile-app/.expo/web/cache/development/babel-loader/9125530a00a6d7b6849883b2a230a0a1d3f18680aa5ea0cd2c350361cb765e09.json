{"ast": null, "code": "import getNamedContext from \"../getNamedContext\";\nvar HeaderHeightContext = getNamedContext('HeaderHeightContext', undefined);\nexport default HeaderHeightContext;", "map": {"version": 3, "names": ["getNamedContext", "HeaderHeightContext", "undefined"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/elements/src/Header/HeaderHeightContext.tsx"], "sourcesContent": ["import getNamedContext from '../getNamedContext';\n\nconst HeaderHeightContext = getNamedContext<number | undefined>(\n  'HeaderHeightContext',\n  undefined\n);\n\nexport default HeaderHeightContext;\n"], "mappings": "AAAA,OAAOA,eAAe;AAEtB,IAAMC,mBAAmB,GAAGD,eAAe,CACzC,qBAAqB,EACrBE,SAAS,CACV;AAED,eAAeD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}