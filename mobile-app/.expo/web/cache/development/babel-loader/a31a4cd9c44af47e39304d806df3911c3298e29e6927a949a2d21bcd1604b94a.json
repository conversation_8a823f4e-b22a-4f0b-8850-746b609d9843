{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { nanoid } from 'nanoid/non-secure';\nimport BaseRouter from \"./BaseRouter\";\nvar TYPE_ROUTE = 'route';\nexport var TabActions = {\n  jumpTo: function jumpTo(name, params) {\n    return {\n      type: 'JUMP_TO',\n      payload: {\n        name: name,\n        params: params\n      }\n    };\n  }\n};\nvar getRouteHistory = function getRouteHistory(routes, index, backBehavior, initialRouteName) {\n  var history = [{\n    type: TYPE_ROUTE,\n    key: routes[index].key\n  }];\n  var initialRouteIndex;\n  switch (backBehavior) {\n    case 'order':\n      for (var i = index; i > 0; i--) {\n        history.unshift({\n          type: TYPE_ROUTE,\n          key: routes[i - 1].key\n        });\n      }\n      break;\n    case 'firstRoute':\n      if (index !== 0) {\n        history.unshift({\n          type: TYPE_ROUTE,\n          key: routes[0].key\n        });\n      }\n      break;\n    case 'initialRoute':\n      initialRouteIndex = routes.findIndex(function (route) {\n        return route.name === initialRouteName;\n      });\n      initialRouteIndex = initialRouteIndex === -1 ? 0 : initialRouteIndex;\n      if (index !== initialRouteIndex) {\n        history.unshift({\n          type: TYPE_ROUTE,\n          key: routes[initialRouteIndex].key\n        });\n      }\n      break;\n    case 'history':\n      break;\n  }\n  return history;\n};\nvar changeIndex = function changeIndex(state, index, backBehavior, initialRouteName) {\n  var history;\n  if (backBehavior === 'history') {\n    var currentKey = state.routes[index].key;\n    history = state.history.filter(function (it) {\n      return it.type === 'route' ? it.key !== currentKey : false;\n    }).concat({\n      type: TYPE_ROUTE,\n      key: currentKey\n    });\n  } else {\n    history = getRouteHistory(state.routes, index, backBehavior, initialRouteName);\n  }\n  return _objectSpread(_objectSpread({}, state), {}, {\n    index: index,\n    history: history\n  });\n};\nexport default function TabRouter(_ref) {\n  var initialRouteName = _ref.initialRouteName,\n    _ref$backBehavior = _ref.backBehavior,\n    backBehavior = _ref$backBehavior === void 0 ? 'firstRoute' : _ref$backBehavior;\n  var router = _objectSpread(_objectSpread({}, BaseRouter), {}, {\n    type: 'tab',\n    getInitialState: function getInitialState(_ref2) {\n      var routeNames = _ref2.routeNames,\n        routeParamList = _ref2.routeParamList;\n      var index = initialRouteName !== undefined && routeNames.includes(initialRouteName) ? routeNames.indexOf(initialRouteName) : 0;\n      var routes = routeNames.map(function (name) {\n        return {\n          name: name,\n          key: `${name}-${nanoid()}`,\n          params: routeParamList[name]\n        };\n      });\n      var history = getRouteHistory(routes, index, backBehavior, initialRouteName);\n      return {\n        stale: false,\n        type: 'tab',\n        key: `tab-${nanoid()}`,\n        index: index,\n        routeNames: routeNames,\n        history: history,\n        routes: routes\n      };\n    },\n    getRehydratedState: function getRehydratedState(partialState, _ref3) {\n      var _ref6, _ref7;\n      var _state$routes, _state$history;\n      var routeNames = _ref3.routeNames,\n        routeParamList = _ref3.routeParamList;\n      var state = partialState;\n      if (state.stale === false) {\n        return state;\n      }\n      var routes = routeNames.map(function (name) {\n        var route = state.routes.find(function (r) {\n          return r.name === name;\n        });\n        return _objectSpread(_objectSpread({}, route), {}, {\n          name: name,\n          key: route && route.name === name && route.key ? route.key : `${name}-${nanoid()}`,\n          params: routeParamList[name] !== undefined ? _objectSpread(_objectSpread({}, routeParamList[name]), route ? route.params : undefined) : route ? route.params : undefined\n        });\n      });\n      var index = Math.min(Math.max(routeNames.indexOf((_state$routes = state.routes[(_ref6 = state === null || state === void 0 ? void 0 : state.index) != null ? _ref6 : 0]) === null || _state$routes === void 0 ? void 0 : _state$routes.name), 0), routes.length - 1);\n      var history = (_ref7 = (_state$history = state.history) === null || _state$history === void 0 ? void 0 : _state$history.filter(function (it) {\n        return routes.find(function (r) {\n          return r.key === it.key;\n        });\n      })) != null ? _ref7 : [];\n      return changeIndex({\n        stale: false,\n        type: 'tab',\n        key: `tab-${nanoid()}`,\n        index: index,\n        routeNames: routeNames,\n        history: history,\n        routes: routes\n      }, index, backBehavior, initialRouteName);\n    },\n    getStateForRouteNamesChange: function getStateForRouteNamesChange(state, _ref4) {\n      var routeNames = _ref4.routeNames,\n        routeParamList = _ref4.routeParamList,\n        routeKeyChanges = _ref4.routeKeyChanges;\n      var routes = routeNames.map(function (name) {\n        return state.routes.find(function (r) {\n          return r.name === name && !routeKeyChanges.includes(r.name);\n        }) || {\n          name: name,\n          key: `${name}-${nanoid()}`,\n          params: routeParamList[name]\n        };\n      });\n      var index = Math.max(0, routeNames.indexOf(state.routes[state.index].name));\n      var history = state.history.filter(function (it) {\n        return it.type !== 'route' || routes.find(function (r) {\n          return r.key === it.key;\n        });\n      });\n      if (!history.length) {\n        history = getRouteHistory(routes, index, backBehavior, initialRouteName);\n      }\n      return _objectSpread(_objectSpread({}, state), {}, {\n        history: history,\n        routeNames: routeNames,\n        routes: routes,\n        index: index\n      });\n    },\n    getStateForRouteFocus: function getStateForRouteFocus(state, key) {\n      var index = state.routes.findIndex(function (r) {\n        return r.key === key;\n      });\n      if (index === -1 || index === state.index) {\n        return state;\n      }\n      return changeIndex(state, index, backBehavior, initialRouteName);\n    },\n    getStateForAction: function getStateForAction(state, action, _ref5) {\n      var routeParamList = _ref5.routeParamList,\n        routeGetIdList = _ref5.routeGetIdList;\n      switch (action.type) {\n        case 'JUMP_TO':\n        case 'NAVIGATE':\n          {\n            var index = -1;\n            if (action.type === 'NAVIGATE' && action.payload.key) {\n              index = state.routes.findIndex(function (route) {\n                return route.key === action.payload.key;\n              });\n            } else {\n              index = state.routes.findIndex(function (route) {\n                return route.name === action.payload.name;\n              });\n            }\n            if (index === -1) {\n              return null;\n            }\n            return changeIndex(_objectSpread(_objectSpread({}, state), {}, {\n              routes: state.routes.map(function (route, i) {\n                if (i !== index) {\n                  return route;\n                }\n                var getId = routeGetIdList[route.name];\n                var currentId = getId === null || getId === void 0 ? void 0 : getId({\n                  params: route.params\n                });\n                var nextId = getId === null || getId === void 0 ? void 0 : getId({\n                  params: action.payload.params\n                });\n                var key = currentId === nextId ? route.key : `${route.name}-${nanoid()}`;\n                var params;\n                if (action.type === 'NAVIGATE' && action.payload.merge && currentId === nextId) {\n                  params = action.payload.params !== undefined || routeParamList[route.name] !== undefined ? _objectSpread(_objectSpread(_objectSpread({}, routeParamList[route.name]), route.params), action.payload.params) : route.params;\n                } else {\n                  params = routeParamList[route.name] !== undefined ? _objectSpread(_objectSpread({}, routeParamList[route.name]), action.payload.params) : action.payload.params;\n                }\n                var path = action.type === 'NAVIGATE' && action.payload.path != null ? action.payload.path : route.path;\n                return params !== route.params || path !== route.path ? _objectSpread(_objectSpread({}, route), {}, {\n                  key: key,\n                  path: path,\n                  params: params\n                }) : route;\n              })\n            }), index, backBehavior, initialRouteName);\n          }\n        case 'GO_BACK':\n          {\n            if (state.history.length === 1) {\n              return null;\n            }\n            var previousKey = state.history[state.history.length - 2].key;\n            var _index = state.routes.findIndex(function (route) {\n              return route.key === previousKey;\n            });\n            if (_index === -1) {\n              return null;\n            }\n            return _objectSpread(_objectSpread({}, state), {}, {\n              history: state.history.slice(0, -1),\n              index: _index\n            });\n          }\n        default:\n          return BaseRouter.getStateForAction(state, action);\n      }\n    },\n    shouldActionChangeFocus: function shouldActionChangeFocus(action) {\n      return action.type === 'NAVIGATE';\n    },\n    actionCreators: TabActions\n  });\n  return router;\n}", "map": {"version": 3, "names": ["nanoid", "BaseRouter", "TYPE_ROUTE", "TabActions", "jumpTo", "name", "params", "type", "payload", "getRouteHistory", "routes", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initialRouteName", "history", "key", "initialRouteIndex", "i", "unshift", "findIndex", "route", "changeIndex", "state", "current<PERSON><PERSON>", "filter", "it", "concat", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_ref$backBehavior", "router", "getInitialState", "_ref2", "routeNames", "routeParamList", "undefined", "includes", "indexOf", "map", "stale", "getRehydratedState", "partialState", "_ref3", "_ref6", "_ref7", "_state$routes", "_state$history", "find", "r", "Math", "min", "max", "length", "getStateForRouteNamesChange", "_ref4", "routeKeyChanges", "getStateForRouteFocus", "getStateForAction", "action", "_ref5", "routeGetIdList", "getId", "currentId", "nextId", "merge", "path", "previousKey", "slice", "shouldActionChangeFocus", "actionCreators"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/routers/src/TabRouter.tsx"], "sourcesContent": ["import { nanoid } from 'nanoid/non-secure';\n\nimport BaseRouter from './BaseRouter';\nimport type {\n  CommonNavigationAction,\n  DefaultRouterOptions,\n  NavigationState,\n  ParamListBase,\n  PartialState,\n  Route,\n  Router,\n} from './types';\n\nexport type TabActionType = {\n  type: 'JUMP_TO';\n  payload: { name: string; params?: object };\n  source?: string;\n  target?: string;\n};\n\nexport type BackBehavior =\n  | 'initialRoute'\n  | 'firstRoute'\n  | 'history'\n  | 'order'\n  | 'none';\n\nexport type TabRouterOptions = DefaultRouterOptions & {\n  backBehavior?: BackBehavior;\n};\n\nexport type TabNavigationState<ParamList extends ParamListBase> = Omit<\n  NavigationState<ParamList>,\n  'history'\n> & {\n  /**\n   * Type of the router, in this case, it's tab.\n   */\n  type: 'tab';\n  /**\n   * List of previously visited route keys.\n   */\n  history: { type: 'route'; key: string }[];\n};\n\nexport type TabActionHelpers<ParamList extends ParamListBase> = {\n  /**\n   * Jump to an existing tab.\n   *\n   * @param name Name of the route for the tab.\n   * @param [params] Params object for the route.\n   */\n  jumpTo<RouteName extends Extract<keyof ParamList, string>>(\n    ...args: undefined extends ParamList[RouteName]\n      ? [screen: RouteName] | [screen: RouteName, params: ParamList[RouteName]]\n      : [screen: RouteName, params: ParamList[RouteName]]\n  ): void;\n};\n\nconst TYPE_ROUTE = 'route' as const;\n\nexport const TabActions = {\n  jumpTo(name: string, params?: object): TabActionType {\n    return { type: 'JUMP_TO', payload: { name, params } };\n  },\n};\n\nconst getRouteHistory = (\n  routes: Route<string>[],\n  index: number,\n  backBehavior: BackBehavior,\n  initialRouteName: string | undefined\n) => {\n  const history = [{ type: TYPE_ROUTE, key: routes[index].key }];\n  let initialRouteIndex;\n\n  switch (backBehavior) {\n    case 'order':\n      for (let i = index; i > 0; i--) {\n        history.unshift({ type: TYPE_ROUTE, key: routes[i - 1].key });\n      }\n      break;\n    case 'firstRoute':\n      if (index !== 0) {\n        history.unshift({\n          type: TYPE_ROUTE,\n          key: routes[0].key,\n        });\n      }\n      break;\n    case 'initialRoute':\n      initialRouteIndex = routes.findIndex(\n        (route) => route.name === initialRouteName\n      );\n      initialRouteIndex = initialRouteIndex === -1 ? 0 : initialRouteIndex;\n\n      if (index !== initialRouteIndex) {\n        history.unshift({\n          type: TYPE_ROUTE,\n          key: routes[initialRouteIndex].key,\n        });\n      }\n      break;\n    case 'history':\n      // The history will fill up on navigation\n      break;\n  }\n\n  return history;\n};\n\nconst changeIndex = (\n  state: TabNavigationState<ParamListBase>,\n  index: number,\n  backBehavior: BackBehavior,\n  initialRouteName: string | undefined\n) => {\n  let history;\n\n  if (backBehavior === 'history') {\n    const currentKey = state.routes[index].key;\n\n    history = state.history\n      .filter((it) => (it.type === 'route' ? it.key !== currentKey : false))\n      .concat({ type: TYPE_ROUTE, key: currentKey });\n  } else {\n    history = getRouteHistory(\n      state.routes,\n      index,\n      backBehavior,\n      initialRouteName\n    );\n  }\n\n  return {\n    ...state,\n    index,\n    history,\n  };\n};\n\nexport default function TabRouter({\n  initialRouteName,\n  backBehavior = 'firstRoute',\n}: TabRouterOptions) {\n  const router: Router<\n    TabNavigationState<ParamListBase>,\n    TabActionType | CommonNavigationAction\n  > = {\n    ...BaseRouter,\n\n    type: 'tab',\n\n    getInitialState({ routeNames, routeParamList }) {\n      const index =\n        initialRouteName !== undefined && routeNames.includes(initialRouteName)\n          ? routeNames.indexOf(initialRouteName)\n          : 0;\n\n      const routes = routeNames.map((name) => ({\n        name,\n        key: `${name}-${nanoid()}`,\n        params: routeParamList[name],\n      }));\n\n      const history = getRouteHistory(\n        routes,\n        index,\n        backBehavior,\n        initialRouteName\n      );\n\n      return {\n        stale: false,\n        type: 'tab',\n        key: `tab-${nanoid()}`,\n        index,\n        routeNames,\n        history,\n        routes,\n      };\n    },\n\n    getRehydratedState(partialState, { routeNames, routeParamList }) {\n      let state = partialState;\n\n      if (state.stale === false) {\n        return state;\n      }\n\n      const routes = routeNames.map((name) => {\n        const route = (\n          state as PartialState<TabNavigationState<ParamListBase>>\n        ).routes.find((r) => r.name === name);\n\n        return {\n          ...route,\n          name,\n          key:\n            route && route.name === name && route.key\n              ? route.key\n              : `${name}-${nanoid()}`,\n          params:\n            routeParamList[name] !== undefined\n              ? {\n                  ...routeParamList[name],\n                  ...(route ? route.params : undefined),\n                }\n              : route\n              ? route.params\n              : undefined,\n        } as Route<string>;\n      });\n\n      const index = Math.min(\n        Math.max(routeNames.indexOf(state.routes[state?.index ?? 0]?.name), 0),\n        routes.length - 1\n      );\n\n      const history =\n        state.history?.filter((it) => routes.find((r) => r.key === it.key)) ??\n        [];\n\n      return changeIndex(\n        {\n          stale: false,\n          type: 'tab',\n          key: `tab-${nanoid()}`,\n          index,\n          routeNames,\n          history,\n          routes,\n        },\n        index,\n        backBehavior,\n        initialRouteName\n      );\n    },\n\n    getStateForRouteNamesChange(\n      state,\n      { routeNames, routeParamList, routeKeyChanges }\n    ) {\n      const routes = routeNames.map(\n        (name) =>\n          state.routes.find(\n            (r) => r.name === name && !routeKeyChanges.includes(r.name)\n          ) || {\n            name,\n            key: `${name}-${nanoid()}`,\n            params: routeParamList[name],\n          }\n      );\n\n      const index = Math.max(\n        0,\n        routeNames.indexOf(state.routes[state.index].name)\n      );\n\n      let history = state.history.filter(\n        // Type will always be 'route' for tabs, but could be different in a router extending this (e.g. drawer)\n        (it) => it.type !== 'route' || routes.find((r) => r.key === it.key)\n      );\n\n      if (!history.length) {\n        history = getRouteHistory(\n          routes,\n          index,\n          backBehavior,\n          initialRouteName\n        );\n      }\n\n      return {\n        ...state,\n        history,\n        routeNames,\n        routes,\n        index,\n      };\n    },\n\n    getStateForRouteFocus(state, key) {\n      const index = state.routes.findIndex((r) => r.key === key);\n\n      if (index === -1 || index === state.index) {\n        return state;\n      }\n\n      return changeIndex(state, index, backBehavior, initialRouteName);\n    },\n\n    getStateForAction(state, action, { routeParamList, routeGetIdList }) {\n      switch (action.type) {\n        case 'JUMP_TO':\n        case 'NAVIGATE': {\n          let index = -1;\n\n          if (action.type === 'NAVIGATE' && action.payload.key) {\n            index = state.routes.findIndex(\n              (route) => route.key === action.payload.key\n            );\n          } else {\n            index = state.routes.findIndex(\n              (route) => route.name === action.payload.name\n            );\n          }\n\n          if (index === -1) {\n            return null;\n          }\n\n          return changeIndex(\n            {\n              ...state,\n              routes: state.routes.map((route, i) => {\n                if (i !== index) {\n                  return route;\n                }\n\n                const getId = routeGetIdList[route.name];\n\n                const currentId = getId?.({ params: route.params });\n                const nextId = getId?.({ params: action.payload.params });\n\n                const key =\n                  currentId === nextId\n                    ? route.key\n                    : `${route.name}-${nanoid()}`;\n\n                let params;\n\n                if (\n                  action.type === 'NAVIGATE' &&\n                  action.payload.merge &&\n                  currentId === nextId\n                ) {\n                  params =\n                    action.payload.params !== undefined ||\n                    routeParamList[route.name] !== undefined\n                      ? {\n                          ...routeParamList[route.name],\n                          ...route.params,\n                          ...action.payload.params,\n                        }\n                      : route.params;\n                } else {\n                  params =\n                    routeParamList[route.name] !== undefined\n                      ? {\n                          ...routeParamList[route.name],\n                          ...action.payload.params,\n                        }\n                      : action.payload.params;\n                }\n\n                const path =\n                  action.type === 'NAVIGATE' && action.payload.path != null\n                    ? action.payload.path\n                    : route.path;\n\n                return params !== route.params || path !== route.path\n                  ? { ...route, key, path, params }\n                  : route;\n              }),\n            },\n            index,\n            backBehavior,\n            initialRouteName\n          );\n        }\n\n        case 'GO_BACK': {\n          if (state.history.length === 1) {\n            return null;\n          }\n\n          const previousKey = state.history[state.history.length - 2].key;\n          const index = state.routes.findIndex(\n            (route) => route.key === previousKey\n          );\n\n          if (index === -1) {\n            return null;\n          }\n\n          return {\n            ...state,\n            history: state.history.slice(0, -1),\n            index,\n          };\n        }\n\n        default:\n          return BaseRouter.getStateForAction(state, action);\n      }\n    },\n\n    shouldActionChangeFocus(action) {\n      return action.type === 'NAVIGATE';\n    },\n\n    actionCreators: TabActions,\n  };\n\n  return router;\n}\n"], "mappings": ";;;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,OAAOC,UAAU;AAyDjB,IAAMC,UAAU,GAAG,OAAgB;AAEnC,OAAO,IAAMC,UAAU,GAAG;EACxBC,MAAM,WAANA,MAAMA,CAACC,IAAY,EAAEC,MAAe,EAAiB;IACnD,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;QAAEH,IAAI,EAAJA,IAAI;QAAEC,MAAA,EAAAA;MAAO;IAAE,CAAC;EACvD;AACF,CAAC;AAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CACnBC,MAAuB,EACvBC,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,EACjC;EACH,IAAMC,OAAO,GAAG,CAAC;IAAEP,IAAI,EAAEL,UAAU;IAAEa,GAAG,EAAEL,MAAM,CAACC,KAAK,CAAC,CAACI;EAAI,CAAC,CAAC;EAC9D,IAAIC,iBAAiB;EAErB,QAAQJ,YAAY;IAClB,KAAK,OAAO;MACV,KAAK,IAAIK,CAAC,GAAGN,KAAK,EAAEM,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9BH,OAAO,CAACI,OAAO,CAAC;UAAEX,IAAI,EAAEL,UAAU;UAAEa,GAAG,EAAEL,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAACF;QAAI,CAAC,CAAC;MAC/D;MACA;IACF,KAAK,YAAY;MACf,IAAIJ,KAAK,KAAK,CAAC,EAAE;QACfG,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;QACjB,CAAC,CAAC;MACJ;MACA;IACF,KAAK,cAAc;MACjBC,iBAAiB,GAAGN,MAAM,CAACS,SAAS,CACjC,UAAAC,KAAK;QAAA,OAAKA,KAAK,CAACf,IAAI,KAAKQ,gBAAgB;MAAA,EAC3C;MACDG,iBAAiB,GAAGA,iBAAiB,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MAEpE,IAAIL,KAAK,KAAKK,iBAAiB,EAAE;QAC/BF,OAAO,CAACI,OAAO,CAAC;UACdX,IAAI,EAAEL,UAAU;UAChBa,GAAG,EAAEL,MAAM,CAACM,iBAAiB,CAAC,CAACD;QACjC,CAAC,CAAC;MACJ;MACA;IACF,KAAK,SAAS;MAEZ;EAAM;EAGV,OAAOD,OAAO;AAChB,CAAC;AAED,IAAMO,WAAW,GAAG,SAAdA,WAAWA,CACfC,KAAwC,EACxCX,KAAa,EACbC,YAA0B,EAC1BC,gBAAoC,EACjC;EACH,IAAIC,OAAO;EAEX,IAAIF,YAAY,KAAK,SAAS,EAAE;IAC9B,IAAMW,UAAU,GAAGD,KAAK,CAACZ,MAAM,CAACC,KAAK,CAAC,CAACI,GAAG;IAE1CD,OAAO,GAAGQ,KAAK,CAACR,OAAO,CACpBU,MAAM,CAAE,UAAAC,EAAE;MAAA,OAAMA,EAAE,CAAClB,IAAI,KAAK,OAAO,GAAGkB,EAAE,CAACV,GAAG,KAAKQ,UAAU,GAAG,KAAM;IAAA,EAAC,CACrEG,MAAM,CAAC;MAAEnB,IAAI,EAAEL,UAAU;MAAEa,GAAG,EAAEQ;IAAW,CAAC,CAAC;EAClD,CAAC,MAAM;IACLT,OAAO,GAAGL,eAAe,CACvBa,KAAK,CAACZ,MAAM,EACZC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;EACH;EAEA,OAAAc,aAAA,CAAAA,aAAA,KACKL,KAAK;IACRX,KAAK,EAALA,KAAK;IACLG,OAAA,EAAAA;EAAA;AAEJ,CAAC;AAED,eAAe,SAASc,SAASA,CAAAC,IAAA,EAGZ;EAAA,IAFnBhB,gBAAgB,GAECgB,IAAA,CAFjBhB,gBAAgB;IAAAiB,iBAAA,GAECD,IAAA,CADjBjB,YAAY;IAAZA,YAAY,GAAAkB,iBAAA,cAAG,eAAAA,iBAAA;EAEf,IAAMC,MAGL,GAAAJ,aAAA,CAAAA,aAAA,KACI1B,UAAU;IAEbM,IAAI,EAAE,KAAK;IAEXyB,eAAe,WAAfA,eAAeA,CAAAC,KAAA,EAAiC;MAAA,IAA9BC,UAAU,GAAkBD,KAAA,CAA5BC,UAAU;QAAEC,cAAA,GAAgBF,KAAA,CAAhBE,cAAA;MAC5B,IAAMxB,KAAK,GACTE,gBAAgB,KAAKuB,SAAS,IAAIF,UAAU,CAACG,QAAQ,CAACxB,gBAAgB,CAAC,GACnEqB,UAAU,CAACI,OAAO,CAACzB,gBAAgB,CAAC,GACpC,CAAC;MAEP,IAAMH,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAAE,UAAAlC,IAAI;QAAA,OAAM;UACvCA,IAAI,EAAJA,IAAI;UACJU,GAAG,EAAG,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;UAC1BM,MAAM,EAAE6B,cAAc,CAAC9B,IAAI;QAC7B,CAAC;MAAA,CAAC,CAAC;MAEH,IAAMS,OAAO,GAAGL,eAAe,CAC7BC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MAED,OAAO;QACL2B,KAAK,EAAE,KAAK;QACZjC,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAMf,MAAM,EAAG,EAAC;QACtBW,KAAK,EAALA,KAAK;QACLuB,UAAU,EAAVA,UAAU;QACVpB,OAAO,EAAPA,OAAO;QACPJ,MAAA,EAAAA;MACF,CAAC;IACH,CAAC;IAED+B,kBAAkB,WAAlBA,kBAAkBA,CAACC,YAAY,EAAAC,KAAA,EAAkC;MAAA,IAAAC,KAAA,EAAAC,KAAA;MAAA,IAAAC,aAAA,EAAAC,cAAA;MAAA,IAA9Bb,UAAU,GAAkBS,KAAA,CAA5BT,UAAU;QAAEC,cAAA,GAAgBQ,KAAA,CAAhBR,cAAA;MAC7C,IAAIb,KAAK,GAAGoB,YAAY;MAExB,IAAIpB,KAAK,CAACkB,KAAK,KAAK,KAAK,EAAE;QACzB,OAAOlB,KAAK;MACd;MAEA,IAAMZ,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAAE,UAAAlC,IAAI,EAAK;QACtC,IAAMe,KAAK,GACTE,KAAK,CACLZ,MAAM,CAACsC,IAAI,CAAE,UAAAC,CAAC;UAAA,OAAKA,CAAC,CAAC5C,IAAI,KAAKA,IAAI;QAAA,EAAC;QAErC,OAAAsB,aAAA,CAAAA,aAAA,KACKP,KAAK;UACRf,IAAI,EAAJA,IAAI;UACJU,GAAG,EACDK,KAAK,IAAIA,KAAK,CAACf,IAAI,KAAKA,IAAI,IAAIe,KAAK,CAACL,GAAG,GACrCK,KAAK,CAACL,GAAG,GACR,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;UAC3BM,MAAM,EACJ6B,cAAc,CAAC9B,IAAI,CAAC,KAAK+B,SAAS,GAAAT,aAAA,CAAAA,aAAA,KAEzBQ,cAAc,CAAC9B,IAAI,CAAC,GACnBe,KAAK,GAAGA,KAAK,CAACd,MAAM,GAAG8B,SAAS,IAEtChB,KAAK,GACLA,KAAK,CAACd,MAAM,GACZ8B;QAAA;MAEV,CAAC,CAAC;MAEF,IAAMzB,KAAK,GAAGuC,IAAI,CAACC,GAAG,CACpBD,IAAI,CAACE,GAAG,CAAClB,UAAU,CAACI,OAAO,EAAAQ,aAAA,GAACxB,KAAK,CAACZ,MAAM,EAAAkC,KAAA,GAACtB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEX,KAAK,YAAAiC,KAAA,GAAI,CAAC,CAAC,cAAAE,aAAA,uBAA/BA,aAAA,CAAiCzC,IAAI,CAAC,EAAE,CAAC,CAAC,EACtEK,MAAM,CAAC2C,MAAM,GAAG,CAAC,CAClB;MAED,IAAMvC,OAAO,IAAA+B,KAAA,GACX,CAAAE,cAAA,GAAAzB,KAAK,CAACR,OAAO,cAAAiC,cAAA,uBAAbA,cAAA,CAAevB,MAAM,CAAE,UAAAC,EAAE;QAAA,OAAKf,MAAM,CAACsC,IAAI,CAAE,UAAAC,CAAC;UAAA,OAAKA,CAAC,CAAClC,GAAG,KAAKU,EAAE,CAACV,GAAG;QAAA,EAAC;MAAA,EAAC,YAAA8B,KAAA,GACnE,EAAE;MAEJ,OAAOxB,WAAW,CAChB;QACEmB,KAAK,EAAE,KAAK;QACZjC,IAAI,EAAE,KAAK;QACXQ,GAAG,EAAG,OAAMf,MAAM,EAAG,EAAC;QACtBW,KAAK,EAALA,KAAK;QACLuB,UAAU,EAAVA,UAAU;QACVpB,OAAO,EAAPA,OAAO;QACPJ,MAAA,EAAAA;MACF,CAAC,EACDC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;IACH,CAAC;IAEDyC,2BAA2B,WAA3BA,2BAA2BA,CACzBhC,KAAK,EAAAiC,KAAA,EAEL;MAAA,IADErB,UAAU,GAAmCqB,KAAA,CAA7CrB,UAAU;QAAEC,cAAc,GAAmBoB,KAAA,CAAjCpB,cAAc;QAAEqB,eAAA,GAAiBD,KAAA,CAAjBC,eAAA;MAE9B,IAAM9C,MAAM,GAAGwB,UAAU,CAACK,GAAG,CAC1B,UAAAlC,IAAI;QAAA,OACHiB,KAAK,CAACZ,MAAM,CAACsC,IAAI,CACd,UAAAC,CAAC;UAAA,OAAKA,CAAC,CAAC5C,IAAI,KAAKA,IAAI,IAAI,CAACmD,eAAe,CAACnB,QAAQ,CAACY,CAAC,CAAC5C,IAAI,CAAC;QAAA,EAC5D,IAAI;UACHA,IAAI,EAAJA,IAAI;UACJU,GAAG,EAAG,GAAEV,IAAK,IAAGL,MAAM,EAAG,EAAC;UAC1BM,MAAM,EAAE6B,cAAc,CAAC9B,IAAI;QAC7B,CAAC;MAAA,EACJ;MAED,IAAMM,KAAK,GAAGuC,IAAI,CAACE,GAAG,CACpB,CAAC,EACDlB,UAAU,CAACI,OAAO,CAAChB,KAAK,CAACZ,MAAM,CAACY,KAAK,CAACX,KAAK,CAAC,CAACN,IAAI,CAAC,CACnD;MAED,IAAIS,OAAO,GAAGQ,KAAK,CAACR,OAAO,CAACU,MAAM,CAE/B,UAAAC,EAAE;QAAA,OAAKA,EAAE,CAAClB,IAAI,KAAK,OAAO,IAAIG,MAAM,CAACsC,IAAI,CAAE,UAAAC,CAAC;UAAA,OAAKA,CAAC,CAAClC,GAAG,KAAKU,EAAE,CAACV,GAAG;QAAA,EAAC;MAAA,EACpE;MAED,IAAI,CAACD,OAAO,CAACuC,MAAM,EAAE;QACnBvC,OAAO,GAAGL,eAAe,CACvBC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;MACH;MAEA,OAAAc,aAAA,CAAAA,aAAA,KACKL,KAAK;QACRR,OAAO,EAAPA,OAAO;QACPoB,UAAU,EAAVA,UAAU;QACVxB,MAAM,EAANA,MAAM;QACNC,KAAA,EAAAA;MAAA;IAEJ,CAAC;IAED8C,qBAAqB,WAArBA,qBAAqBA,CAACnC,KAAK,EAAEP,GAAG,EAAE;MAChC,IAAMJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAAE,UAAA8B,CAAC;QAAA,OAAKA,CAAC,CAAClC,GAAG,KAAKA,GAAG;MAAA,EAAC;MAE1D,IAAIJ,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKW,KAAK,CAACX,KAAK,EAAE;QACzC,OAAOW,KAAK;MACd;MAEA,OAAOD,WAAW,CAACC,KAAK,EAAEX,KAAK,EAAEC,YAAY,EAAEC,gBAAgB,CAAC;IAClE,CAAC;IAED6C,iBAAiB,WAAjBA,iBAAiBA,CAACpC,KAAK,EAAEqC,MAAM,EAAAC,KAAA,EAAsC;MAAA,IAAlCzB,cAAc,GAAkByB,KAAA,CAAhCzB,cAAc;QAAE0B,cAAA,GAAgBD,KAAA,CAAhBC,cAAA;MACjD,QAAQF,MAAM,CAACpD,IAAI;QACjB,KAAK,SAAS;QACd,KAAK,UAAU;UAAE;YACf,IAAII,KAAK,GAAG,CAAC,CAAC;YAEd,IAAIgD,MAAM,CAACpD,IAAI,KAAK,UAAU,IAAIoD,MAAM,CAACnD,OAAO,CAACO,GAAG,EAAE;cACpDJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3B,UAAAC,KAAK;gBAAA,OAAKA,KAAK,CAACL,GAAG,KAAK4C,MAAM,CAACnD,OAAO,CAACO,GAAG;cAAA,EAC5C;YACH,CAAC,MAAM;cACLJ,KAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CAC3B,UAAAC,KAAK;gBAAA,OAAKA,KAAK,CAACf,IAAI,KAAKsD,MAAM,CAACnD,OAAO,CAACH,IAAI;cAAA,EAC9C;YACH;YAEA,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAOU,WAAW,CAAAM,aAAA,CAAAA,aAAA,KAEXL,KAAK;cACRZ,MAAM,EAAEY,KAAK,CAACZ,MAAM,CAAC6B,GAAG,CAAC,UAACnB,KAAK,EAAEH,CAAC,EAAK;gBACrC,IAAIA,CAAC,KAAKN,KAAK,EAAE;kBACf,OAAOS,KAAK;gBACd;gBAEA,IAAM0C,KAAK,GAAGD,cAAc,CAACzC,KAAK,CAACf,IAAI,CAAC;gBAExC,IAAM0D,SAAS,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAExD,MAAM,EAAEc,KAAK,CAACd;gBAAO,CAAC,CAAC;gBACnD,IAAM0D,MAAM,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;kBAAExD,MAAM,EAAEqD,MAAM,CAACnD,OAAO,CAACF;gBAAO,CAAC,CAAC;gBAEzD,IAAMS,GAAG,GACPgD,SAAS,KAAKC,MAAM,GAChB5C,KAAK,CAACL,GAAG,GACR,GAAEK,KAAK,CAACf,IAAK,IAAGL,MAAM,EAAG,EAAC;gBAEjC,IAAIM,MAAM;gBAEV,IACEqD,MAAM,CAACpD,IAAI,KAAK,UAAU,IAC1BoD,MAAM,CAACnD,OAAO,CAACyD,KAAK,IACpBF,SAAS,KAAKC,MAAM,EACpB;kBACA1D,MAAM,GACJqD,MAAM,CAACnD,OAAO,CAACF,MAAM,KAAK8B,SAAS,IACnCD,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,KAAK+B,SAAS,GAAAT,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAE/BQ,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,GAC1Be,KAAK,CAACd,MAAM,GACZqD,MAAM,CAACnD,OAAO,CAACF,MAAA,IAEpBc,KAAK,CAACd,MAAM;gBACpB,CAAC,MAAM;kBACLA,MAAM,GACJ6B,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,KAAK+B,SAAS,GAAAT,aAAA,CAAAA,aAAA,KAE/BQ,cAAc,CAACf,KAAK,CAACf,IAAI,CAAC,GAC1BsD,MAAM,CAACnD,OAAO,CAACF,MAAA,IAEpBqD,MAAM,CAACnD,OAAO,CAACF,MAAM;gBAC7B;gBAEA,IAAM4D,IAAI,GACRP,MAAM,CAACpD,IAAI,KAAK,UAAU,IAAIoD,MAAM,CAACnD,OAAO,CAAC0D,IAAI,IAAI,IAAI,GACrDP,MAAM,CAACnD,OAAO,CAAC0D,IAAI,GACnB9C,KAAK,CAAC8C,IAAI;gBAEhB,OAAO5D,MAAM,KAAKc,KAAK,CAACd,MAAM,IAAI4D,IAAI,KAAK9C,KAAK,CAAC8C,IAAI,GAAAvC,aAAA,CAAAA,aAAA,KAC5CP,KAAK;kBAAEL,GAAG,EAAHA,GAAG;kBAAEmD,IAAI,EAAJA,IAAI;kBAAE5D,MAAA,EAAAA;gBAAA,KACvBc,KAAK;cACX,CAAC;YAAA,IAEHT,KAAK,EACLC,YAAY,EACZC,gBAAgB,CACjB;UACH;QAEA,KAAK,SAAS;UAAE;YACd,IAAIS,KAAK,CAACR,OAAO,CAACuC,MAAM,KAAK,CAAC,EAAE;cAC9B,OAAO,IAAI;YACb;YAEA,IAAMc,WAAW,GAAG7C,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACuC,MAAM,GAAG,CAAC,CAAC,CAACtC,GAAG;YAC/D,IAAMJ,MAAK,GAAGW,KAAK,CAACZ,MAAM,CAACS,SAAS,CACjC,UAAAC,KAAK;cAAA,OAAKA,KAAK,CAACL,GAAG,KAAKoD,WAAW;YAAA,EACrC;YAED,IAAIxD,MAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,OAAAgB,aAAA,CAAAA,aAAA,KACKL,KAAK;cACRR,OAAO,EAAEQ,KAAK,CAACR,OAAO,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACnCzD,KAAA,EAAAA;YAAA;UAEJ;QAEA;UACE,OAAOV,UAAU,CAACyD,iBAAiB,CAACpC,KAAK,EAAEqC,MAAM,CAAC;MAAC;IAEzD,CAAC;IAEDU,uBAAuB,WAAvBA,uBAAuBA,CAACV,MAAM,EAAE;MAC9B,OAAOA,MAAM,CAACpD,IAAI,KAAK,UAAU;IACnC,CAAC;IAED+D,cAAc,EAAEnE;EAAA,EACjB;EAED,OAAO4B,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}