{"ast": null, "code": "import React from 'react';\nimport PanResponder from \"react-native-web/dist/exports/PanResponder\";\nexport function startShouldSetPanResponder() {\n  return true;\n}\nexport function moveShouldSetPanResponder(_event, gesture) {\n  var dx = gesture.dx,\n    dy = gesture.dy;\n  var offset = 2;\n  return Math.abs(dx) > offset || Math.abs(dy) > offset;\n}\nexport function shouldDismissView(newAnimatedValue, gesture) {\n  var dismissThreshold = 0.65;\n  var vy = gesture.vy,\n    dy = gesture.dy;\n  return newAnimatedValue <= dismissThreshold || Math.abs(vy) >= dismissThreshold && dy < 0;\n}\nexport function usePanResponder(_ref) {\n  var animatedValue = _ref.animatedValue,\n    computeNewAnimatedValueForGesture = _ref.computeNewAnimatedValueForGesture,\n    onDismiss = _ref.onDismiss,\n    onRestore = _ref.onRestore,\n    onStart = _ref.onStart,\n    onEnd = _ref.onEnd,\n    disable = _ref.disable;\n  var onGrant = React.useCallback(function () {\n    if (disable) return;\n    onStart();\n  }, [onStart, disable]);\n  var onMove = React.useCallback(function (_event, gesture) {\n    var _animatedValue$curren;\n    if (disable) return;\n    var newAnimatedValue = computeNewAnimatedValueForGesture(gesture);\n    (_animatedValue$curren = animatedValue.current) == null ? void 0 : _animatedValue$curren.setValue(newAnimatedValue);\n  }, [animatedValue, computeNewAnimatedValueForGesture, disable]);\n  var onRelease = React.useCallback(function (_event, gesture) {\n    if (disable) return;\n    var newAnimatedValue = computeNewAnimatedValueForGesture(gesture);\n    onEnd();\n    if (shouldDismissView(newAnimatedValue, gesture)) {\n      onDismiss();\n    } else {\n      onRestore();\n    }\n  }, [computeNewAnimatedValueForGesture, onEnd, onDismiss, onRestore, disable]);\n  var panResponder = React.useMemo(function () {\n    return PanResponder.create({\n      onStartShouldSetPanResponder: startShouldSetPanResponder,\n      onPanResponderGrant: onGrant,\n      onMoveShouldSetPanResponder: moveShouldSetPanResponder,\n      onMoveShouldSetPanResponderCapture: moveShouldSetPanResponder,\n      onPanResponderMove: onMove,\n      onPanResponderRelease: onRelease\n    });\n  }, [onMove, onRelease, onGrant]);\n  return {\n    panResponder: panResponder,\n    onGrant: onGrant,\n    onMove: onMove,\n    onRelease: onRelease\n  };\n}", "map": {"version": 3, "names": ["React", "PanResponder", "startShouldSetPanResponder", "moveShouldSetPanResponder", "_event", "gesture", "dx", "dy", "offset", "Math", "abs", "<PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "newAnimatedValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vy", "usePanResponder", "_ref", "animatedValue", "computeNewAnimatedValueForGesture", "on<PERSON><PERSON><PERSON>", "onRestore", "onStart", "onEnd", "disable", "onGrant", "useCallback", "onMove", "_animatedValue$curren", "current", "setValue", "onRelease", "panResponder", "useMemo", "create", "onStartShouldSetPanResponder", "onPanResponderGrant", "onMoveShouldSetPanResponder", "onMoveShouldSetPanResponderCapture", "onPanResponderMove", "onPanResponderRelease"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-toast-message/lib/src/hooks/usePanResponder.js"], "sourcesContent": ["import React from 'react';\nimport { PanResponder } from 'react-native';\nexport function startShouldSetPanResponder() {\n    return true;\n}\nexport function moveShouldSetPanResponder(_event, gesture) {\n    const { dx, dy } = gesture;\n    // Fixes onPress handler\n    // https://github.com/calintamas/react-native-toast-message/issues/113\n    const offset = 2;\n    return Math.abs(dx) > offset || Math.abs(dy) > offset;\n}\nexport function shouldDismissView(newAnimatedValue, gesture) {\n    const dismissThreshold = 0.65;\n    const { vy, dy } = gesture;\n    return (newAnimatedValue <= dismissThreshold ||\n        (Math.abs(vy) >= dismissThreshold && dy < 0));\n}\nexport function usePanResponder({ animatedValue, computeNewAnimatedValueForGesture, onDismiss, onRestore, onStart, onEnd, disable }) {\n    const onGrant = React.useCallback(() => {\n        if (disable)\n            return;\n        onStart();\n    }, [onStart, disable]);\n    const onMove = React.useCallback((_event, gesture) => {\n        if (disable)\n            return;\n        const newAnimatedValue = computeNewAnimatedValueForGesture(gesture);\n        animatedValue.current?.setValue(newAnimatedValue);\n    }, [animatedValue, computeNewAnimatedValueForGesture, disable]);\n    const onRelease = React.useCallback((_event, gesture) => {\n        if (disable)\n            return;\n        const newAnimatedValue = computeNewAnimatedValueForGesture(gesture);\n        onEnd();\n        if (shouldDismissView(newAnimatedValue, gesture)) {\n            onDismiss();\n        }\n        else {\n            onRestore();\n        }\n    }, [computeNewAnimatedValueForGesture, onEnd, onDismiss, onRestore, disable]);\n    const panResponder = React.useMemo(() => PanResponder.create({\n        onStartShouldSetPanResponder: startShouldSetPanResponder,\n        onPanResponderGrant: onGrant,\n        onMoveShouldSetPanResponder: moveShouldSetPanResponder,\n        onMoveShouldSetPanResponderCapture: moveShouldSetPanResponder,\n        onPanResponderMove: onMove,\n        onPanResponderRelease: onRelease\n    }), [onMove, onRelease, onGrant]);\n    return {\n        panResponder,\n        onGrant,\n        onMove,\n        onRelease,\n    };\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,YAAA;AAE1B,OAAO,SAASC,0BAA0BA,CAAA,EAAG;EACzC,OAAO,IAAI;AACf;AACA,OAAO,SAASC,yBAAyBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvD,IAAQC,EAAE,GAASD,OAAO,CAAlBC,EAAE;IAAEC,EAAE,GAAKF,OAAO,CAAdE,EAAE;EAGd,IAAMC,MAAM,GAAG,CAAC;EAChB,OAAOC,IAAI,CAACC,GAAG,CAACJ,EAAE,CAAC,GAAGE,MAAM,IAAIC,IAAI,CAACC,GAAG,CAACH,EAAE,CAAC,GAAGC,MAAM;AACzD;AACA,OAAO,SAASG,iBAAiBA,CAACC,gBAAgB,EAAEP,OAAO,EAAE;EACzD,IAAMQ,gBAAgB,GAAG,IAAI;EAC7B,IAAQC,EAAE,GAAST,OAAO,CAAlBS,EAAE;IAAEP,EAAE,GAAKF,OAAO,CAAdE,EAAE;EACd,OAAQK,gBAAgB,IAAIC,gBAAgB,IACvCJ,IAAI,CAACC,GAAG,CAACI,EAAE,CAAC,IAAID,gBAAgB,IAAIN,EAAE,GAAG,CAAE;AACpD;AACA,OAAO,SAASQ,eAAeA,CAAAC,IAAA,EAAsG;EAAA,IAAnGC,aAAa,GAAAD,IAAA,CAAbC,aAAa;IAAEC,iCAAiC,GAAAF,IAAA,CAAjCE,iCAAiC;IAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS;IAAEC,SAAS,GAAAJ,IAAA,CAATI,SAAS;IAAEC,OAAO,GAAAL,IAAA,CAAPK,OAAO;IAAEC,KAAK,GAAAN,IAAA,CAALM,KAAK;IAAEC,OAAO,GAAAP,IAAA,CAAPO,OAAO;EAC7H,IAAMC,OAAO,GAAGxB,KAAK,CAACyB,WAAW,CAAC,YAAM;IACpC,IAAIF,OAAO,EACP;IACJF,OAAO,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,OAAO,EAAEE,OAAO,CAAC,CAAC;EACtB,IAAMG,MAAM,GAAG1B,KAAK,CAACyB,WAAW,CAAC,UAACrB,MAAM,EAAEC,OAAO,EAAK;IAAA,IAAAsB,qBAAA;IAClD,IAAIJ,OAAO,EACP;IACJ,IAAMX,gBAAgB,GAAGM,iCAAiC,CAACb,OAAO,CAAC;IACnE,CAAAsB,qBAAA,GAAAV,aAAa,CAACW,OAAO,qBAArBD,qBAAA,CAAuBE,QAAQ,CAACjB,gBAAgB,CAAC;EACrD,CAAC,EAAE,CAACK,aAAa,EAAEC,iCAAiC,EAAEK,OAAO,CAAC,CAAC;EAC/D,IAAMO,SAAS,GAAG9B,KAAK,CAACyB,WAAW,CAAC,UAACrB,MAAM,EAAEC,OAAO,EAAK;IACrD,IAAIkB,OAAO,EACP;IACJ,IAAMX,gBAAgB,GAAGM,iCAAiC,CAACb,OAAO,CAAC;IACnEiB,KAAK,CAAC,CAAC;IACP,IAAIX,iBAAiB,CAACC,gBAAgB,EAAEP,OAAO,CAAC,EAAE;MAC9Cc,SAAS,CAAC,CAAC;IACf,CAAC,MACI;MACDC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,CAACF,iCAAiC,EAAEI,KAAK,EAAEH,SAAS,EAAEC,SAAS,EAAEG,OAAO,CAAC,CAAC;EAC7E,IAAMQ,YAAY,GAAG/B,KAAK,CAACgC,OAAO,CAAC;IAAA,OAAM/B,YAAY,CAACgC,MAAM,CAAC;MACzDC,4BAA4B,EAAEhC,0BAA0B;MACxDiC,mBAAmB,EAAEX,OAAO;MAC5BY,2BAA2B,EAAEjC,yBAAyB;MACtDkC,kCAAkC,EAAElC,yBAAyB;MAC7DmC,kBAAkB,EAAEZ,MAAM;MAC1Ba,qBAAqB,EAAET;IAC3B,CAAC,CAAC;EAAA,GAAE,CAACJ,MAAM,EAAEI,SAAS,EAAEN,OAAO,CAAC,CAAC;EACjC,OAAO;IACHO,YAAY,EAAZA,YAAY;IACZP,OAAO,EAAPA,OAAO;IACPE,MAAM,EAANA,MAAM;IACNI,SAAS,EAATA;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}