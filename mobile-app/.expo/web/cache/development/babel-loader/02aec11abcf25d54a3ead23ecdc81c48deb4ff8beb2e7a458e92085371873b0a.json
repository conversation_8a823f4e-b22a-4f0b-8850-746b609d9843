{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/expo-modules-core/src/TypedArrays.types.ts"], "sourcesContent": ["/** A union type for all integer based [`TypedArray` objects](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray#typedarray_objects). */\nexport type IntBasedTypedArray = Int8Array | Int16Array | Int32Array;\n\n/** A union type for all unsigned integer based [`TypedArray` objects](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray#typedarray_objects). */\nexport type UintBasedTypedArray = Uint8Array | Uint8ClampedArray | Uint16Array | Uint32Array;\n\n/** A union type for all floating point based [`TypedArray` objects](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray#typedarray_objects). */\nexport type FloatBasedTypedArray = Float32Array | Float64Array;\n\n/** A [`TypedArray`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray) describes an array-like view of an underlying binary data buffer. */\nexport type TypedArray = IntBasedTypedArray | UintBasedTypedArray | FloatBasedTypedArray;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}