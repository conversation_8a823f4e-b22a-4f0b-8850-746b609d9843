{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport AnimatedInterpolation from \"./AnimatedInterpolation\";\nimport AnimatedValue from \"./AnimatedValue\";\nimport AnimatedWithChildren from \"./AnimatedWithChildren\";\nvar AnimatedAddition = function (_AnimatedWithChildren) {\n  function AnimatedAddition(a, b) {\n    var _this;\n    _classCallCheck(this, AnimatedAddition);\n    _this = _callSuper(this, AnimatedAddition);\n    _this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    _this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n    return _this;\n  }\n  _inherits(AnimatedAddition, _AnimatedWithChildren);\n  return _createClass(AnimatedAddition, [{\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      this._a.__makeNative(platformConfig);\n      this._b.__makeNative(platformConfig);\n      _superPropGet(AnimatedAddition, \"__makeNative\", this, 3)([platformConfig]);\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {\n      return this._a.__getValue() + this._b.__getValue();\n    }\n  }, {\n    key: \"interpolate\",\n    value: function interpolate(config) {\n      return new AnimatedInterpolation(this, config);\n    }\n  }, {\n    key: \"__attach\",\n    value: function __attach() {\n      this._a.__addChild(this);\n      this._b.__addChild(this);\n    }\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      this._a.__removeChild(this);\n      this._b.__removeChild(this);\n      _superPropGet(AnimatedAddition, \"__detach\", this, 3)([]);\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      return {\n        type: 'addition',\n        input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n      };\n    }\n  }]);\n}(AnimatedWithChildren);\nexport default AnimatedAddition;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "AnimatedInterpolation", "AnimatedValue", "AnimatedWithChildren", "AnimatedAddition", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "a", "b", "_this", "_a", "_b", "key", "value", "__makeNative", "platformConfig", "__getValue", "interpolate", "config", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedAddition.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport AnimatedInterpolation from './AnimatedInterpolation';\nimport AnimatedValue from './AnimatedValue';\nimport AnimatedWithChildren from './AnimatedWithChildren';\nclass AnimatedAddition extends AnimatedWithChildren {\n  constructor(a, b) {\n    super();\n    this._a = typeof a === 'number' ? new AnimatedValue(a) : a;\n    this._b = typeof b === 'number' ? new AnimatedValue(b) : b;\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getValue() {\n    return this._a.__getValue() + this._b.__getValue();\n  }\n  interpolate(config) {\n    return new AnimatedInterpolation(this, config);\n  }\n  __attach() {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'addition',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n    };\n  }\n}\nexport default AnimatedAddition;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,qBAAqB;AAC5B,OAAOC,aAAa;AACpB,OAAOC,oBAAoB;AAA+B,IACpDC,gBAAgB,aAAAC,qBAAA;EACpB,SAAAD,iBAAYE,CAAC,EAAEC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA7B,eAAA,OAAAyB,gBAAA;IAChBI,KAAA,GAAAvB,UAAA,OAAAmB,gBAAA;IACAI,KAAA,CAAKC,EAAE,GAAG,OAAOH,CAAC,KAAK,QAAQ,GAAG,IAAIJ,aAAa,CAACI,CAAC,CAAC,GAAGA,CAAC;IAC1DE,KAAA,CAAKE,EAAE,GAAG,OAAOH,CAAC,KAAK,QAAQ,GAAG,IAAIL,aAAa,CAACK,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAC,KAAA;EAC7D;EAACxB,SAAA,CAAAoB,gBAAA,EAAAC,qBAAA;EAAA,OAAAzB,YAAA,CAAAwB,gBAAA;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACJ,EAAE,CAACG,YAAY,CAACC,cAAc,CAAC;MACpChB,aAAA,CAAAM,gBAAA,4BAAmBU,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAG,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACN,EAAE,CAACM,UAAU,CAAC,CAAC,GAAG,IAAI,CAACL,EAAE,CAACK,UAAU,CAAC,CAAC;IACpD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAI,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAIhB,qBAAqB,CAAC,IAAI,EAAEgB,MAAM,CAAC;IAChD;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAM,QAAQA,CAAA,EAAG;MACT,IAAI,CAACT,EAAE,CAACU,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACT,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAQ,QAAQA,CAAA,EAAG;MACT,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACX,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3BvB,aAAA,CAAAM,gBAAA;IACF;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAU,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACf,EAAE,CAACgB,cAAc,CAAC,CAAC,EAAE,IAAI,CAACf,EAAE,CAACe,cAAc,CAAC,CAAC;MAC5D,CAAC;IACH;EAAC;AAAA,EA/B4BtB,oBAAoB;AAiCnD,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}