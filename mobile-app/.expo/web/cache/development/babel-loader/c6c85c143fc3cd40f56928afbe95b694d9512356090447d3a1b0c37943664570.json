{"ast": null, "code": "export default function isRecordEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  var aKeys = Object.keys(a);\n  var bKeys = Object.keys(b);\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n  return aKeys.every(function (key) {\n    return a[key] === b[key];\n  });\n}", "map": {"version": 3, "names": ["isRecordEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "Object", "keys", "b<PERSON><PERSON><PERSON>", "length", "every", "key"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/isRecordEqual.tsx"], "sourcesContent": ["/**\n * Compare two records with primitive values as the content.\n */\nexport default function isRecordEqual(\n  a: Record<string, any>,\n  b: Record<string, any>\n) {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n\n  return aKeys.every((key) => a[key] === b[key]);\n}\n"], "mappings": "AAGA,eAAe,SAASA,aAAaA,CACnCC,CAAsB,EACtBC,CAAsB,EACtB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EAEA,IAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAC5B,IAAMK,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC;EAE5B,IAAIC,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,OAAOJ,KAAK,CAACK,KAAK,CAAE,UAAAC,GAAG;IAAA,OAAKR,CAAC,CAACQ,GAAG,CAAC,KAAKP,CAAC,CAACO,GAAG,CAAC;EAAA,EAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}