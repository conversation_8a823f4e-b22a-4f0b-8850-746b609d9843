{"ast": null, "code": "import * as React from 'react';\nvar PreventRemoveContext = React.createContext(undefined);\nexport default PreventRemoveContext;", "map": {"version": 3, "names": ["React", "PreventRemoveContext", "createContext", "undefined"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/PreventRemoveContext.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A type of an object that have a route key as an object key\n * and a value whether to prevent that route.\n */\nexport type PreventedRoutes = Record<string, { preventRemove: boolean }>;\n\nconst PreventRemoveContext = React.createContext<\n  | {\n      preventedRoutes: PreventedRoutes;\n      setPreventRemove: (\n        id: string,\n        routeKey: string,\n        preventRemove: boolean\n      ) => void;\n    }\n  | undefined\n>(undefined);\n\nexport default PreventRemoveContext;\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAQ9B,IAAMC,oBAAoB,GAAGD,KAAK,CAACE,aAAa,CAU9CC,SAAS,CAAC;AAEZ,eAAeF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}