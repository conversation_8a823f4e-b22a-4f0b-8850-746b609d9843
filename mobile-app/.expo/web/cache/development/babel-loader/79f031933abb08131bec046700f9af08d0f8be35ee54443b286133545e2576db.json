{"ast": null, "code": "import * as React from 'react';\nvar NavigationRouteContext = React.createContext(undefined);\nexport default NavigationRouteContext;", "map": {"version": 3, "names": ["React", "NavigationRouteContext", "createContext", "undefined"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/core/src/NavigationRouteContext.tsx"], "sourcesContent": ["import type { Route } from '@react-navigation/routers';\nimport * as React from 'react';\n\n/**\n * Context which holds the route prop for a screen.\n */\nconst NavigationRouteContext = React.createContext<Route<string> | undefined>(\n  undefined\n);\n\nexport default NavigationRouteContext;\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAK9B,IAAMC,sBAAsB,GAAGD,KAAK,CAACE,aAAa,CAChDC,SAAS,CACV;AAED,eAAeF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}