{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { Platform } from 'expo-modules-core';\nimport path from 'path-browserify';\nimport PixelRatio from \"react-native-web/dist/exports/PixelRatio\";\nimport URL from 'url-parse';\nimport AssetSourceResolver from \"./AssetSourceResolver\";\nimport { getManifest, getManifest2, manifestBaseUrl } from \"./PlatformUtils\";\nvar assetMapOverride = getManifest().assetMapOverride;\nexport function selectAssetSource(meta) {\n  var _manifest2$extra, _manifest2$extra$expo;\n  if (assetMapOverride && assetMapOverride.hasOwnProperty(meta.hash)) {\n    meta = _objectSpread(_objectSpread({}, meta), assetMapOverride[meta.hash]);\n  }\n  var scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  var index = meta.scales.findIndex(function (s) {\n    return s === scale;\n  });\n  var hash = meta.fileHashes ? meta.fileHashes[index] || meta.fileHashes[0] : meta.hash;\n  var uri = meta.fileUris ? meta.fileUris[index] || meta.fileUris[0] : meta.uri;\n  if (uri) {\n    return {\n      uri: resolveUri(uri),\n      hash: hash\n    };\n  }\n  var assetUrlOverride = getManifest().assetUrlOverride;\n  if (assetUrlOverride) {\n    var _uri = path.join(assetUrlOverride, hash);\n    return {\n      uri: resolveUri(_uri),\n      hash: hash\n    };\n  }\n  var fileScale = scale === 1 ? '' : `@${scale}x`;\n  var fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  var suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}?platform=${encodeURIComponent(Platform.OS)}&hash=${encodeURIComponent(meta.hash)}`;\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    var _uri2 = meta.httpServerLocation + suffix;\n    return {\n      uri: _uri2,\n      hash: hash\n    };\n  }\n  var manifest2 = getManifest2();\n  if (manifest2 != null && (_manifest2$extra = manifest2.extra) != null && (_manifest2$extra$expo = _manifest2$extra.expoGo) != null && _manifest2$extra$expo.developer) {\n    var baseUrl = new URL(`http://${manifest2.extra.expoGo.debuggerHost}`);\n    baseUrl.set('pathname', meta.httpServerLocation + suffix);\n    return {\n      uri: baseUrl.href,\n      hash: hash\n    };\n  }\n  if (getManifest().developer) {\n    var _baseUrl = new URL(getManifest().bundleUrl);\n    _baseUrl.set('pathname', meta.httpServerLocation + suffix);\n    return {\n      uri: _baseUrl.href,\n      hash: hash\n    };\n  }\n  return {\n    uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`,\n    hash: hash\n  };\n}\nexport function resolveUri(uri) {\n  if (!manifestBaseUrl) {\n    return uri;\n  }\n  var _URL = new URL(uri),\n    protocol = _URL.protocol;\n  if (protocol !== '') {\n    return uri;\n  }\n  var baseUrl = new URL(manifestBaseUrl);\n  var resolvedPath = uri.startsWith('/') ? uri : path.join(baseUrl.pathname, uri);\n  baseUrl.set('pathname', resolvedPath);\n  return baseUrl.href;\n}", "map": {"version": 3, "names": ["Platform", "path", "PixelRatio", "URL", "AssetSourceResolver", "getManifest", "getManifest2", "manifestBaseUrl", "assetMapOverride", "selectAssetSource", "meta", "_manifest2$extra", "_manifest2$extra$expo", "hasOwnProperty", "hash", "_objectSpread", "scale", "pickScale", "scales", "get", "index", "findIndex", "s", "fileHashes", "uri", "fileUris", "resolve<PERSON>ri", "assetUrlOverride", "join", "fileScale", "fileExtension", "type", "encodeURIComponent", "suffix", "name", "OS", "test", "httpServerLocation", "manifest2", "extra", "expoGo", "developer", "baseUrl", "debuggerHost", "set", "href", "bundleUrl", "_URL", "protocol", "<PERSON><PERSON><PERSON>", "startsWith", "pathname"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/expo-asset/src/AssetSources.ts"], "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport path from 'path-browserify';\nimport { PixelRatio } from 'react-native';\nimport { PackagerAsset } from 'react-native/Libraries/Image/AssetRegistry';\nimport URL from 'url-parse';\n\nimport AssetSourceResolver from './AssetSourceResolver';\nimport { getManifest, getManifest2, manifestBaseUrl } from './PlatformUtils';\n\n// @docsMissing\nexport type AssetMetadata = Pick<\n  PackagerAsset,\n  'httpServerLocation' | 'name' | 'hash' | 'type' | 'scales' | 'width' | 'height'\n> & {\n  uri?: string;\n  fileHashes?: string[];\n  fileUris?: string[];\n};\n\nexport type AssetSource = {\n  uri: string;\n  hash: string;\n};\n\n// Fast lookup check if asset map has any overrides in the manifest\nconst assetMapOverride = getManifest().assetMapOverride;\n\n/**\n * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n * a { uri, hash } pair for the specific asset file.\n *\n * If the asset isn't an image with multiple scales, the first file is selected.\n */\nexport function selectAssetSource(meta: AssetMetadata): AssetSource {\n  // Override with the asset map in manifest if available\n  if (assetMapOverride && assetMapOverride.hasOwnProperty(meta.hash)) {\n    meta = { ...meta, ...assetMapOverride[meta.hash] };\n  }\n\n  // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n  // explicitly provided URIs\n  const scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  const index = meta.scales.findIndex((s) => s === scale);\n  const hash = meta.fileHashes ? meta.fileHashes[index] || meta.fileHashes[0] : meta.hash;\n\n  // Allow asset processors to directly provide the URL to load\n  const uri = meta.fileUris ? meta.fileUris[index] || meta.fileUris[0] : meta.uri;\n  if (uri) {\n    return { uri: resolveUri(uri), hash };\n  }\n\n  // Check if the assetUrl was overridden in the manifest\n  const assetUrlOverride = getManifest().assetUrlOverride;\n  if (assetUrlOverride) {\n    const uri = path.join(assetUrlOverride, hash);\n    return { uri: resolveUri(uri), hash };\n  }\n\n  const fileScale = scale === 1 ? '' : `@${scale}x`;\n  const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  const suffix = `/${encodeURIComponent(\n    meta.name\n  )}${fileScale}${fileExtension}?platform=${encodeURIComponent(\n    Platform.OS\n  )}&hash=${encodeURIComponent(meta.hash)}`;\n\n  // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n  // development server or production CDN URL origin\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    const uri = meta.httpServerLocation + suffix;\n    return { uri, hash };\n  }\n\n  // For assets during development using manifest2, we use the development server's URL origin\n  const manifest2 = getManifest2();\n\n  if (manifest2?.extra?.expoGo?.developer) {\n    const baseUrl = new URL(`http://${manifest2.extra.expoGo.debuggerHost}`);\n    baseUrl.set('pathname', meta.httpServerLocation + suffix);\n\n    return {\n      uri: baseUrl.href,\n      hash,\n    };\n  }\n\n  // For assets during development, we use the development server's URL origin\n  if (getManifest().developer) {\n    const baseUrl = new URL(getManifest().bundleUrl);\n    baseUrl.set('pathname', meta.httpServerLocation + suffix);\n    return { uri: baseUrl.href, hash };\n  }\n\n  // Production CDN URIs are based on each asset file hash\n  return {\n    uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`,\n    hash,\n  };\n}\n\n/**\n * Resolves the given URI to an absolute URI. If the given URI is already an absolute URI, it is\n * simply returned. Otherwise, if it is a relative URI, it is resolved relative to the manifest's\n * base URI.\n */\nexport function resolveUri(uri: string): string {\n  if (!manifestBaseUrl) {\n    return uri;\n  }\n\n  const { protocol } = new URL(uri);\n  if (protocol !== '') {\n    return uri;\n  }\n\n  const baseUrl = new URL(manifestBaseUrl);\n  const resolvedPath = uri.startsWith('/') ? uri : path.join(baseUrl.pathname, uri);\n  baseUrl.set('pathname', resolvedPath);\n  return baseUrl.href;\n}\n"], "mappings": ";;;AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,IAAI,MAAM,iBAAiB;AAAC,OAAAC,UAAA;AAGnC,OAAOC,GAAG,MAAM,WAAW;AAE3B,OAAOC,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,YAAY,EAAEC,eAAe;AAkBnD,IAAMC,gBAAgB,GAAGH,WAAW,EAAE,CAACG,gBAAgB;AAQvD,OAAM,SAAUC,iBAAiBA,CAACC,IAAmB;EAAA,IAAAC,gBAAA,EAAAC,qBAAA;EAEnD,IAAIJ,gBAAgB,IAAIA,gBAAgB,CAACK,cAAc,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;IAClEJ,IAAI,GAAAK,aAAA,CAAAA,aAAA,KAAQL,IAAI,GAAKF,gBAAgB,CAACE,IAAI,CAACI,IAAI,CAAC,CAAE;;EAKpD,IAAME,KAAK,GAAGZ,mBAAmB,CAACa,SAAS,CAACP,IAAI,CAACQ,MAAM,EAAEhB,UAAU,CAACiB,GAAG,EAAE,CAAC;EAC1E,IAAMC,KAAK,GAAGV,IAAI,CAACQ,MAAM,CAACG,SAAS,CAAC,UAACC,CAAC;IAAA,OAAKA,CAAC,KAAKN,KAAK;EAAA,EAAC;EACvD,IAAMF,IAAI,GAAGJ,IAAI,CAACa,UAAU,GAAGb,IAAI,CAACa,UAAU,CAACH,KAAK,CAAC,IAAIV,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,GAAGb,IAAI,CAACI,IAAI;EAGvF,IAAMU,GAAG,GAAGd,IAAI,CAACe,QAAQ,GAAGf,IAAI,CAACe,QAAQ,CAACL,KAAK,CAAC,IAAIV,IAAI,CAACe,QAAQ,CAAC,CAAC,CAAC,GAAGf,IAAI,CAACc,GAAG;EAC/E,IAAIA,GAAG,EAAE;IACP,OAAO;MAAEA,GAAG,EAAEE,UAAU,CAACF,GAAG,CAAC;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAIvC,IAAMa,gBAAgB,GAAGtB,WAAW,EAAE,CAACsB,gBAAgB;EACvD,IAAIA,gBAAgB,EAAE;IACpB,IAAMH,IAAG,GAAGvB,IAAI,CAAC2B,IAAI,CAACD,gBAAgB,EAAEb,IAAI,CAAC;IAC7C,OAAO;MAAEU,GAAG,EAAEE,UAAU,CAACF,IAAG,CAAC;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAGvC,IAAMe,SAAS,GAAGb,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,IAAIA,KAAK,GAAG;EACjD,IAAMc,aAAa,GAAGpB,IAAI,CAACqB,IAAI,GAAG,IAAIC,kBAAkB,CAACtB,IAAI,CAACqB,IAAI,CAAC,EAAE,GAAG,EAAE;EAC1E,IAAME,MAAM,GAAG,IAAID,kBAAkB,CACnCtB,IAAI,CAACwB,IAAI,CACV,GAAGL,SAAS,GAAGC,aAAa,aAAaE,kBAAkB,CAC1DhC,QAAQ,CAACmC,EAAE,CACZ,SAASH,kBAAkB,CAACtB,IAAI,CAACI,IAAI,CAAC,EAAE;EAIzC,IAAI,cAAc,CAACsB,IAAI,CAAC1B,IAAI,CAAC2B,kBAAkB,CAAC,EAAE;IAChD,IAAMb,KAAG,GAAGd,IAAI,CAAC2B,kBAAkB,GAAGJ,MAAM;IAC5C,OAAO;MAAET,GAAG,EAAHA,KAAG;MAAEV,IAAI,EAAJA;IAAI,CAAE;;EAItB,IAAMwB,SAAS,GAAGhC,YAAY,EAAE;EAEhC,IAAIgC,SAAS,aAAA3B,gBAAA,GAAT2B,SAAS,CAAEC,KAAK,cAAA3B,qBAAA,GAAhBD,gBAAA,CAAkB6B,MAAM,aAAxB5B,qBAAA,CAA0B6B,SAAS,EAAE;IACvC,IAAMC,OAAO,GAAG,IAAIvC,GAAG,CAAC,UAAUmC,SAAS,CAACC,KAAK,CAACC,MAAM,CAACG,YAAY,EAAE,CAAC;IACxED,OAAO,CAACE,GAAG,CAAC,UAAU,EAAElC,IAAI,CAAC2B,kBAAkB,GAAGJ,MAAM,CAAC;IAEzD,OAAO;MACLT,GAAG,EAAEkB,OAAO,CAACG,IAAI;MACjB/B,IAAI,EAAJA;KACD;;EAIH,IAAIT,WAAW,EAAE,CAACoC,SAAS,EAAE;IAC3B,IAAMC,QAAO,GAAG,IAAIvC,GAAG,CAACE,WAAW,EAAE,CAACyC,SAAS,CAAC;IAChDJ,QAAO,CAACE,GAAG,CAAC,UAAU,EAAElC,IAAI,CAAC2B,kBAAkB,GAAGJ,MAAM,CAAC;IACzD,OAAO;MAAET,GAAG,EAAEkB,QAAO,CAACG,IAAI;MAAE/B,IAAI,EAAJA;IAAI,CAAE;;EAIpC,OAAO;IACLU,GAAG,EAAE,6CAA6CQ,kBAAkB,CAAClB,IAAI,CAAC,EAAE;IAC5EA,IAAI,EAAJA;GACD;AACH;AAOA,OAAM,SAAUY,UAAUA,CAACF,GAAW;EACpC,IAAI,CAACjB,eAAe,EAAE;IACpB,OAAOiB,GAAG;;EAGZ,IAAAuB,IAAA,GAAqB,IAAI5C,GAAG,CAACqB,GAAG,CAAC;IAAzBwB,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAChB,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACnB,OAAOxB,GAAG;;EAGZ,IAAMkB,OAAO,GAAG,IAAIvC,GAAG,CAACI,eAAe,CAAC;EACxC,IAAM0C,YAAY,GAAGzB,GAAG,CAAC0B,UAAU,CAAC,GAAG,CAAC,GAAG1B,GAAG,GAAGvB,IAAI,CAAC2B,IAAI,CAACc,OAAO,CAACS,QAAQ,EAAE3B,GAAG,CAAC;EACjFkB,OAAO,CAACE,GAAG,CAAC,UAAU,EAAEK,YAAY,CAAC;EACrC,OAAOP,OAAO,CAACG,IAAI;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}