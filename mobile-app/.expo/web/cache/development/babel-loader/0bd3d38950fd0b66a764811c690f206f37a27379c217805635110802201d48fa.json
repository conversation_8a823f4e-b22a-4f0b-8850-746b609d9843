{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport Animation from \"./Animation\";\nimport SpringConfig from \"../SpringConfig\";\nimport invariant from 'fbjs/lib/invariant';\nimport { shouldUseNativeDriver } from \"../NativeAnimatedHelper\";\nimport AnimatedColor from \"../nodes/AnimatedColor\";\nvar SpringAnimation = function (_Animation) {\n  function SpringAnimation(config) {\n    var _this;\n    _classCallCheck(this, SpringAnimation);\n    var _config$overshootClam, _config$restDisplacem, _config$restSpeedThre, _config$velocity, _config$velocity2, _config$delay, _config$isInteraction, _config$iterations;\n    _this = _callSuper(this, SpringAnimation);\n    _this._overshootClamping = (_config$overshootClam = config.overshootClamping) !== null && _config$overshootClam !== void 0 ? _config$overshootClam : false;\n    _this._restDisplacementThreshold = (_config$restDisplacem = config.restDisplacementThreshold) !== null && _config$restDisplacem !== void 0 ? _config$restDisplacem : 0.001;\n    _this._restSpeedThreshold = (_config$restSpeedThre = config.restSpeedThreshold) !== null && _config$restSpeedThre !== void 0 ? _config$restSpeedThre : 0.001;\n    _this._initialVelocity = (_config$velocity = config.velocity) !== null && _config$velocity !== void 0 ? _config$velocity : 0;\n    _this._lastVelocity = (_config$velocity2 = config.velocity) !== null && _config$velocity2 !== void 0 ? _config$velocity2 : 0;\n    _this._toValue = config.toValue;\n    _this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    _this._useNativeDriver = shouldUseNativeDriver(config);\n    _this._platformConfig = config.platformConfig;\n    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;\n    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    if (config.stiffness !== undefined || config.damping !== undefined || config.mass !== undefined) {\n      var _config$stiffness, _config$damping, _config$mass;\n      invariant(config.bounciness === undefined && config.speed === undefined && config.tension === undefined && config.friction === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      _this._stiffness = (_config$stiffness = config.stiffness) !== null && _config$stiffness !== void 0 ? _config$stiffness : 100;\n      _this._damping = (_config$damping = config.damping) !== null && _config$damping !== void 0 ? _config$damping : 10;\n      _this._mass = (_config$mass = config.mass) !== null && _config$mass !== void 0 ? _config$mass : 1;\n    } else if (config.bounciness !== undefined || config.speed !== undefined) {\n      var _config$bounciness, _config$speed;\n      invariant(config.tension === undefined && config.friction === undefined && config.stiffness === undefined && config.damping === undefined && config.mass === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      var springConfig = SpringConfig.fromBouncinessAndSpeed((_config$bounciness = config.bounciness) !== null && _config$bounciness !== void 0 ? _config$bounciness : 8, (_config$speed = config.speed) !== null && _config$speed !== void 0 ? _config$speed : 12);\n      _this._stiffness = springConfig.stiffness;\n      _this._damping = springConfig.damping;\n      _this._mass = 1;\n    } else {\n      var _config$tension, _config$friction;\n      var _springConfig = SpringConfig.fromOrigamiTensionAndFriction((_config$tension = config.tension) !== null && _config$tension !== void 0 ? _config$tension : 40, (_config$friction = config.friction) !== null && _config$friction !== void 0 ? _config$friction : 7);\n      _this._stiffness = _springConfig.stiffness;\n      _this._damping = _springConfig.damping;\n      _this._mass = 1;\n    }\n    invariant(_this._stiffness > 0, 'Stiffness value must be greater than 0');\n    invariant(_this._damping > 0, 'Damping value must be greater than 0');\n    invariant(_this._mass > 0, 'Mass value must be greater than 0');\n    return _this;\n  }\n  _inherits(SpringAnimation, _Animation);\n  return _createClass(SpringAnimation, [{\n    key: \"__getNativeAnimationConfig\",\n    value: function __getNativeAnimationConfig() {\n      var _this$_initialVelocit;\n      return {\n        type: 'spring',\n        overshootClamping: this._overshootClamping,\n        restDisplacementThreshold: this._restDisplacementThreshold,\n        restSpeedThreshold: this._restSpeedThreshold,\n        stiffness: this._stiffness,\n        damping: this._damping,\n        mass: this._mass,\n        initialVelocity: (_this$_initialVelocit = this._initialVelocity) !== null && _this$_initialVelocit !== void 0 ? _this$_initialVelocit : this._lastVelocity,\n        toValue: this._toValue,\n        iterations: this.__iterations,\n        platformConfig: this._platformConfig\n      };\n    }\n  }, {\n    key: \"start\",\n    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n      var _this2 = this;\n      this.__active = true;\n      this._startPosition = fromValue;\n      this._lastPosition = this._startPosition;\n      this._onUpdate = onUpdate;\n      this.__onEnd = onEnd;\n      this._lastTime = Date.now();\n      this._frameTime = 0.0;\n      if (previousAnimation instanceof SpringAnimation) {\n        var internalState = previousAnimation.getInternalState();\n        this._lastPosition = internalState.lastPosition;\n        this._lastVelocity = internalState.lastVelocity;\n        this._initialVelocity = this._lastVelocity;\n        this._lastTime = internalState.lastTime;\n      }\n      var start = function start() {\n        if (_this2._useNativeDriver) {\n          _this2.__startNativeAnimation(animatedValue);\n        } else {\n          _this2.onUpdate();\n        }\n      };\n      if (this._delay) {\n        this._timeout = setTimeout(start, this._delay);\n      } else {\n        start();\n      }\n    }\n  }, {\n    key: \"getInternalState\",\n    value: function getInternalState() {\n      return {\n        lastPosition: this._lastPosition,\n        lastVelocity: this._lastVelocity,\n        lastTime: this._lastTime\n      };\n    }\n  }, {\n    key: \"onUpdate\",\n    value: function onUpdate() {\n      var MAX_STEPS = 64;\n      var now = Date.now();\n      if (now > this._lastTime + MAX_STEPS) {\n        now = this._lastTime + MAX_STEPS;\n      }\n      var deltaTime = (now - this._lastTime) / 1000;\n      this._frameTime += deltaTime;\n      var c = this._damping;\n      var m = this._mass;\n      var k = this._stiffness;\n      var v0 = -this._initialVelocity;\n      var zeta = c / (2 * Math.sqrt(k * m));\n      var omega0 = Math.sqrt(k / m);\n      var omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta);\n      var x0 = this._toValue - this._startPosition;\n      var position = 0.0;\n      var velocity = 0.0;\n      var t = this._frameTime;\n      if (zeta < 1) {\n        var envelope = Math.exp(-zeta * omega0 * t);\n        position = this._toValue - envelope * ((v0 + zeta * omega0 * x0) / omega1 * Math.sin(omega1 * t) + x0 * Math.cos(omega1 * t));\n        velocity = zeta * omega0 * envelope * (Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0) / omega1 + x0 * Math.cos(omega1 * t)) - envelope * (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) - omega1 * x0 * Math.sin(omega1 * t));\n      } else {\n        var _envelope = Math.exp(-omega0 * t);\n        position = this._toValue - _envelope * (x0 + (v0 + omega0 * x0) * t);\n        velocity = _envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));\n      }\n      this._lastTime = now;\n      this._lastPosition = position;\n      this._lastVelocity = velocity;\n      this._onUpdate(position);\n      if (!this.__active) {\n        return;\n      }\n      var isOvershooting = false;\n      if (this._overshootClamping && this._stiffness !== 0) {\n        if (this._startPosition < this._toValue) {\n          isOvershooting = position > this._toValue;\n        } else {\n          isOvershooting = position < this._toValue;\n        }\n      }\n      var isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;\n      var isDisplacement = true;\n      if (this._stiffness !== 0) {\n        isDisplacement = Math.abs(this._toValue - position) <= this._restDisplacementThreshold;\n      }\n      if (isOvershooting || isVelocity && isDisplacement) {\n        if (this._stiffness !== 0) {\n          this._lastPosition = this._toValue;\n          this._lastVelocity = 0;\n          this._onUpdate(this._toValue);\n        }\n        this.__debouncedOnEnd({\n          finished: true\n        });\n        return;\n      }\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      _superPropGet(SpringAnimation, \"stop\", this, 3)([]);\n      this.__active = false;\n      clearTimeout(this._timeout);\n      global.cancelAnimationFrame(this._animationFrame);\n      this.__debouncedOnEnd({\n        finished: false\n      });\n    }\n  }]);\n}(Animation);\nexport default SpringAnimation;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "Animation", "SpringConfig", "invariant", "shouldUseNativeDriver", "AnimatedColor", "SpringAnimation", "_Animation", "config", "_this", "_config$overshootClam", "_config$restDisplacem", "_config$restSpeedThre", "_config$velocity", "_config$velocity2", "_config$delay", "_config$isInteraction", "_config$iterations", "_overshootClamping", "overshootClamping", "_restDisplacementThreshold", "restDisplacementThreshold", "_restSpeedThreshold", "restSpeedThreshold", "_initialVelocity", "velocity", "_lastVelocity", "_toValue", "toValue", "_delay", "delay", "_useNativeDriver", "_platformConfig", "platformConfig", "__isInteraction", "isInteraction", "__iterations", "iterations", "stiffness", "undefined", "damping", "mass", "_config$stiffness", "_config$damping", "_config$mass", "bounciness", "speed", "tension", "friction", "_stiffness", "_damping", "_mass", "_config$bounciness", "_config$speed", "springConfig", "fromBouncinessAndSpeed", "_config$tension", "_config$friction", "_springConfig", "fromOrigamiTensionAndFriction", "key", "value", "__getNativeAnimationConfig", "_this$_initialVelocit", "type", "initialVelocity", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "__active", "_startPosition", "_lastPosition", "_onUpdate", "__onEnd", "_lastTime", "Date", "now", "_frameTime", "internalState", "getInternalState", "lastPosition", "lastVelocity", "lastTime", "__startNativeAnimation", "_timeout", "setTimeout", "MAX_STEPS", "deltaTime", "c", "m", "k", "v0", "zeta", "Math", "sqrt", "omega0", "omega1", "x0", "position", "envelope", "exp", "sin", "cos", "_envelope", "isOvershooting", "isVelocity", "abs", "isDisplacement", "__debouncedOnEnd", "finished", "_animationFrame", "requestAnimationFrame", "bind", "stop", "clearTimeout", "global", "cancelAnimationFrame"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/vendor/react-native/Animated/animations/SpringAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport Animation from './Animation';\nimport SpringConfig from '../SpringConfig';\nimport invariant from 'fbjs/lib/invariant';\nimport { shouldUseNativeDriver } from '../NativeAnimatedHelper';\nimport AnimatedColor from '../nodes/AnimatedColor';\nclass SpringAnimation extends Animation {\n  constructor(config) {\n    var _config$overshootClam, _config$restDisplacem, _config$restSpeedThre, _config$velocity, _config$velocity2, _config$delay, _config$isInteraction, _config$iterations;\n    super();\n    this._overshootClamping = (_config$overshootClam = config.overshootClamping) !== null && _config$overshootClam !== void 0 ? _config$overshootClam : false;\n    this._restDisplacementThreshold = (_config$restDisplacem = config.restDisplacementThreshold) !== null && _config$restDisplacem !== void 0 ? _config$restDisplacem : 0.001;\n    this._restSpeedThreshold = (_config$restSpeedThre = config.restSpeedThreshold) !== null && _config$restSpeedThre !== void 0 ? _config$restSpeedThre : 0.001;\n    this._initialVelocity = (_config$velocity = config.velocity) !== null && _config$velocity !== void 0 ? _config$velocity : 0;\n    this._lastVelocity = (_config$velocity2 = config.velocity) !== null && _config$velocity2 !== void 0 ? _config$velocity2 : 0;\n    this._toValue = config.toValue;\n    this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    this._useNativeDriver = shouldUseNativeDriver(config);\n    this._platformConfig = config.platformConfig;\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    if (config.stiffness !== undefined || config.damping !== undefined || config.mass !== undefined) {\n      var _config$stiffness, _config$damping, _config$mass;\n      invariant(config.bounciness === undefined && config.speed === undefined && config.tension === undefined && config.friction === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      this._stiffness = (_config$stiffness = config.stiffness) !== null && _config$stiffness !== void 0 ? _config$stiffness : 100;\n      this._damping = (_config$damping = config.damping) !== null && _config$damping !== void 0 ? _config$damping : 10;\n      this._mass = (_config$mass = config.mass) !== null && _config$mass !== void 0 ? _config$mass : 1;\n    } else if (config.bounciness !== undefined || config.speed !== undefined) {\n      var _config$bounciness, _config$speed;\n      // Convert the origami bounciness/speed values to stiffness/damping\n      // We assume mass is 1.\n      invariant(config.tension === undefined && config.friction === undefined && config.stiffness === undefined && config.damping === undefined && config.mass === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      var springConfig = SpringConfig.fromBouncinessAndSpeed((_config$bounciness = config.bounciness) !== null && _config$bounciness !== void 0 ? _config$bounciness : 8, (_config$speed = config.speed) !== null && _config$speed !== void 0 ? _config$speed : 12);\n      this._stiffness = springConfig.stiffness;\n      this._damping = springConfig.damping;\n      this._mass = 1;\n    } else {\n      var _config$tension, _config$friction;\n      // Convert the origami tension/friction values to stiffness/damping\n      // We assume mass is 1.\n      var _springConfig = SpringConfig.fromOrigamiTensionAndFriction((_config$tension = config.tension) !== null && _config$tension !== void 0 ? _config$tension : 40, (_config$friction = config.friction) !== null && _config$friction !== void 0 ? _config$friction : 7);\n      this._stiffness = _springConfig.stiffness;\n      this._damping = _springConfig.damping;\n      this._mass = 1;\n    }\n    invariant(this._stiffness > 0, 'Stiffness value must be greater than 0');\n    invariant(this._damping > 0, 'Damping value must be greater than 0');\n    invariant(this._mass > 0, 'Mass value must be greater than 0');\n  }\n  __getNativeAnimationConfig() {\n    var _this$_initialVelocit;\n    return {\n      type: 'spring',\n      overshootClamping: this._overshootClamping,\n      restDisplacementThreshold: this._restDisplacementThreshold,\n      restSpeedThreshold: this._restSpeedThreshold,\n      stiffness: this._stiffness,\n      damping: this._damping,\n      mass: this._mass,\n      initialVelocity: (_this$_initialVelocit = this._initialVelocity) !== null && _this$_initialVelocit !== void 0 ? _this$_initialVelocit : this._lastVelocity,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._startPosition = fromValue;\n    this._lastPosition = this._startPosition;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    this._lastTime = Date.now();\n    this._frameTime = 0.0;\n    if (previousAnimation instanceof SpringAnimation) {\n      var internalState = previousAnimation.getInternalState();\n      this._lastPosition = internalState.lastPosition;\n      this._lastVelocity = internalState.lastVelocity;\n      // Set the initial velocity to the last velocity\n      this._initialVelocity = this._lastVelocity;\n      this._lastTime = internalState.lastTime;\n    }\n    var start = () => {\n      if (this._useNativeDriver) {\n        this.__startNativeAnimation(animatedValue);\n      } else {\n        this.onUpdate();\n      }\n    };\n\n    //  If this._delay is more than 0, we start after the timeout.\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n  getInternalState() {\n    return {\n      lastPosition: this._lastPosition,\n      lastVelocity: this._lastVelocity,\n      lastTime: this._lastTime\n    };\n  }\n\n  /**\n   * This spring model is based off of a damped harmonic oscillator\n   * (https://en.wikipedia.org/wiki/Harmonic_oscillator#Damped_harmonic_oscillator).\n   *\n   * We use the closed form of the second order differential equation:\n   *\n   * x'' + (2ζ⍵_0)x' + ⍵^2x = 0\n   *\n   * where\n   *    ⍵_0 = √(k / m) (undamped angular frequency of the oscillator),\n   *    ζ = c / 2√mk (damping ratio),\n   *    c = damping constant\n   *    k = stiffness\n   *    m = mass\n   *\n   * The derivation of the closed form is described in detail here:\n   * http://planetmath.org/sites/default/files/texpdf/39745.pdf\n   *\n   * This algorithm happens to match the algorithm used by CASpringAnimation,\n   * a QuartzCore (iOS) API that creates spring animations.\n   */\n  onUpdate() {\n    // If for some reason we lost a lot of frames (e.g. process large payload or\n    // stopped in the debugger), we only advance by 4 frames worth of\n    // computation and will continue on the next frame. It's better to have it\n    // running at faster speed than jumping to the end.\n    var MAX_STEPS = 64;\n    var now = Date.now();\n    if (now > this._lastTime + MAX_STEPS) {\n      now = this._lastTime + MAX_STEPS;\n    }\n    var deltaTime = (now - this._lastTime) / 1000;\n    this._frameTime += deltaTime;\n    var c = this._damping;\n    var m = this._mass;\n    var k = this._stiffness;\n    var v0 = -this._initialVelocity;\n    var zeta = c / (2 * Math.sqrt(k * m)); // damping ratio\n    var omega0 = Math.sqrt(k / m); // undamped angular frequency of the oscillator (rad/ms)\n    var omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta); // exponential decay\n    var x0 = this._toValue - this._startPosition; // calculate the oscillation from x0 = 1 to x = 0\n\n    var position = 0.0;\n    var velocity = 0.0;\n    var t = this._frameTime;\n    if (zeta < 1) {\n      // Under damped\n      var envelope = Math.exp(-zeta * omega0 * t);\n      position = this._toValue - envelope * ((v0 + zeta * omega0 * x0) / omega1 * Math.sin(omega1 * t) + x0 * Math.cos(omega1 * t));\n      // This looks crazy -- it's actually just the derivative of the\n      // oscillation function\n      velocity = zeta * omega0 * envelope * (Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0) / omega1 + x0 * Math.cos(omega1 * t)) - envelope * (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) - omega1 * x0 * Math.sin(omega1 * t));\n    } else {\n      // Critically damped\n      var _envelope = Math.exp(-omega0 * t);\n      position = this._toValue - _envelope * (x0 + (v0 + omega0 * x0) * t);\n      velocity = _envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));\n    }\n    this._lastTime = now;\n    this._lastPosition = position;\n    this._lastVelocity = velocity;\n    this._onUpdate(position);\n    if (!this.__active) {\n      // a listener might have stopped us in _onUpdate\n      return;\n    }\n\n    // Conditions for stopping the spring animation\n    var isOvershooting = false;\n    if (this._overshootClamping && this._stiffness !== 0) {\n      if (this._startPosition < this._toValue) {\n        isOvershooting = position > this._toValue;\n      } else {\n        isOvershooting = position < this._toValue;\n      }\n    }\n    var isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;\n    var isDisplacement = true;\n    if (this._stiffness !== 0) {\n      isDisplacement = Math.abs(this._toValue - position) <= this._restDisplacementThreshold;\n    }\n    if (isOvershooting || isVelocity && isDisplacement) {\n      if (this._stiffness !== 0) {\n        // Ensure that we end up with a round value\n        this._lastPosition = this._toValue;\n        this._lastVelocity = 0;\n        this._onUpdate(this._toValue);\n      }\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n    this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    clearTimeout(this._timeout);\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nexport default SpringAnimation;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,SAAS;AAChB,OAAOC,YAAY;AACnB,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,qBAAqB;AAC9B,OAAOC,aAAa;AAA+B,IAC7CC,eAAe,aAAAC,UAAA;EACnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA9B,eAAA,OAAA2B,eAAA;IAClB,IAAII,qBAAqB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB;IACtKR,KAAA,GAAAxB,UAAA,OAAAqB,eAAA;IACAG,KAAA,CAAKS,kBAAkB,GAAG,CAACR,qBAAqB,GAAGF,MAAM,CAACW,iBAAiB,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IACzJD,KAAA,CAAKW,0BAA0B,GAAG,CAACT,qBAAqB,GAAGH,MAAM,CAACa,yBAAyB,MAAM,IAAI,IAAIV,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IACzKF,KAAA,CAAKa,mBAAmB,GAAG,CAACV,qBAAqB,GAAGJ,MAAM,CAACe,kBAAkB,MAAM,IAAI,IAAIX,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IAC3JH,KAAA,CAAKe,gBAAgB,GAAG,CAACX,gBAAgB,GAAGL,MAAM,CAACiB,QAAQ,MAAM,IAAI,IAAIZ,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,CAAC;IAC3HJ,KAAA,CAAKiB,aAAa,GAAG,CAACZ,iBAAiB,GAAGN,MAAM,CAACiB,QAAQ,MAAM,IAAI,IAAIX,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,CAAC;IAC3HL,KAAA,CAAKkB,QAAQ,GAAGnB,MAAM,CAACoB,OAAO;IAC9BnB,KAAA,CAAKoB,MAAM,GAAG,CAACd,aAAa,GAAGP,MAAM,CAACsB,KAAK,MAAM,IAAI,IAAIf,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;IACrGN,KAAA,CAAKsB,gBAAgB,GAAG3B,qBAAqB,CAACI,MAAM,CAAC;IACrDC,KAAA,CAAKuB,eAAe,GAAGxB,MAAM,CAACyB,cAAc;IAC5CxB,KAAA,CAAKyB,eAAe,GAAG,CAAClB,qBAAqB,GAAGR,MAAM,CAAC2B,aAAa,MAAM,IAAI,IAAInB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACP,KAAA,CAAKsB,gBAAgB;IACnKtB,KAAA,CAAK2B,YAAY,GAAG,CAACnB,kBAAkB,GAAGT,MAAM,CAAC6B,UAAU,MAAM,IAAI,IAAIpB,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC/H,IAAIT,MAAM,CAAC8B,SAAS,KAAKC,SAAS,IAAI/B,MAAM,CAACgC,OAAO,KAAKD,SAAS,IAAI/B,MAAM,CAACiC,IAAI,KAAKF,SAAS,EAAE;MAC/F,IAAIG,iBAAiB,EAAEC,eAAe,EAAEC,YAAY;MACpDzC,SAAS,CAACK,MAAM,CAACqC,UAAU,KAAKN,SAAS,IAAI/B,MAAM,CAACsC,KAAK,KAAKP,SAAS,IAAI/B,MAAM,CAACuC,OAAO,KAAKR,SAAS,IAAI/B,MAAM,CAACwC,QAAQ,KAAKT,SAAS,EAAE,4GAA4G,CAAC;MACvP9B,KAAA,CAAKwC,UAAU,GAAG,CAACP,iBAAiB,GAAGlC,MAAM,CAAC8B,SAAS,MAAM,IAAI,IAAII,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,GAAG;MAC3HjC,KAAA,CAAKyC,QAAQ,GAAG,CAACP,eAAe,GAAGnC,MAAM,CAACgC,OAAO,MAAM,IAAI,IAAIG,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,EAAE;MAChHlC,KAAA,CAAK0C,KAAK,GAAG,CAACP,YAAY,GAAGpC,MAAM,CAACiC,IAAI,MAAM,IAAI,IAAIG,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC;IAClG,CAAC,MAAM,IAAIpC,MAAM,CAACqC,UAAU,KAAKN,SAAS,IAAI/B,MAAM,CAACsC,KAAK,KAAKP,SAAS,EAAE;MACxE,IAAIa,kBAAkB,EAAEC,aAAa;MAGrClD,SAAS,CAACK,MAAM,CAACuC,OAAO,KAAKR,SAAS,IAAI/B,MAAM,CAACwC,QAAQ,KAAKT,SAAS,IAAI/B,MAAM,CAAC8B,SAAS,KAAKC,SAAS,IAAI/B,MAAM,CAACgC,OAAO,KAAKD,SAAS,IAAI/B,MAAM,CAACiC,IAAI,KAAKF,SAAS,EAAE,4GAA4G,CAAC;MACrR,IAAIe,YAAY,GAAGpD,YAAY,CAACqD,sBAAsB,CAAC,CAACH,kBAAkB,GAAG5C,MAAM,CAACqC,UAAU,MAAM,IAAI,IAAIO,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC,EAAE,CAACC,aAAa,GAAG7C,MAAM,CAACsC,KAAK,MAAM,IAAI,IAAIO,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,EAAE,CAAC;MAC7P5C,KAAA,CAAKwC,UAAU,GAAGK,YAAY,CAAChB,SAAS;MACxC7B,KAAA,CAAKyC,QAAQ,GAAGI,YAAY,CAACd,OAAO;MACpC/B,KAAA,CAAK0C,KAAK,GAAG,CAAC;IAChB,CAAC,MAAM;MACL,IAAIK,eAAe,EAAEC,gBAAgB;MAGrC,IAAIC,aAAa,GAAGxD,YAAY,CAACyD,6BAA6B,CAAC,CAACH,eAAe,GAAGhD,MAAM,CAACuC,OAAO,MAAM,IAAI,IAAIS,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,EAAE,EAAE,CAACC,gBAAgB,GAAGjD,MAAM,CAACwC,QAAQ,MAAM,IAAI,IAAIS,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,CAAC,CAAC;MACrQhD,KAAA,CAAKwC,UAAU,GAAGS,aAAa,CAACpB,SAAS;MACzC7B,KAAA,CAAKyC,QAAQ,GAAGQ,aAAa,CAAClB,OAAO;MACrC/B,KAAA,CAAK0C,KAAK,GAAG,CAAC;IAChB;IACAhD,SAAS,CAACM,KAAA,CAAKwC,UAAU,GAAG,CAAC,EAAE,wCAAwC,CAAC;IACxE9C,SAAS,CAACM,KAAA,CAAKyC,QAAQ,GAAG,CAAC,EAAE,sCAAsC,CAAC;IACpE/C,SAAS,CAACM,KAAA,CAAK0C,KAAK,GAAG,CAAC,EAAE,mCAAmC,CAAC;IAAC,OAAA1C,KAAA;EACjE;EAACzB,SAAA,CAAAsB,eAAA,EAAAC,UAAA;EAAA,OAAA3B,YAAA,CAAA0B,eAAA;IAAAsD,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,IAAIC,qBAAqB;MACzB,OAAO;QACLC,IAAI,EAAE,QAAQ;QACd7C,iBAAiB,EAAE,IAAI,CAACD,kBAAkB;QAC1CG,yBAAyB,EAAE,IAAI,CAACD,0BAA0B;QAC1DG,kBAAkB,EAAE,IAAI,CAACD,mBAAmB;QAC5CgB,SAAS,EAAE,IAAI,CAACW,UAAU;QAC1BT,OAAO,EAAE,IAAI,CAACU,QAAQ;QACtBT,IAAI,EAAE,IAAI,CAACU,KAAK;QAChBc,eAAe,EAAE,CAACF,qBAAqB,GAAG,IAAI,CAACvC,gBAAgB,MAAM,IAAI,IAAIuC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,IAAI,CAACrC,aAAa;QAC1JE,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBU,UAAU,EAAE,IAAI,CAACD,YAAY;QAC7BH,cAAc,EAAE,IAAI,CAACD;MACvB,CAAC;IACH;EAAC;IAAA4B,GAAA;IAAAC,KAAA,EACD,SAAAK,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAAA,IAAAC,MAAA;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,cAAc,GAAGP,SAAS;MAC/B,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACD,cAAc;MACxC,IAAI,CAACE,SAAS,GAAGR,QAAQ;MACzB,IAAI,CAACS,OAAO,GAAGR,KAAK;MACpB,IAAI,CAACS,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3B,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAIX,iBAAiB,YAAYhE,eAAe,EAAE;QAChD,IAAI4E,aAAa,GAAGZ,iBAAiB,CAACa,gBAAgB,CAAC,CAAC;QACxD,IAAI,CAACR,aAAa,GAAGO,aAAa,CAACE,YAAY;QAC/C,IAAI,CAAC1D,aAAa,GAAGwD,aAAa,CAACG,YAAY;QAE/C,IAAI,CAAC7D,gBAAgB,GAAG,IAAI,CAACE,aAAa;QAC1C,IAAI,CAACoD,SAAS,GAAGI,aAAa,CAACI,QAAQ;MACzC;MACA,IAAIpB,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAChB,IAAIM,MAAI,CAACzC,gBAAgB,EAAE;UACzByC,MAAI,CAACe,sBAAsB,CAAChB,aAAa,CAAC;QAC5C,CAAC,MAAM;UACLC,MAAI,CAACJ,QAAQ,CAAC,CAAC;QACjB;MACF,CAAC;MAGD,IAAI,IAAI,CAACvC,MAAM,EAAE;QACf,IAAI,CAAC2D,QAAQ,GAAGC,UAAU,CAACvB,KAAK,EAAE,IAAI,CAACrC,MAAM,CAAC;MAChD,CAAC,MAAM;QACLqC,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAsB,gBAAgBA,CAAA,EAAG;MACjB,OAAO;QACLC,YAAY,EAAE,IAAI,CAACT,aAAa;QAChCU,YAAY,EAAE,IAAI,CAAC3D,aAAa;QAChC4D,QAAQ,EAAE,IAAI,CAACR;MACjB,CAAC;IACH;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAuBD,SAAAO,QAAQA,CAAA,EAAG;MAKT,IAAIsB,SAAS,GAAG,EAAE;MAClB,IAAIV,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIA,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGY,SAAS,EAAE;QACpCV,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGY,SAAS;MAClC;MACA,IAAIC,SAAS,GAAG,CAACX,GAAG,GAAG,IAAI,CAACF,SAAS,IAAI,IAAI;MAC7C,IAAI,CAACG,UAAU,IAAIU,SAAS;MAC5B,IAAIC,CAAC,GAAG,IAAI,CAAC1C,QAAQ;MACrB,IAAI2C,CAAC,GAAG,IAAI,CAAC1C,KAAK;MAClB,IAAI2C,CAAC,GAAG,IAAI,CAAC7C,UAAU;MACvB,IAAI8C,EAAE,GAAG,CAAC,IAAI,CAACvE,gBAAgB;MAC/B,IAAIwE,IAAI,GAAGJ,CAAC,IAAI,CAAC,GAAGK,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC,CAAC;MACrC,IAAIM,MAAM,GAAGF,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC;MAC7B,IAAIO,MAAM,GAAGD,MAAM,GAAGF,IAAI,CAACC,IAAI,CAAC,GAAG,GAAGF,IAAI,GAAGA,IAAI,CAAC;MAClD,IAAIK,EAAE,GAAG,IAAI,CAAC1E,QAAQ,GAAG,IAAI,CAAC+C,cAAc;MAE5C,IAAI4B,QAAQ,GAAG,GAAG;MAClB,IAAI7E,QAAQ,GAAG,GAAG;MAClB,IAAIvC,CAAC,GAAG,IAAI,CAAC+F,UAAU;MACvB,IAAIe,IAAI,GAAG,CAAC,EAAE;QAEZ,IAAIO,QAAQ,GAAGN,IAAI,CAACO,GAAG,CAAC,CAACR,IAAI,GAAGG,MAAM,GAAGjH,CAAC,CAAC;QAC3CoH,QAAQ,GAAG,IAAI,CAAC3E,QAAQ,GAAG4E,QAAQ,IAAI,CAACR,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,IAAID,MAAM,GAAGH,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGlH,CAAC,CAAC,GAAGmH,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGlH,CAAC,CAAC,CAAC;QAG7HuC,QAAQ,GAAGuE,IAAI,GAAGG,MAAM,GAAGI,QAAQ,IAAIN,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGlH,CAAC,CAAC,IAAI6G,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAAGD,MAAM,GAAGC,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGlH,CAAC,CAAC,CAAC,GAAGqH,QAAQ,IAAIN,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGlH,CAAC,CAAC,IAAI6G,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAAGD,MAAM,GAAGC,EAAE,GAAGJ,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGlH,CAAC,CAAC,CAAC;MACpO,CAAC,MAAM;QAEL,IAAIyH,SAAS,GAAGV,IAAI,CAACO,GAAG,CAAC,CAACL,MAAM,GAAGjH,CAAC,CAAC;QACrCoH,QAAQ,GAAG,IAAI,CAAC3E,QAAQ,GAAGgF,SAAS,IAAIN,EAAE,GAAG,CAACN,EAAE,GAAGI,MAAM,GAAGE,EAAE,IAAInH,CAAC,CAAC;QACpEuC,QAAQ,GAAGkF,SAAS,IAAIZ,EAAE,IAAI7G,CAAC,GAAGiH,MAAM,GAAG,CAAC,CAAC,GAAGjH,CAAC,GAAGmH,EAAE,IAAIF,MAAM,GAAGA,MAAM,CAAC,CAAC;MAC7E;MACA,IAAI,CAACrB,SAAS,GAAGE,GAAG;MACpB,IAAI,CAACL,aAAa,GAAG2B,QAAQ;MAC7B,IAAI,CAAC5E,aAAa,GAAGD,QAAQ;MAC7B,IAAI,CAACmD,SAAS,CAAC0B,QAAQ,CAAC;MACxB,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE;QAElB;MACF;MAGA,IAAImC,cAAc,GAAG,KAAK;MAC1B,IAAI,IAAI,CAAC1F,kBAAkB,IAAI,IAAI,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACpD,IAAI,IAAI,CAACyB,cAAc,GAAG,IAAI,CAAC/C,QAAQ,EAAE;UACvCiF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC3E,QAAQ;QAC3C,CAAC,MAAM;UACLiF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC3E,QAAQ;QAC3C;MACF;MACA,IAAIkF,UAAU,GAAGZ,IAAI,CAACa,GAAG,CAACrF,QAAQ,CAAC,IAAI,IAAI,CAACH,mBAAmB;MAC/D,IAAIyF,cAAc,GAAG,IAAI;MACzB,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;QACzB8D,cAAc,GAAGd,IAAI,CAACa,GAAG,CAAC,IAAI,CAACnF,QAAQ,GAAG2E,QAAQ,CAAC,IAAI,IAAI,CAAClF,0BAA0B;MACxF;MACA,IAAIwF,cAAc,IAAIC,UAAU,IAAIE,cAAc,EAAE;QAClD,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;UAEzB,IAAI,CAAC0B,aAAa,GAAG,IAAI,CAAChD,QAAQ;UAClC,IAAI,CAACD,aAAa,GAAG,CAAC;UACtB,IAAI,CAACkD,SAAS,CAAC,IAAI,CAACjD,QAAQ,CAAC;QAC/B;QACA,IAAI,CAACqF,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE;EAAC;IAAAxD,GAAA;IAAAC,KAAA,EACD,SAAAwD,IAAIA,CAAA,EAAG;MACLvH,aAAA,CAAAQ,eAAA;MACA,IAAI,CAACmE,QAAQ,GAAG,KAAK;MACrB6C,YAAY,CAAC,IAAI,CAAC9B,QAAQ,CAAC;MAC3B+B,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACN,eAAe,CAAC;MACjD,IAAI,CAACF,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EA1M2BhH,SAAS;AA4MvC,eAAeK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}