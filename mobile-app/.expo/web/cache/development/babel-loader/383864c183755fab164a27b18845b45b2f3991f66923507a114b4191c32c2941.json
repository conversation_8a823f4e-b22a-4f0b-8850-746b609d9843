{"ast": null, "code": "var slice = Array.prototype.slice;\nexport default function createOrderedCSSStyleSheet(sheet) {\n  var groups = {};\n  var selectors = {};\n  if (sheet != null) {\n    var group;\n    slice.call(sheet.cssRules).forEach(function (cssRule, i) {\n      var cssText = cssRule.cssText;\n      if (cssText.indexOf('stylesheet-group') > -1) {\n        group = decodeGroupRule(cssRule);\n        groups[group] = {\n          start: i,\n          rules: [cssText]\n        };\n      } else {\n        var selectorText = getSelectorText(cssText);\n        if (selectorText != null) {\n          selectors[selectorText] = true;\n          groups[group].rules.push(cssText);\n        }\n      }\n    });\n  }\n  function sheetInsert(sheet, group, text) {\n    var orderedGroups = getOrderedGroups(groups);\n    var groupIndex = orderedGroups.indexOf(group);\n    var nextGroupIndex = groupIndex + 1;\n    var nextGroup = orderedGroups[nextGroupIndex];\n    var position = nextGroup != null && groups[nextGroup].start != null ? groups[nextGroup].start : sheet.cssRules.length;\n    var isInserted = insertRuleAt(sheet, text, position);\n    if (isInserted) {\n      if (groups[group].start == null) {\n        groups[group].start = position;\n      }\n      for (var i = nextGroupIndex; i < orderedGroups.length; i += 1) {\n        var groupNumber = orderedGroups[i];\n        var previousStart = groups[groupNumber].start || 0;\n        groups[groupNumber].start = previousStart + 1;\n      }\n    }\n    return isInserted;\n  }\n  var OrderedCSSStyleSheet = {\n    getTextContent: function getTextContent() {\n      return getOrderedGroups(groups).map(function (group) {\n        var rules = groups[group].rules;\n        var marker = rules.shift();\n        rules.sort();\n        rules.unshift(marker);\n        return rules.join('\\n');\n      }).join('\\n');\n    },\n    insert: function insert(cssText, groupValue) {\n      var group = Number(groupValue);\n      if (groups[group] == null) {\n        var markerRule = encodeGroupRule(group);\n        groups[group] = {\n          start: null,\n          rules: [markerRule]\n        };\n        if (sheet != null) {\n          sheetInsert(sheet, group, markerRule);\n        }\n      }\n      var selectorText = getSelectorText(cssText);\n      if (selectorText != null && selectors[selectorText] == null) {\n        selectors[selectorText] = true;\n        groups[group].rules.push(cssText);\n        if (sheet != null) {\n          var isInserted = sheetInsert(sheet, group, cssText);\n          if (!isInserted) {\n            groups[group].rules.pop();\n          }\n        }\n      }\n    }\n  };\n  return OrderedCSSStyleSheet;\n}\nfunction encodeGroupRule(group) {\n  return \"[stylesheet-group=\\\"\" + group + \"\\\"]{}\";\n}\nvar groupPattern = /[\"']/g;\nfunction decodeGroupRule(cssRule) {\n  return Number(cssRule.selectorText.split(groupPattern)[1]);\n}\nfunction getOrderedGroups(obj) {\n  return Object.keys(obj).map(Number).sort(function (a, b) {\n    return a > b ? 1 : -1;\n  });\n}\nvar selectorPattern = /\\s*([,])\\s*/g;\nfunction getSelectorText(cssText) {\n  var selector = cssText.split('{')[0].trim();\n  return selector !== '' ? selector.replace(selectorPattern, '$1') : null;\n}\nfunction insertRuleAt(root, cssText, position) {\n  try {\n    root.insertRule(cssText, position);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}", "map": {"version": 3, "names": ["slice", "Array", "prototype", "createOrderedCSSStyleSheet", "sheet", "groups", "selectors", "group", "call", "cssRules", "for<PERSON>ach", "cssRule", "i", "cssText", "indexOf", "decodeGroupRule", "start", "rules", "selectorText", "getSelectorText", "push", "sheetInsert", "text", "orderedGroups", "getOrderedGroups", "groupIndex", "nextGroupIndex", "nextGroup", "position", "length", "isInserted", "insertRuleAt", "groupNumber", "previousStart", "OrderedCSSStyleSheet", "getTextContent", "map", "marker", "shift", "sort", "unshift", "join", "insert", "groupValue", "Number", "markerRule", "encodeGroupRule", "pop", "groupPattern", "split", "obj", "Object", "keys", "a", "b", "selectorPattern", "selector", "trim", "replace", "root", "insertRule", "e"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/exports/StyleSheet/dom/createOrderedCSSStyleSheet.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar slice = Array.prototype.slice;\n\n/**\n * Order-based insertion of CSS.\n *\n * Each rule is associated with a numerically defined group.\n * Groups are ordered within the style sheet according to their number, with the\n * lowest first.\n *\n * Groups are implemented using marker rules. The selector of the first rule of\n * each group is used only to encode the group number for hydration. An\n * alternative implementation could rely on CSSMediaRule, allowing groups to be\n * treated as a sub-sheet, but the Edge implementation of CSSMediaRule is\n * broken.\n * https://developer.mozilla.org/en-US/docs/Web/API/CSSMediaRule\n * https://gist.github.com/necolas/aa0c37846ad6bd3b05b727b959e82674\n */\nexport default function createOrderedCSSStyleSheet(sheet) {\n  var groups = {};\n  var selectors = {};\n\n  /**\n   * Hydrate approximate record from any existing rules in the sheet.\n   */\n  if (sheet != null) {\n    var group;\n    slice.call(sheet.cssRules).forEach((cssRule, i) => {\n      var cssText = cssRule.cssText;\n      // Create record of existing selectors and rules\n      if (cssText.indexOf('stylesheet-group') > -1) {\n        group = decodeGroupRule(cssRule);\n        groups[group] = {\n          start: i,\n          rules: [cssText]\n        };\n      } else {\n        var selectorText = getSelectorText(cssText);\n        if (selectorText != null) {\n          selectors[selectorText] = true;\n          groups[group].rules.push(cssText);\n        }\n      }\n    });\n  }\n  function sheetInsert(sheet, group, text) {\n    var orderedGroups = getOrderedGroups(groups);\n    var groupIndex = orderedGroups.indexOf(group);\n    var nextGroupIndex = groupIndex + 1;\n    var nextGroup = orderedGroups[nextGroupIndex];\n    // Insert rule before the next group, or at the end of the stylesheet\n    var position = nextGroup != null && groups[nextGroup].start != null ? groups[nextGroup].start : sheet.cssRules.length;\n    var isInserted = insertRuleAt(sheet, text, position);\n    if (isInserted) {\n      // Set the starting index of the new group\n      if (groups[group].start == null) {\n        groups[group].start = position;\n      }\n      // Increment the starting index of all subsequent groups\n      for (var i = nextGroupIndex; i < orderedGroups.length; i += 1) {\n        var groupNumber = orderedGroups[i];\n        var previousStart = groups[groupNumber].start || 0;\n        groups[groupNumber].start = previousStart + 1;\n      }\n    }\n    return isInserted;\n  }\n  var OrderedCSSStyleSheet = {\n    /**\n     * The textContent of the style sheet.\n     */\n    getTextContent() {\n      return getOrderedGroups(groups).map(group => {\n        var rules = groups[group].rules;\n        // Sorting provides deterministic order of styles in group for\n        // build-time extraction of the style sheet.\n        var marker = rules.shift();\n        rules.sort();\n        rules.unshift(marker);\n        return rules.join('\\n');\n      }).join('\\n');\n    },\n    /**\n     * Insert a rule into the style sheet\n     */\n    insert(cssText, groupValue) {\n      var group = Number(groupValue);\n\n      // Create a new group.\n      if (groups[group] == null) {\n        var markerRule = encodeGroupRule(group);\n        // Create the internal record.\n        groups[group] = {\n          start: null,\n          rules: [markerRule]\n        };\n        // Update CSSOM.\n        if (sheet != null) {\n          sheetInsert(sheet, group, markerRule);\n        }\n      }\n\n      // selectorText is more reliable than cssText for insertion checks. The\n      // browser excludes vendor-prefixed properties and rewrites certain values\n      // making cssText more likely to be different from what was inserted.\n      var selectorText = getSelectorText(cssText);\n      if (selectorText != null && selectors[selectorText] == null) {\n        // Update the internal records.\n        selectors[selectorText] = true;\n        groups[group].rules.push(cssText);\n        // Update CSSOM.\n        if (sheet != null) {\n          var isInserted = sheetInsert(sheet, group, cssText);\n          if (!isInserted) {\n            // Revert internal record change if a rule was rejected (e.g.,\n            // unrecognized pseudo-selector)\n            groups[group].rules.pop();\n          }\n        }\n      }\n    }\n  };\n  return OrderedCSSStyleSheet;\n}\n\n/**\n * Helper functions\n */\n\nfunction encodeGroupRule(group) {\n  return \"[stylesheet-group=\\\"\" + group + \"\\\"]{}\";\n}\nvar groupPattern = /[\"']/g;\nfunction decodeGroupRule(cssRule) {\n  return Number(cssRule.selectorText.split(groupPattern)[1]);\n}\nfunction getOrderedGroups(obj) {\n  return Object.keys(obj).map(Number).sort((a, b) => a > b ? 1 : -1);\n}\nvar selectorPattern = /\\s*([,])\\s*/g;\nfunction getSelectorText(cssText) {\n  var selector = cssText.split('{')[0].trim();\n  return selector !== '' ? selector.replace(selectorPattern, '$1') : null;\n}\nfunction insertRuleAt(root, cssText, position) {\n  try {\n    // $FlowFixMe: Flow is missing CSSOM types needed to type 'root'.\n    root.insertRule(cssText, position);\n    return true;\n  } catch (e) {\n    // JSDOM doesn't support `CSSSMediaRule#insertRule`.\n    // Also ignore errors that occur from attempting to insert vendor-prefixed selectors.\n    return false;\n  }\n}"], "mappings": "AASA,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS,CAACF,KAAK;AAiBjC,eAAe,SAASG,0BAA0BA,CAACC,KAAK,EAAE;EACxD,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,SAAS,GAAG,CAAC,CAAC;EAKlB,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB,IAAIG,KAAK;IACTP,KAAK,CAACQ,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAACC,OAAO,CAAC,UAACC,OAAO,EAAEC,CAAC,EAAK;MACjD,IAAIC,OAAO,GAAGF,OAAO,CAACE,OAAO;MAE7B,IAAIA,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5CP,KAAK,GAAGQ,eAAe,CAACJ,OAAO,CAAC;QAChCN,MAAM,CAACE,KAAK,CAAC,GAAG;UACdS,KAAK,EAAEJ,CAAC;UACRK,KAAK,EAAE,CAACJ,OAAO;QACjB,CAAC;MACH,CAAC,MAAM;QACL,IAAIK,YAAY,GAAGC,eAAe,CAACN,OAAO,CAAC;QAC3C,IAAIK,YAAY,IAAI,IAAI,EAAE;UACxBZ,SAAS,CAACY,YAAY,CAAC,GAAG,IAAI;UAC9Bb,MAAM,CAACE,KAAK,CAAC,CAACU,KAAK,CAACG,IAAI,CAACP,OAAO,CAAC;QACnC;MACF;IACF,CAAC,CAAC;EACJ;EACA,SAASQ,WAAWA,CAACjB,KAAK,EAAEG,KAAK,EAAEe,IAAI,EAAE;IACvC,IAAIC,aAAa,GAAGC,gBAAgB,CAACnB,MAAM,CAAC;IAC5C,IAAIoB,UAAU,GAAGF,aAAa,CAACT,OAAO,CAACP,KAAK,CAAC;IAC7C,IAAImB,cAAc,GAAGD,UAAU,GAAG,CAAC;IACnC,IAAIE,SAAS,GAAGJ,aAAa,CAACG,cAAc,CAAC;IAE7C,IAAIE,QAAQ,GAAGD,SAAS,IAAI,IAAI,IAAItB,MAAM,CAACsB,SAAS,CAAC,CAACX,KAAK,IAAI,IAAI,GAAGX,MAAM,CAACsB,SAAS,CAAC,CAACX,KAAK,GAAGZ,KAAK,CAACK,QAAQ,CAACoB,MAAM;IACrH,IAAIC,UAAU,GAAGC,YAAY,CAAC3B,KAAK,EAAEkB,IAAI,EAAEM,QAAQ,CAAC;IACpD,IAAIE,UAAU,EAAE;MAEd,IAAIzB,MAAM,CAACE,KAAK,CAAC,CAACS,KAAK,IAAI,IAAI,EAAE;QAC/BX,MAAM,CAACE,KAAK,CAAC,CAACS,KAAK,GAAGY,QAAQ;MAChC;MAEA,KAAK,IAAIhB,CAAC,GAAGc,cAAc,EAAEd,CAAC,GAAGW,aAAa,CAACM,MAAM,EAAEjB,CAAC,IAAI,CAAC,EAAE;QAC7D,IAAIoB,WAAW,GAAGT,aAAa,CAACX,CAAC,CAAC;QAClC,IAAIqB,aAAa,GAAG5B,MAAM,CAAC2B,WAAW,CAAC,CAAChB,KAAK,IAAI,CAAC;QAClDX,MAAM,CAAC2B,WAAW,CAAC,CAAChB,KAAK,GAAGiB,aAAa,GAAG,CAAC;MAC/C;IACF;IACA,OAAOH,UAAU;EACnB;EACA,IAAII,oBAAoB,GAAG;IAIzBC,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,OAAOX,gBAAgB,CAACnB,MAAM,CAAC,CAAC+B,GAAG,CAAC,UAAA7B,KAAK,EAAI;QAC3C,IAAIU,KAAK,GAAGZ,MAAM,CAACE,KAAK,CAAC,CAACU,KAAK;QAG/B,IAAIoB,MAAM,GAAGpB,KAAK,CAACqB,KAAK,CAAC,CAAC;QAC1BrB,KAAK,CAACsB,IAAI,CAAC,CAAC;QACZtB,KAAK,CAACuB,OAAO,CAACH,MAAM,CAAC;QACrB,OAAOpB,KAAK,CAACwB,IAAI,CAAC,IAAI,CAAC;MACzB,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IACf,CAAC;IAIDC,MAAM,WAANA,MAAMA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;MAC1B,IAAIpC,KAAK,GAAGqC,MAAM,CAACD,UAAU,CAAC;MAG9B,IAAItC,MAAM,CAACE,KAAK,CAAC,IAAI,IAAI,EAAE;QACzB,IAAIsC,UAAU,GAAGC,eAAe,CAACvC,KAAK,CAAC;QAEvCF,MAAM,CAACE,KAAK,CAAC,GAAG;UACdS,KAAK,EAAE,IAAI;UACXC,KAAK,EAAE,CAAC4B,UAAU;QACpB,CAAC;QAED,IAAIzC,KAAK,IAAI,IAAI,EAAE;UACjBiB,WAAW,CAACjB,KAAK,EAAEG,KAAK,EAAEsC,UAAU,CAAC;QACvC;MACF;MAKA,IAAI3B,YAAY,GAAGC,eAAe,CAACN,OAAO,CAAC;MAC3C,IAAIK,YAAY,IAAI,IAAI,IAAIZ,SAAS,CAACY,YAAY,CAAC,IAAI,IAAI,EAAE;QAE3DZ,SAAS,CAACY,YAAY,CAAC,GAAG,IAAI;QAC9Bb,MAAM,CAACE,KAAK,CAAC,CAACU,KAAK,CAACG,IAAI,CAACP,OAAO,CAAC;QAEjC,IAAIT,KAAK,IAAI,IAAI,EAAE;UACjB,IAAI0B,UAAU,GAAGT,WAAW,CAACjB,KAAK,EAAEG,KAAK,EAAEM,OAAO,CAAC;UACnD,IAAI,CAACiB,UAAU,EAAE;YAGfzB,MAAM,CAACE,KAAK,CAAC,CAACU,KAAK,CAAC8B,GAAG,CAAC,CAAC;UAC3B;QACF;MACF;IACF;EACF,CAAC;EACD,OAAOb,oBAAoB;AAC7B;AAMA,SAASY,eAAeA,CAACvC,KAAK,EAAE;EAC9B,OAAO,sBAAsB,GAAGA,KAAK,GAAG,OAAO;AACjD;AACA,IAAIyC,YAAY,GAAG,OAAO;AAC1B,SAASjC,eAAeA,CAACJ,OAAO,EAAE;EAChC,OAAOiC,MAAM,CAACjC,OAAO,CAACO,YAAY,CAAC+B,KAAK,CAACD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D;AACA,SAASxB,gBAAgBA,CAAC0B,GAAG,EAAE;EAC7B,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACd,GAAG,CAACQ,MAAM,CAAC,CAACL,IAAI,CAAC,UAACc,CAAC,EAAEC,CAAC;IAAA,OAAKD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAAA,EAAC;AACpE;AACA,IAAIC,eAAe,GAAG,cAAc;AACpC,SAASpC,eAAeA,CAACN,OAAO,EAAE;EAChC,IAAI2C,QAAQ,GAAG3C,OAAO,CAACoC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;EAC3C,OAAOD,QAAQ,KAAK,EAAE,GAAGA,QAAQ,CAACE,OAAO,CAACH,eAAe,EAAE,IAAI,CAAC,GAAG,IAAI;AACzE;AACA,SAASxB,YAAYA,CAAC4B,IAAI,EAAE9C,OAAO,EAAEe,QAAQ,EAAE;EAC7C,IAAI;IAEF+B,IAAI,CAACC,UAAU,CAAC/C,OAAO,EAAEe,QAAQ,CAAC;IAClC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOiC,CAAC,EAAE;IAGV,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}