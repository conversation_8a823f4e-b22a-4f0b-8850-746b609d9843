{"ast": null, "code": "import Platform from \"react-native-web/dist/exports/Platform\";\nexport default function getDefaultHeaderHeight(layout, modalPresentation, statusBarHeight) {\n  var headerHeight;\n  var isLandscape = layout.width > layout.height;\n  if (Platform.OS === 'ios') {\n    if (Platform.isPad || Platform.isTV) {\n      if (modalPresentation) {\n        headerHeight = 56;\n      } else {\n        headerHeight = 50;\n      }\n    } else {\n      if (isLandscape) {\n        headerHeight = 32;\n      } else {\n        if (modalPresentation) {\n          headerHeight = 56;\n        } else {\n          headerHeight = 44;\n        }\n      }\n    }\n  } else if (Platform.OS === 'android') {\n    headerHeight = 56;\n  } else {\n    headerHeight = 64;\n  }\n  return headerHeight + statusBarHeight;\n}", "map": {"version": 3, "names": ["getDefaultHeaderHeight", "layout", "modalPresentation", "statusBarHeight", "headerHeight", "isLandscape", "width", "height", "Platform", "OS", "isPad", "isTV"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/@react-navigation/elements/src/Header/getDefaultHeaderHeight.tsx"], "sourcesContent": ["import { Platform } from 'react-native';\n\nimport type { Layout } from '../types';\n\nexport default function getDefaultHeaderHeight(\n  layout: Layout,\n  modalPresentation: boolean,\n  statusBarHeight: number\n): number {\n  let headerHeight;\n\n  const isLandscape = layout.width > layout.height;\n\n  if (Platform.OS === 'ios') {\n    if (Platform.isPad || Platform.isTV) {\n      if (modalPresentation) {\n        headerHeight = 56;\n      } else {\n        headerHeight = 50;\n      }\n    } else {\n      if (isLandscape) {\n        headerHeight = 32;\n      } else {\n        if (modalPresentation) {\n          headerHeight = 56;\n        } else {\n          headerHeight = 44;\n        }\n      }\n    }\n  } else if (Platform.OS === 'android') {\n    headerHeight = 56;\n  } else {\n    headerHeight = 64;\n  }\n\n  return headerHeight + statusBarHeight;\n}\n"], "mappings": ";AAIA,eAAe,SAASA,sBAAsBA,CAC5CC,MAAc,EACdC,iBAA0B,EAC1BC,eAAuB,EACf;EACR,IAAIC,YAAY;EAEhB,IAAMC,WAAW,GAAGJ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACM,MAAM;EAEhD,IAAIC,QAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,IAAID,QAAQ,CAACE,KAAK,IAAIF,QAAQ,CAACG,IAAI,EAAE;MACnC,IAAIT,iBAAiB,EAAE;QACrBE,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACLA,YAAY,GAAG,EAAE;MACnB;IACF,CAAC,MAAM;MACL,IAAIC,WAAW,EAAE;QACfD,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIF,iBAAiB,EAAE;UACrBE,YAAY,GAAG,EAAE;QACnB,CAAC,MAAM;UACLA,YAAY,GAAG,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM,IAAII,QAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IACpCL,YAAY,GAAG,EAAE;EACnB,CAAC,MAAM;IACLA,YAAY,GAAG,EAAE;EACnB;EAEA,OAAOA,YAAY,GAAGD,eAAe;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}