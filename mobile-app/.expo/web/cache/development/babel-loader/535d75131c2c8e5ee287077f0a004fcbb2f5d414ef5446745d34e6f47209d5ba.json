{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nvar _PROPERTIES_FLIP;\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animationKeyframes\"];\nimport createReactDOMStyle from \"./createReactDOMStyle\";\nimport hash from \"./hash\";\nimport hyphenateStyleName from \"./hyphenateStyleName\";\nimport normalizeValueWithProperty from \"./normalizeValueWithProperty\";\nimport prefixStyles from \"../../../modules/prefixStyles\";\nvar cache = new Map();\nvar emptyObject = {};\nvar classicGroup = 1;\nvar atomicGroup = 3;\nvar customGroup = {\n  borderColor: 2,\n  borderRadius: 2,\n  borderStyle: 2,\n  borderWidth: 2,\n  display: 2,\n  flex: 2,\n  inset: 2,\n  margin: 2,\n  overflow: 2,\n  overscrollBehavior: 2,\n  padding: 2,\n  insetBlock: 2.1,\n  insetInline: 2.1,\n  marginInline: 2.1,\n  marginBlock: 2.1,\n  paddingInline: 2.1,\n  paddingBlock: 2.1,\n  borderBlockStartColor: 2.2,\n  borderBlockStartStyle: 2.2,\n  borderBlockStartWidth: 2.2,\n  borderBlockEndColor: 2.2,\n  borderBlockEndStyle: 2.2,\n  borderBlockEndWidth: 2.2,\n  borderInlineStartColor: 2.2,\n  borderInlineStartStyle: 2.2,\n  borderInlineStartWidth: 2.2,\n  borderInlineEndColor: 2.2,\n  borderInlineEndStyle: 2.2,\n  borderInlineEndWidth: 2.2,\n  borderEndStartRadius: 2.2,\n  borderEndEndRadius: 2.2,\n  borderStartStartRadius: 2.2,\n  borderStartEndRadius: 2.2,\n  insetBlockEnd: 2.2,\n  insetBlockStart: 2.2,\n  insetInlineEnd: 2.2,\n  insetInlineStart: 2.2,\n  marginBlockStart: 2.2,\n  marginBlockEnd: 2.2,\n  marginInlineStart: 2.2,\n  marginInlineEnd: 2.2,\n  paddingBlockStart: 2.2,\n  paddingBlockEnd: 2.2,\n  paddingInlineStart: 2.2,\n  paddingInlineEnd: 2.2\n};\nvar borderTopLeftRadius = 'borderTopLeftRadius';\nvar borderTopRightRadius = 'borderTopRightRadius';\nvar borderBottomLeftRadius = 'borderBottomLeftRadius';\nvar borderBottomRightRadius = 'borderBottomRightRadius';\nvar borderLeftColor = 'borderLeftColor';\nvar borderLeftStyle = 'borderLeftStyle';\nvar borderLeftWidth = 'borderLeftWidth';\nvar borderRightColor = 'borderRightColor';\nvar borderRightStyle = 'borderRightStyle';\nvar borderRightWidth = 'borderRightWidth';\nvar right = 'right';\nvar marginLeft = 'marginLeft';\nvar marginRight = 'marginRight';\nvar paddingLeft = 'paddingLeft';\nvar paddingRight = 'paddingRight';\nvar left = 'left';\nvar PROPERTIES_FLIP = (_PROPERTIES_FLIP = {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_PROPERTIES_FLIP, borderTopLeftRadius, borderTopRightRadius), borderTopRightRadius, borderTopLeftRadius), borderBottomLeftRadius, borderBottomRightRadius), borderBottomRightRadius, borderBottomLeftRadius), borderLeftColor, borderRightColor), borderLeftStyle, borderRightStyle), borderLeftWidth, borderRightWidth), borderRightColor, borderLeftColor), borderRightStyle, borderLeftStyle), borderRightWidth, borderLeftWidth), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_PROPERTIES_FLIP, left, right), marginLeft, marginRight), marginRight, marginLeft), paddingLeft, paddingRight), paddingRight, paddingLeft), right, left));\nvar PROPERTIES_I18N = {\n  borderStartStartRadius: borderTopLeftRadius,\n  borderStartEndRadius: borderTopRightRadius,\n  borderEndStartRadius: borderBottomLeftRadius,\n  borderEndEndRadius: borderBottomRightRadius,\n  borderInlineStartColor: borderLeftColor,\n  borderInlineStartStyle: borderLeftStyle,\n  borderInlineStartWidth: borderLeftWidth,\n  borderInlineEndColor: borderRightColor,\n  borderInlineEndStyle: borderRightStyle,\n  borderInlineEndWidth: borderRightWidth,\n  insetInlineEnd: right,\n  insetInlineStart: left,\n  marginInlineStart: marginLeft,\n  marginInlineEnd: marginRight,\n  paddingInlineStart: paddingLeft,\n  paddingInlineEnd: paddingRight\n};\nvar PROPERTIES_VALUE = ['clear', 'float', 'textAlign'];\nexport function atomic(style) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  function atomicCompile(srcProp, prop, value) {\n    var valueString = stringifyValueWithProperty(value, prop);\n    var cacheKey = prop + valueString;\n    var cachedResult = cache.get(cacheKey);\n    var identifier;\n    if (cachedResult != null) {\n      identifier = cachedResult[0];\n      compiledRules.push(cachedResult[1]);\n    } else {\n      var v = srcProp !== prop ? cacheKey : valueString;\n      identifier = createIdentifier('r', srcProp, v);\n      var order = customGroup[srcProp] || atomicGroup;\n      var rules = createAtomicRules(identifier, prop, value);\n      var orderedRules = [rules, order];\n      compiledRules.push(orderedRules);\n      cache.set(cacheKey, [identifier, orderedRules]);\n    }\n    return identifier;\n  }\n  Object.keys(style).sort().forEach(function (srcProp) {\n    var value = style[srcProp];\n    if (value != null) {\n      var localizeableValue;\n      if (PROPERTIES_VALUE.indexOf(srcProp) > -1) {\n        var _left = atomicCompile(srcProp, srcProp, 'left');\n        var _right = atomicCompile(srcProp, srcProp, 'right');\n        if (value === 'start') {\n          localizeableValue = [_left, _right];\n        } else if (value === 'end') {\n          localizeableValue = [_right, _left];\n        }\n      }\n      var propPolyfill = PROPERTIES_I18N[srcProp];\n      if (propPolyfill != null) {\n        var ltr = atomicCompile(srcProp, propPolyfill, value);\n        var rtl = atomicCompile(srcProp, PROPERTIES_FLIP[propPolyfill], value);\n        localizeableValue = [ltr, rtl];\n      }\n      if (srcProp === 'transitionProperty') {\n        var values = Array.isArray(value) ? value : [value];\n        var polyfillIndices = [];\n        for (var i = 0; i < values.length; i++) {\n          var val = values[i];\n          if (typeof val === 'string' && PROPERTIES_I18N[val] != null) {\n            polyfillIndices.push(i);\n          }\n        }\n        if (polyfillIndices.length > 0) {\n          var ltrPolyfillValues = _toConsumableArray(values);\n          var rtlPolyfillValues = _toConsumableArray(values);\n          polyfillIndices.forEach(function (i) {\n            var ltrVal = ltrPolyfillValues[i];\n            if (typeof ltrVal === 'string') {\n              var ltrPolyfill = PROPERTIES_I18N[ltrVal];\n              var rtlPolyfill = PROPERTIES_FLIP[ltrPolyfill];\n              ltrPolyfillValues[i] = ltrPolyfill;\n              rtlPolyfillValues[i] = rtlPolyfill;\n              var _ltr = atomicCompile(srcProp, srcProp, ltrPolyfillValues);\n              var _rtl = atomicCompile(srcProp, srcProp, rtlPolyfillValues);\n              localizeableValue = [_ltr, _rtl];\n            }\n          });\n        }\n      }\n      if (localizeableValue == null) {\n        localizeableValue = atomicCompile(srcProp, srcProp, value);\n      } else {\n        compiledStyle['$$css$localize'] = true;\n      }\n      compiledStyle[srcProp] = localizeableValue;\n    }\n  });\n  return [compiledStyle, compiledRules];\n}\nexport function classic(style, name) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  var animationKeyframes = style.animationKeyframes,\n    rest = _objectWithoutPropertiesLoose(style, _excluded);\n  var identifier = createIdentifier('css', name, JSON.stringify(style));\n  var selector = \".\" + identifier;\n  var animationName;\n  if (animationKeyframes != null) {\n    var _processKeyframesValu = processKeyframesValue(animationKeyframes),\n      animationNames = _processKeyframesValu[0],\n      keyframesRules = _processKeyframesValu[1];\n    animationName = animationNames.join(',');\n    compiledRules.push.apply(compiledRules, _toConsumableArray(keyframesRules));\n  }\n  var block = createDeclarationBlock(_objectSpread(_objectSpread({}, rest), {}, {\n    animationName: animationName\n  }));\n  compiledRules.push(\"\" + selector + block);\n  compiledStyle[identifier] = identifier;\n  return [compiledStyle, [[compiledRules, classicGroup]]];\n}\nexport function inline(originalStyle, isRTL) {\n  var style = originalStyle || emptyObject;\n  var frozenProps = {};\n  var nextStyle = {};\n  var _loop = function _loop() {\n    var originalValue = style[originalProp];\n    var prop = originalProp;\n    var value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || originalValue == null) {\n      return \"continue\";\n    }\n    if (PROPERTIES_VALUE.indexOf(originalProp) > -1) {\n      if (originalValue === 'start') {\n        value = isRTL ? 'right' : 'left';\n      } else if (originalValue === 'end') {\n        value = isRTL ? 'left' : 'right';\n      }\n    }\n    var propPolyfill = PROPERTIES_I18N[originalProp];\n    if (propPolyfill != null) {\n      prop = isRTL ? PROPERTIES_FLIP[propPolyfill] : propPolyfill;\n    }\n    if (originalProp === 'transitionProperty') {\n      var originalValues = Array.isArray(originalValue) ? originalValue : [originalValue];\n      originalValues.forEach(function (val, i) {\n        if (typeof val === 'string') {\n          var valuePolyfill = PROPERTIES_I18N[val];\n          if (valuePolyfill != null) {\n            originalValues[i] = isRTL ? PROPERTIES_FLIP[valuePolyfill] : valuePolyfill;\n            value = originalValues.join(' ');\n          }\n        }\n      });\n    }\n    if (!frozenProps[prop]) {\n      nextStyle[prop] = value;\n    }\n    if (prop === originalProp) {\n      frozenProps[prop] = true;\n    }\n  };\n  for (var originalProp in style) {\n    var _ret = _loop();\n    if (_ret === \"continue\") continue;\n  }\n  return createReactDOMStyle(nextStyle, true);\n}\nexport function stringifyValueWithProperty(value, property) {\n  var normalizedValue = normalizeValueWithProperty(value, property);\n  return typeof normalizedValue !== 'string' ? JSON.stringify(normalizedValue || '') : normalizedValue;\n}\nfunction createAtomicRules(identifier, property, value) {\n  var rules = [];\n  var selector = \".\" + identifier;\n  switch (property) {\n    case 'animationKeyframes':\n      {\n        var _processKeyframesValu2 = processKeyframesValue(value),\n          animationNames = _processKeyframesValu2[0],\n          keyframesRules = _processKeyframesValu2[1];\n        var block = createDeclarationBlock({\n          animationName: animationNames.join(',')\n        });\n        rules.push.apply(rules, [\"\" + selector + block].concat(_toConsumableArray(keyframesRules)));\n        break;\n      }\n    case 'placeholderTextColor':\n      {\n        var _block = createDeclarationBlock({\n          color: value,\n          opacity: 1\n        });\n        rules.push(selector + \"::-webkit-input-placeholder\" + _block, selector + \"::-moz-placeholder\" + _block, selector + \":-ms-input-placeholder\" + _block, selector + \"::placeholder\" + _block);\n        break;\n      }\n    case 'pointerEvents':\n      {\n        var finalValue = value;\n        if (value === 'auto' || value === 'box-only') {\n          finalValue = 'auto!important';\n          if (value === 'box-only') {\n            var _block2 = createDeclarationBlock({\n              pointerEvents: 'none'\n            });\n            rules.push(selector + \">*\" + _block2);\n          }\n        } else if (value === 'none' || value === 'box-none') {\n          finalValue = 'none!important';\n          if (value === 'box-none') {\n            var _block3 = createDeclarationBlock({\n              pointerEvents: 'auto'\n            });\n            rules.push(selector + \">*\" + _block3);\n          }\n        }\n        var _block4 = createDeclarationBlock({\n          pointerEvents: finalValue\n        });\n        rules.push(\"\" + selector + _block4);\n        break;\n      }\n    case 'scrollbarWidth':\n      {\n        if (value === 'none') {\n          rules.push(selector + \"::-webkit-scrollbar{display:none}\");\n        }\n        var _block5 = createDeclarationBlock({\n          scrollbarWidth: value\n        });\n        rules.push(\"\" + selector + _block5);\n        break;\n      }\n    default:\n      {\n        var _block6 = createDeclarationBlock(_defineProperty({}, property, value));\n        rules.push(\"\" + selector + _block6);\n        break;\n      }\n  }\n  return rules;\n}\nfunction createDeclarationBlock(style) {\n  var domStyle = prefixStyles(createReactDOMStyle(style));\n  var declarationsString = Object.keys(domStyle).map(function (property) {\n    var value = domStyle[property];\n    var prop = hyphenateStyleName(property);\n    if (Array.isArray(value)) {\n      return value.map(function (v) {\n        return prop + \":\" + v;\n      }).join(';');\n    } else {\n      return prop + \":\" + value;\n    }\n  }).sort().join(';');\n  return \"{\" + declarationsString + \";}\";\n}\nfunction createIdentifier(prefix, name, key) {\n  var hashedString = hash(name + key);\n  return process.env.NODE_ENV !== 'production' ? prefix + \"-\" + name + \"-\" + hashedString : prefix + \"-\" + hashedString;\n}\nfunction createKeyframes(keyframes) {\n  var prefixes = ['-webkit-', ''];\n  var identifier = createIdentifier('r', 'animation', JSON.stringify(keyframes));\n  var steps = '{' + Object.keys(keyframes).map(function (stepName) {\n    var rule = keyframes[stepName];\n    var block = createDeclarationBlock(rule);\n    return \"\" + stepName + block;\n  }).join('') + '}';\n  var rules = prefixes.map(function (prefix) {\n    return \"@\" + prefix + \"keyframes \" + identifier + steps;\n  });\n  return [identifier, rules];\n}\nfunction processKeyframesValue(keyframesValue) {\n  if (typeof keyframesValue === 'number') {\n    throw new Error(\"Invalid CSS keyframes type: \" + typeof keyframesValue);\n  }\n  var animationNames = [];\n  var rules = [];\n  var value = Array.isArray(keyframesValue) ? keyframesValue : [keyframesValue];\n  value.forEach(function (keyframes) {\n    if (typeof keyframes === 'string') {\n      animationNames.push(keyframes);\n    } else {\n      var _createKeyframes = createKeyframes(keyframes),\n        identifier = _createKeyframes[0],\n        keyframesRules = _createKeyframes[1];\n      animationNames.push(identifier);\n      rules.push.apply(rules, _toConsumableArray(keyframesRules));\n    }\n  });\n  return [animationNames, rules];\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutPropertiesLoose", "_excluded", "createReactDOMStyle", "hash", "hyphenateStyleName", "normalizeValueWithProperty", "prefixStyles", "cache", "Map", "emptyObject", "classicGroup", "atomicGroup", "customGroup", "borderColor", "borderRadius", "borderStyle", "borderWidth", "display", "flex", "inset", "margin", "overflow", "overscroll<PERSON><PERSON><PERSON><PERSON>", "padding", "insetBlock", "insetInline", "marginInline", "marginBlock", "paddingInline", "paddingBlock", "borderBlockStartColor", "borderBlockStartStyle", "borderBlockStartWidth", "borderBlockEndColor", "borderBlockEndStyle", "borderBlockEndWidth", "borderInlineStartColor", "borderInlineStartStyle", "borderInlineStartWidth", "borderInlineEndColor", "borderInlineEndStyle", "borderInlineEndWidth", "borderEndStartRadius", "borderEndEndRadius", "borderStartStartRadius", "borderStartEndRadius", "insetBlockEnd", "insetBlockStart", "insetInlineEnd", "insetInlineStart", "marginBlockStart", "marginBlockEnd", "marginInlineStart", "marginInlineEnd", "paddingBlockStart", "paddingBlockEnd", "paddingInlineStart", "paddingInlineEnd", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "borderLeftColor", "borderLeftStyle", "borderLeftWidth", "borderRightColor", "borderRightStyle", "borderRightWidth", "right", "marginLeft", "marginRight", "paddingLeft", "paddingRight", "left", "PROPERTIES_FLIP", "_PROPERTIES_FLIP", "_defineProperty", "PROPERTIES_I18N", "PROPERTIES_VALUE", "atomic", "style", "compiledStyle", "$$css", "compiledRules", "atomicCompile", "srcProp", "prop", "value", "valueString", "stringifyValueWithProperty", "cache<PERSON>ey", "cachedResult", "get", "identifier", "push", "v", "createIdentifier", "order", "rules", "createAtomicRules", "orderedRules", "set", "Object", "keys", "sort", "for<PERSON>ach", "localizeableValue", "indexOf", "_left", "_right", "propPolyfill", "ltr", "rtl", "values", "Array", "isArray", "polyfillIndices", "i", "length", "val", "ltrPolyfillValues", "_toConsumableArray", "rtlPolyfillValues", "ltrVal", "ltrPolyfill", "rtlPolyfill", "_ltr", "_rtl", "classic", "name", "animationKeyframes", "rest", "JSON", "stringify", "selector", "animationName", "_processKeyframesValu", "processKeyframesValue", "animationNames", "keyframesRules", "join", "apply", "block", "createDeclarationBlock", "inline", "originalStyle", "isRTL", "frozenProps", "nextStyle", "_loop", "originalValue", "originalProp", "prototype", "hasOwnProperty", "call", "originalValues", "valuePolyfill", "_ret", "property", "normalizedValue", "_processKeyframesValu2", "concat", "_block", "color", "opacity", "finalValue", "_block2", "pointerEvents", "_block3", "_block4", "_block5", "scrollbarWidth", "_block6", "domStyle", "declarationsString", "map", "prefix", "key", "hashedString", "process", "env", "NODE_ENV", "createKeyframes", "keyframes", "prefixes", "steps", "<PERSON><PERSON><PERSON>", "rule", "keyframesValue", "Error", "_createKeyframes"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/react-native-web/dist/exports/StyleSheet/compiler/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"animationKeyframes\"];\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport createReactDOMStyle from './createReactDOMStyle';\nimport hash from './hash';\nimport hyphenateStyleName from './hyphenateStyleName';\nimport normalizeValueWithProperty from './normalizeValueWithProperty';\nimport prefixStyles from '../../../modules/prefixStyles';\nvar cache = new Map();\nvar emptyObject = {};\nvar classicGroup = 1;\nvar atomicGroup = 3;\nvar customGroup = {\n  borderColor: 2,\n  borderRadius: 2,\n  borderStyle: 2,\n  borderWidth: 2,\n  display: 2,\n  flex: 2,\n  inset: 2,\n  margin: 2,\n  overflow: 2,\n  overscrollBehavior: 2,\n  padding: 2,\n  insetBlock: 2.1,\n  insetInline: 2.1,\n  marginInline: 2.1,\n  marginBlock: 2.1,\n  paddingInline: 2.1,\n  paddingBlock: 2.1,\n  borderBlockStartColor: 2.2,\n  borderBlockStartStyle: 2.2,\n  borderBlockStartWidth: 2.2,\n  borderBlockEndColor: 2.2,\n  borderBlockEndStyle: 2.2,\n  borderBlockEndWidth: 2.2,\n  borderInlineStartColor: 2.2,\n  borderInlineStartStyle: 2.2,\n  borderInlineStartWidth: 2.2,\n  borderInlineEndColor: 2.2,\n  borderInlineEndStyle: 2.2,\n  borderInlineEndWidth: 2.2,\n  borderEndStartRadius: 2.2,\n  borderEndEndRadius: 2.2,\n  borderStartStartRadius: 2.2,\n  borderStartEndRadius: 2.2,\n  insetBlockEnd: 2.2,\n  insetBlockStart: 2.2,\n  insetInlineEnd: 2.2,\n  insetInlineStart: 2.2,\n  marginBlockStart: 2.2,\n  marginBlockEnd: 2.2,\n  marginInlineStart: 2.2,\n  marginInlineEnd: 2.2,\n  paddingBlockStart: 2.2,\n  paddingBlockEnd: 2.2,\n  paddingInlineStart: 2.2,\n  paddingInlineEnd: 2.2\n};\nvar borderTopLeftRadius = 'borderTopLeftRadius';\nvar borderTopRightRadius = 'borderTopRightRadius';\nvar borderBottomLeftRadius = 'borderBottomLeftRadius';\nvar borderBottomRightRadius = 'borderBottomRightRadius';\nvar borderLeftColor = 'borderLeftColor';\nvar borderLeftStyle = 'borderLeftStyle';\nvar borderLeftWidth = 'borderLeftWidth';\nvar borderRightColor = 'borderRightColor';\nvar borderRightStyle = 'borderRightStyle';\nvar borderRightWidth = 'borderRightWidth';\nvar right = 'right';\nvar marginLeft = 'marginLeft';\nvar marginRight = 'marginRight';\nvar paddingLeft = 'paddingLeft';\nvar paddingRight = 'paddingRight';\nvar left = 'left';\n\n// Map of LTR property names to their BiDi equivalent.\nvar PROPERTIES_FLIP = {\n  [borderTopLeftRadius]: borderTopRightRadius,\n  [borderTopRightRadius]: borderTopLeftRadius,\n  [borderBottomLeftRadius]: borderBottomRightRadius,\n  [borderBottomRightRadius]: borderBottomLeftRadius,\n  [borderLeftColor]: borderRightColor,\n  [borderLeftStyle]: borderRightStyle,\n  [borderLeftWidth]: borderRightWidth,\n  [borderRightColor]: borderLeftColor,\n  [borderRightStyle]: borderLeftStyle,\n  [borderRightWidth]: borderLeftWidth,\n  [left]: right,\n  [marginLeft]: marginRight,\n  [marginRight]: marginLeft,\n  [paddingLeft]: paddingRight,\n  [paddingRight]: paddingLeft,\n  [right]: left\n};\n\n// Map of I18N property names to their LTR equivalent.\nvar PROPERTIES_I18N = {\n  borderStartStartRadius: borderTopLeftRadius,\n  borderStartEndRadius: borderTopRightRadius,\n  borderEndStartRadius: borderBottomLeftRadius,\n  borderEndEndRadius: borderBottomRightRadius,\n  borderInlineStartColor: borderLeftColor,\n  borderInlineStartStyle: borderLeftStyle,\n  borderInlineStartWidth: borderLeftWidth,\n  borderInlineEndColor: borderRightColor,\n  borderInlineEndStyle: borderRightStyle,\n  borderInlineEndWidth: borderRightWidth,\n  insetInlineEnd: right,\n  insetInlineStart: left,\n  marginInlineStart: marginLeft,\n  marginInlineEnd: marginRight,\n  paddingInlineStart: paddingLeft,\n  paddingInlineEnd: paddingRight\n};\nvar PROPERTIES_VALUE = ['clear', 'float', 'textAlign'];\nexport function atomic(style) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  function atomicCompile(srcProp, prop, value) {\n    var valueString = stringifyValueWithProperty(value, prop);\n    var cacheKey = prop + valueString;\n    var cachedResult = cache.get(cacheKey);\n    var identifier;\n    if (cachedResult != null) {\n      identifier = cachedResult[0];\n      compiledRules.push(cachedResult[1]);\n    } else {\n      var v = srcProp !== prop ? cacheKey : valueString;\n      identifier = createIdentifier('r', srcProp, v);\n      var order = customGroup[srcProp] || atomicGroup;\n      var rules = createAtomicRules(identifier, prop, value);\n      var orderedRules = [rules, order];\n      compiledRules.push(orderedRules);\n      cache.set(cacheKey, [identifier, orderedRules]);\n    }\n    return identifier;\n  }\n  Object.keys(style).sort().forEach(srcProp => {\n    var value = style[srcProp];\n    if (value != null) {\n      var localizeableValue;\n      // BiDi flip values\n      if (PROPERTIES_VALUE.indexOf(srcProp) > -1) {\n        var _left = atomicCompile(srcProp, srcProp, 'left');\n        var _right = atomicCompile(srcProp, srcProp, 'right');\n        if (value === 'start') {\n          localizeableValue = [_left, _right];\n        } else if (value === 'end') {\n          localizeableValue = [_right, _left];\n        }\n      }\n      // BiDi flip properties\n      var propPolyfill = PROPERTIES_I18N[srcProp];\n      if (propPolyfill != null) {\n        var ltr = atomicCompile(srcProp, propPolyfill, value);\n        var rtl = atomicCompile(srcProp, PROPERTIES_FLIP[propPolyfill], value);\n        localizeableValue = [ltr, rtl];\n      }\n      // BiDi flip transitionProperty value\n      if (srcProp === 'transitionProperty') {\n        var values = Array.isArray(value) ? value : [value];\n        var polyfillIndices = [];\n        for (var i = 0; i < values.length; i++) {\n          var val = values[i];\n          if (typeof val === 'string' && PROPERTIES_I18N[val] != null) {\n            polyfillIndices.push(i);\n          }\n        }\n        if (polyfillIndices.length > 0) {\n          var ltrPolyfillValues = [...values];\n          var rtlPolyfillValues = [...values];\n          polyfillIndices.forEach(i => {\n            var ltrVal = ltrPolyfillValues[i];\n            if (typeof ltrVal === 'string') {\n              var ltrPolyfill = PROPERTIES_I18N[ltrVal];\n              var rtlPolyfill = PROPERTIES_FLIP[ltrPolyfill];\n              ltrPolyfillValues[i] = ltrPolyfill;\n              rtlPolyfillValues[i] = rtlPolyfill;\n              var _ltr = atomicCompile(srcProp, srcProp, ltrPolyfillValues);\n              var _rtl = atomicCompile(srcProp, srcProp, rtlPolyfillValues);\n              localizeableValue = [_ltr, _rtl];\n            }\n          });\n        }\n      }\n      if (localizeableValue == null) {\n        localizeableValue = atomicCompile(srcProp, srcProp, value);\n      } else {\n        compiledStyle['$$css$localize'] = true;\n      }\n      compiledStyle[srcProp] = localizeableValue;\n    }\n  });\n  return [compiledStyle, compiledRules];\n}\n\n/**\n * Compile simple style object to classic CSS rules.\n * No support for 'placeholderTextColor', 'scrollbarWidth', or 'pointerEvents'.\n */\nexport function classic(style, name) {\n  var compiledStyle = {\n    $$css: true\n  };\n  var compiledRules = [];\n  var animationKeyframes = style.animationKeyframes,\n    rest = _objectWithoutPropertiesLoose(style, _excluded);\n  var identifier = createIdentifier('css', name, JSON.stringify(style));\n  var selector = \".\" + identifier;\n  var animationName;\n  if (animationKeyframes != null) {\n    var _processKeyframesValu = processKeyframesValue(animationKeyframes),\n      animationNames = _processKeyframesValu[0],\n      keyframesRules = _processKeyframesValu[1];\n    animationName = animationNames.join(',');\n    compiledRules.push(...keyframesRules);\n  }\n  var block = createDeclarationBlock(_objectSpread(_objectSpread({}, rest), {}, {\n    animationName\n  }));\n  compiledRules.push(\"\" + selector + block);\n  compiledStyle[identifier] = identifier;\n  return [compiledStyle, [[compiledRules, classicGroup]]];\n}\n\n/**\n * Compile simple style object to inline DOM styles.\n * No support for 'animationKeyframes', 'placeholderTextColor', 'scrollbarWidth', or 'pointerEvents'.\n */\nexport function inline(originalStyle, isRTL) {\n  var style = originalStyle || emptyObject;\n  var frozenProps = {};\n  var nextStyle = {};\n  var _loop = function _loop() {\n    var originalValue = style[originalProp];\n    var prop = originalProp;\n    var value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || originalValue == null) {\n      return \"continue\";\n    }\n\n    // BiDi flip values\n    if (PROPERTIES_VALUE.indexOf(originalProp) > -1) {\n      if (originalValue === 'start') {\n        value = isRTL ? 'right' : 'left';\n      } else if (originalValue === 'end') {\n        value = isRTL ? 'left' : 'right';\n      }\n    }\n    // BiDi flip properties\n    var propPolyfill = PROPERTIES_I18N[originalProp];\n    if (propPolyfill != null) {\n      prop = isRTL ? PROPERTIES_FLIP[propPolyfill] : propPolyfill;\n    }\n    // BiDi flip transitionProperty value\n    if (originalProp === 'transitionProperty') {\n      // $FlowFixMe\n      var originalValues = Array.isArray(originalValue) ? originalValue : [originalValue];\n      originalValues.forEach((val, i) => {\n        if (typeof val === 'string') {\n          var valuePolyfill = PROPERTIES_I18N[val];\n          if (valuePolyfill != null) {\n            originalValues[i] = isRTL ? PROPERTIES_FLIP[valuePolyfill] : valuePolyfill;\n            value = originalValues.join(' ');\n          }\n        }\n      });\n    }\n\n    // Create finalized style\n    if (!frozenProps[prop]) {\n      nextStyle[prop] = value;\n    }\n    if (prop === originalProp) {\n      frozenProps[prop] = true;\n    }\n\n    //    if (PROPERTIES_I18N.hasOwnProperty(originalProp)) {\n    //    frozenProps[prop] = true;\n    //}\n  };\n  for (var originalProp in style) {\n    var _ret = _loop();\n    if (_ret === \"continue\") continue;\n  }\n  return createReactDOMStyle(nextStyle, true);\n}\n\n/**\n * Create a value string that normalizes different input values with a common\n * output.\n */\nexport function stringifyValueWithProperty(value, property) {\n  // e.g., 0 => '0px', 'black' => 'rgba(0,0,0,1)'\n  var normalizedValue = normalizeValueWithProperty(value, property);\n  return typeof normalizedValue !== 'string' ? JSON.stringify(normalizedValue || '') : normalizedValue;\n}\n\n/**\n * Create the Atomic CSS rules needed for a given StyleSheet rule.\n * Translates StyleSheet declarations to CSS.\n */\nfunction createAtomicRules(identifier, property, value) {\n  var rules = [];\n  var selector = \".\" + identifier;\n\n  // Handle non-standard properties and object values that require multiple\n  // CSS rules to be created.\n  switch (property) {\n    case 'animationKeyframes':\n      {\n        var _processKeyframesValu2 = processKeyframesValue(value),\n          animationNames = _processKeyframesValu2[0],\n          keyframesRules = _processKeyframesValu2[1];\n        var block = createDeclarationBlock({\n          animationName: animationNames.join(',')\n        });\n        rules.push(\"\" + selector + block, ...keyframesRules);\n        break;\n      }\n\n    // Equivalent to using '::placeholder'\n    case 'placeholderTextColor':\n      {\n        var _block = createDeclarationBlock({\n          color: value,\n          opacity: 1\n        });\n        rules.push(selector + \"::-webkit-input-placeholder\" + _block, selector + \"::-moz-placeholder\" + _block, selector + \":-ms-input-placeholder\" + _block, selector + \"::placeholder\" + _block);\n        break;\n      }\n\n    // Polyfill for additional 'pointer-events' values\n    // See d13f78622b233a0afc0c7a200c0a0792c8ca9e58\n    case 'pointerEvents':\n      {\n        var finalValue = value;\n        if (value === 'auto' || value === 'box-only') {\n          finalValue = 'auto!important';\n          if (value === 'box-only') {\n            var _block2 = createDeclarationBlock({\n              pointerEvents: 'none'\n            });\n            rules.push(selector + \">*\" + _block2);\n          }\n        } else if (value === 'none' || value === 'box-none') {\n          finalValue = 'none!important';\n          if (value === 'box-none') {\n            var _block3 = createDeclarationBlock({\n              pointerEvents: 'auto'\n            });\n            rules.push(selector + \">*\" + _block3);\n          }\n        }\n        var _block4 = createDeclarationBlock({\n          pointerEvents: finalValue\n        });\n        rules.push(\"\" + selector + _block4);\n        break;\n      }\n\n    // Polyfill for draft spec\n    // https://drafts.csswg.org/css-scrollbars-1/\n    case 'scrollbarWidth':\n      {\n        if (value === 'none') {\n          rules.push(selector + \"::-webkit-scrollbar{display:none}\");\n        }\n        var _block5 = createDeclarationBlock({\n          scrollbarWidth: value\n        });\n        rules.push(\"\" + selector + _block5);\n        break;\n      }\n    default:\n      {\n        var _block6 = createDeclarationBlock({\n          [property]: value\n        });\n        rules.push(\"\" + selector + _block6);\n        break;\n      }\n  }\n  return rules;\n}\n\n/**\n * Creates a CSS declaration block from a StyleSheet object.\n */\nfunction createDeclarationBlock(style) {\n  var domStyle = prefixStyles(createReactDOMStyle(style));\n  var declarationsString = Object.keys(domStyle).map(property => {\n    var value = domStyle[property];\n    var prop = hyphenateStyleName(property);\n    // The prefixer may return an array of values:\n    // { display: [ '-webkit-flex', 'flex' ] }\n    // to represent \"fallback\" declarations\n    // { display: -webkit-flex; display: flex; }\n    if (Array.isArray(value)) {\n      return value.map(v => prop + \":\" + v).join(';');\n    } else {\n      return prop + \":\" + value;\n    }\n  })\n  // Once properties are hyphenated, this will put the vendor\n  // prefixed and short-form properties first in the list.\n  .sort().join(';');\n  return \"{\" + declarationsString + \";}\";\n}\n\n/**\n * An identifier is associated with a unique set of styles.\n */\nfunction createIdentifier(prefix, name, key) {\n  var hashedString = hash(name + key);\n  return process.env.NODE_ENV !== 'production' ? prefix + \"-\" + name + \"-\" + hashedString : prefix + \"-\" + hashedString;\n}\n\n/**\n * Create individual CSS keyframes rules.\n */\nfunction createKeyframes(keyframes) {\n  var prefixes = ['-webkit-', ''];\n  var identifier = createIdentifier('r', 'animation', JSON.stringify(keyframes));\n  var steps = '{' + Object.keys(keyframes).map(stepName => {\n    var rule = keyframes[stepName];\n    var block = createDeclarationBlock(rule);\n    return \"\" + stepName + block;\n  }).join('') + '}';\n  var rules = prefixes.map(prefix => {\n    return \"@\" + prefix + \"keyframes \" + identifier + steps;\n  });\n  return [identifier, rules];\n}\n\n/**\n * Create CSS keyframes rules and names from a StyleSheet keyframes object.\n */\nfunction processKeyframesValue(keyframesValue) {\n  if (typeof keyframesValue === 'number') {\n    throw new Error(\"Invalid CSS keyframes type: \" + typeof keyframesValue);\n  }\n  var animationNames = [];\n  var rules = [];\n  var value = Array.isArray(keyframesValue) ? keyframesValue : [keyframesValue];\n  value.forEach(keyframes => {\n    if (typeof keyframes === 'string') {\n      // Support external animation libraries (identifiers only)\n      animationNames.push(keyframes);\n    } else {\n      // Create rules for each of the keyframes\n      var _createKeyframes = createKeyframes(keyframes),\n        identifier = _createKeyframes[0],\n        keyframesRules = _createKeyframes[1];\n      animationNames.push(identifier);\n      rules.push(...keyframesRules);\n    }\n  });\n  return [animationNames, rules];\n}"], "mappings": ";;;AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,oBAAoB,CAAC;AAUtC,OAAOC,mBAAmB;AAC1B,OAAOC,IAAI;AACX,OAAOC,kBAAkB;AACzB,OAAOC,0BAA0B;AACjC,OAAOC,YAAY;AACnB,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,WAAW,GAAG;EAChBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,CAAC;EACXC,kBAAkB,EAAE,CAAC;EACrBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAE,GAAG;EAChBC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE,GAAG;EACjBC,qBAAqB,EAAE,GAAG;EAC1BC,qBAAqB,EAAE,GAAG;EAC1BC,qBAAqB,EAAE,GAAG;EAC1BC,mBAAmB,EAAE,GAAG;EACxBC,mBAAmB,EAAE,GAAG;EACxBC,mBAAmB,EAAE,GAAG;EACxBC,sBAAsB,EAAE,GAAG;EAC3BC,sBAAsB,EAAE,GAAG;EAC3BC,sBAAsB,EAAE,GAAG;EAC3BC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,oBAAoB,EAAE,GAAG;EACzBC,kBAAkB,EAAE,GAAG;EACvBC,sBAAsB,EAAE,GAAG;EAC3BC,oBAAoB,EAAE,GAAG;EACzBC,aAAa,EAAE,GAAG;EAClBC,eAAe,EAAE,GAAG;EACpBC,cAAc,EAAE,GAAG;EACnBC,gBAAgB,EAAE,GAAG;EACrBC,gBAAgB,EAAE,GAAG;EACrBC,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE,GAAG;EACpBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE,GAAG;EACpBC,kBAAkB,EAAE,GAAG;EACvBC,gBAAgB,EAAE;AACpB,CAAC;AACD,IAAIC,mBAAmB,GAAG,qBAAqB;AAC/C,IAAIC,oBAAoB,GAAG,sBAAsB;AACjD,IAAIC,sBAAsB,GAAG,wBAAwB;AACrD,IAAIC,uBAAuB,GAAG,yBAAyB;AACvD,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,gBAAgB,GAAG,kBAAkB;AACzC,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,YAAY,GAAG,cAAc;AACjC,IAAIC,IAAI,GAAG,MAAM;AAGjB,IAAIC,eAAe,IAAAC,gBAAA,OAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAD,gBAAA,EAChBjB,mBAAmB,EAAGC,oBAAoB,GAC1CA,oBAAoB,EAAGD,mBAAmB,GAC1CE,sBAAsB,EAAGC,uBAAuB,GAChDA,uBAAuB,EAAGD,sBAAsB,GAChDE,eAAe,EAAGG,gBAAgB,GAClCF,eAAe,EAAGG,gBAAgB,GAClCF,eAAe,EAAGG,gBAAgB,GAClCF,gBAAgB,EAAGH,eAAe,GAClCI,gBAAgB,EAAGH,eAAe,GAClCI,gBAAgB,EAAGH,eAAe,GAAAY,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAD,gBAAA,EAClCF,IAAI,EAAGL,KAAK,GACZC,UAAU,EAAGC,WAAW,GACxBA,WAAW,EAAGD,UAAU,GACxBE,WAAW,EAAGC,YAAY,GAC1BA,YAAY,EAAGD,WAAW,GAC1BH,KAAK,EAAGK,IAAI,EACd;AAGD,IAAII,eAAe,GAAG;EACpBjC,sBAAsB,EAAEc,mBAAmB;EAC3Cb,oBAAoB,EAAEc,oBAAoB;EAC1CjB,oBAAoB,EAAEkB,sBAAsB;EAC5CjB,kBAAkB,EAAEkB,uBAAuB;EAC3CzB,sBAAsB,EAAE0B,eAAe;EACvCzB,sBAAsB,EAAE0B,eAAe;EACvCzB,sBAAsB,EAAE0B,eAAe;EACvCzB,oBAAoB,EAAE0B,gBAAgB;EACtCzB,oBAAoB,EAAE0B,gBAAgB;EACtCzB,oBAAoB,EAAE0B,gBAAgB;EACtCnB,cAAc,EAAEoB,KAAK;EACrBnB,gBAAgB,EAAEwB,IAAI;EACtBrB,iBAAiB,EAAEiB,UAAU;EAC7BhB,eAAe,EAAEiB,WAAW;EAC5Bd,kBAAkB,EAAEe,WAAW;EAC/Bd,gBAAgB,EAAEe;AACpB,CAAC;AACD,IAAIM,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AACtD,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,IAAIC,aAAa,GAAG;IAClBC,KAAK,EAAE;EACT,CAAC;EACD,IAAIC,aAAa,GAAG,EAAE;EACtB,SAASC,aAAaA,CAACC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAE;IAC3C,IAAIC,WAAW,GAAGC,0BAA0B,CAACF,KAAK,EAAED,IAAI,CAAC;IACzD,IAAII,QAAQ,GAAGJ,IAAI,GAAGE,WAAW;IACjC,IAAIG,YAAY,GAAGpF,KAAK,CAACqF,GAAG,CAACF,QAAQ,CAAC;IACtC,IAAIG,UAAU;IACd,IAAIF,YAAY,IAAI,IAAI,EAAE;MACxBE,UAAU,GAAGF,YAAY,CAAC,CAAC,CAAC;MAC5BR,aAAa,CAACW,IAAI,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAII,CAAC,GAAGV,OAAO,KAAKC,IAAI,GAAGI,QAAQ,GAAGF,WAAW;MACjDK,UAAU,GAAGG,gBAAgB,CAAC,GAAG,EAAEX,OAAO,EAAEU,CAAC,CAAC;MAC9C,IAAIE,KAAK,GAAGrF,WAAW,CAACyE,OAAO,CAAC,IAAI1E,WAAW;MAC/C,IAAIuF,KAAK,GAAGC,iBAAiB,CAACN,UAAU,EAAEP,IAAI,EAAEC,KAAK,CAAC;MACtD,IAAIa,YAAY,GAAG,CAACF,KAAK,EAAED,KAAK,CAAC;MACjCd,aAAa,CAACW,IAAI,CAACM,YAAY,CAAC;MAChC7F,KAAK,CAAC8F,GAAG,CAACX,QAAQ,EAAE,CAACG,UAAU,EAAEO,YAAY,CAAC,CAAC;IACjD;IACA,OAAOP,UAAU;EACnB;EACAS,MAAM,CAACC,IAAI,CAACvB,KAAK,CAAC,CAACwB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,UAAApB,OAAO,EAAI;IAC3C,IAAIE,KAAK,GAAGP,KAAK,CAACK,OAAO,CAAC;IAC1B,IAAIE,KAAK,IAAI,IAAI,EAAE;MACjB,IAAImB,iBAAiB;MAErB,IAAI5B,gBAAgB,CAAC6B,OAAO,CAACtB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC1C,IAAIuB,KAAK,GAAGxB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAE,MAAM,CAAC;QACnD,IAAIwB,MAAM,GAAGzB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAE,OAAO,CAAC;QACrD,IAAIE,KAAK,KAAK,OAAO,EAAE;UACrBmB,iBAAiB,GAAG,CAACE,KAAK,EAAEC,MAAM,CAAC;QACrC,CAAC,MAAM,IAAItB,KAAK,KAAK,KAAK,EAAE;UAC1BmB,iBAAiB,GAAG,CAACG,MAAM,EAAED,KAAK,CAAC;QACrC;MACF;MAEA,IAAIE,YAAY,GAAGjC,eAAe,CAACQ,OAAO,CAAC;MAC3C,IAAIyB,YAAY,IAAI,IAAI,EAAE;QACxB,IAAIC,GAAG,GAAG3B,aAAa,CAACC,OAAO,EAAEyB,YAAY,EAAEvB,KAAK,CAAC;QACrD,IAAIyB,GAAG,GAAG5B,aAAa,CAACC,OAAO,EAAEX,eAAe,CAACoC,YAAY,CAAC,EAAEvB,KAAK,CAAC;QACtEmB,iBAAiB,GAAG,CAACK,GAAG,EAAEC,GAAG,CAAC;MAChC;MAEA,IAAI3B,OAAO,KAAK,oBAAoB,EAAE;QACpC,IAAI4B,MAAM,GAAGC,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnD,IAAI6B,eAAe,GAAG,EAAE;QACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UACtC,IAAIE,GAAG,GAAGN,MAAM,CAACI,CAAC,CAAC;UACnB,IAAI,OAAOE,GAAG,KAAK,QAAQ,IAAI1C,eAAe,CAAC0C,GAAG,CAAC,IAAI,IAAI,EAAE;YAC3DH,eAAe,CAACtB,IAAI,CAACuB,CAAC,CAAC;UACzB;QACF;QACA,IAAID,eAAe,CAACE,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAIE,iBAAiB,GAAAC,kBAAA,CAAOR,MAAM,CAAC;UACnC,IAAIS,iBAAiB,GAAAD,kBAAA,CAAOR,MAAM,CAAC;UACnCG,eAAe,CAACX,OAAO,CAAC,UAAAY,CAAC,EAAI;YAC3B,IAAIM,MAAM,GAAGH,iBAAiB,CAACH,CAAC,CAAC;YACjC,IAAI,OAAOM,MAAM,KAAK,QAAQ,EAAE;cAC9B,IAAIC,WAAW,GAAG/C,eAAe,CAAC8C,MAAM,CAAC;cACzC,IAAIE,WAAW,GAAGnD,eAAe,CAACkD,WAAW,CAAC;cAC9CJ,iBAAiB,CAACH,CAAC,CAAC,GAAGO,WAAW;cAClCF,iBAAiB,CAACL,CAAC,CAAC,GAAGQ,WAAW;cAClC,IAAIC,IAAI,GAAG1C,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEmC,iBAAiB,CAAC;cAC7D,IAAIO,IAAI,GAAG3C,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEqC,iBAAiB,CAAC;cAC7DhB,iBAAiB,GAAG,CAACoB,IAAI,EAAEC,IAAI,CAAC;YAClC;UACF,CAAC,CAAC;QACJ;MACF;MACA,IAAIrB,iBAAiB,IAAI,IAAI,EAAE;QAC7BA,iBAAiB,GAAGtB,aAAa,CAACC,OAAO,EAAEA,OAAO,EAAEE,KAAK,CAAC;MAC5D,CAAC,MAAM;QACLN,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI;MACxC;MACAA,aAAa,CAACI,OAAO,CAAC,GAAGqB,iBAAiB;IAC5C;EACF,CAAC,CAAC;EACF,OAAO,CAACzB,aAAa,EAAEE,aAAa,CAAC;AACvC;AAMA,OAAO,SAAS6C,OAAOA,CAAChD,KAAK,EAAEiD,IAAI,EAAE;EACnC,IAAIhD,aAAa,GAAG;IAClBC,KAAK,EAAE;EACT,CAAC;EACD,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAI+C,kBAAkB,GAAGlD,KAAK,CAACkD,kBAAkB;IAC/CC,IAAI,GAAGnI,6BAA6B,CAACgF,KAAK,EAAE/E,SAAS,CAAC;EACxD,IAAI4F,UAAU,GAAGG,gBAAgB,CAAC,KAAK,EAAEiC,IAAI,EAAEG,IAAI,CAACC,SAAS,CAACrD,KAAK,CAAC,CAAC;EACrE,IAAIsD,QAAQ,GAAG,GAAG,GAAGzC,UAAU;EAC/B,IAAI0C,aAAa;EACjB,IAAIL,kBAAkB,IAAI,IAAI,EAAE;IAC9B,IAAIM,qBAAqB,GAAGC,qBAAqB,CAACP,kBAAkB,CAAC;MACnEQ,cAAc,GAAGF,qBAAqB,CAAC,CAAC,CAAC;MACzCG,cAAc,GAAGH,qBAAqB,CAAC,CAAC,CAAC;IAC3CD,aAAa,GAAGG,cAAc,CAACE,IAAI,CAAC,GAAG,CAAC;IACxCzD,aAAa,CAACW,IAAI,CAAA+C,KAAA,CAAlB1D,aAAa,EAAAsC,kBAAA,CAASkB,cAAc,EAAC;EACvC;EACA,IAAIG,KAAK,GAAGC,sBAAsB,CAAChJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5EI,aAAa,EAAbA;EACF,CAAC,CAAC,CAAC;EACHpD,aAAa,CAACW,IAAI,CAAC,EAAE,GAAGwC,QAAQ,GAAGQ,KAAK,CAAC;EACzC7D,aAAa,CAACY,UAAU,CAAC,GAAGA,UAAU;EACtC,OAAO,CAACZ,aAAa,EAAE,CAAC,CAACE,aAAa,EAAEzE,YAAY,CAAC,CAAC,CAAC;AACzD;AAMA,OAAO,SAASsI,MAAMA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC3C,IAAIlE,KAAK,GAAGiE,aAAa,IAAIxI,WAAW;EACxC,IAAI0I,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,aAAa,GAAGtE,KAAK,CAACuE,YAAY,CAAC;IACvC,IAAIjE,IAAI,GAAGiE,YAAY;IACvB,IAAIhE,KAAK,GAAG+D,aAAa;IACzB,IAAI,CAAChD,MAAM,CAACkD,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC1E,KAAK,EAAEuE,YAAY,CAAC,IAAID,aAAa,IAAI,IAAI,EAAE;MACvF,OAAO,UAAU;IACnB;IAGA,IAAIxE,gBAAgB,CAAC6B,OAAO,CAAC4C,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAID,aAAa,KAAK,OAAO,EAAE;QAC7B/D,KAAK,GAAG2D,KAAK,GAAG,OAAO,GAAG,MAAM;MAClC,CAAC,MAAM,IAAII,aAAa,KAAK,KAAK,EAAE;QAClC/D,KAAK,GAAG2D,KAAK,GAAG,MAAM,GAAG,OAAO;MAClC;IACF;IAEA,IAAIpC,YAAY,GAAGjC,eAAe,CAAC0E,YAAY,CAAC;IAChD,IAAIzC,YAAY,IAAI,IAAI,EAAE;MACxBxB,IAAI,GAAG4D,KAAK,GAAGxE,eAAe,CAACoC,YAAY,CAAC,GAAGA,YAAY;IAC7D;IAEA,IAAIyC,YAAY,KAAK,oBAAoB,EAAE;MAEzC,IAAII,cAAc,GAAGzC,KAAK,CAACC,OAAO,CAACmC,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,CAAC;MACnFK,cAAc,CAAClD,OAAO,CAAC,UAACc,GAAG,EAAEF,CAAC,EAAK;QACjC,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE;UAC3B,IAAIqC,aAAa,GAAG/E,eAAe,CAAC0C,GAAG,CAAC;UACxC,IAAIqC,aAAa,IAAI,IAAI,EAAE;YACzBD,cAAc,CAACtC,CAAC,CAAC,GAAG6B,KAAK,GAAGxE,eAAe,CAACkF,aAAa,CAAC,GAAGA,aAAa;YAC1ErE,KAAK,GAAGoE,cAAc,CAACf,IAAI,CAAC,GAAG,CAAC;UAClC;QACF;MACF,CAAC,CAAC;IACJ;IAGA,IAAI,CAACO,WAAW,CAAC7D,IAAI,CAAC,EAAE;MACtB8D,SAAS,CAAC9D,IAAI,CAAC,GAAGC,KAAK;IACzB;IACA,IAAID,IAAI,KAAKiE,YAAY,EAAE;MACzBJ,WAAW,CAAC7D,IAAI,CAAC,GAAG,IAAI;IAC1B;EAKF,CAAC;EACD,KAAK,IAAIiE,YAAY,IAAIvE,KAAK,EAAE;IAC9B,IAAI6E,IAAI,GAAGR,KAAK,CAAC,CAAC;IAClB,IAAIQ,IAAI,KAAK,UAAU,EAAE;EAC3B;EACA,OAAO3J,mBAAmB,CAACkJ,SAAS,EAAE,IAAI,CAAC;AAC7C;AAMA,OAAO,SAAS3D,0BAA0BA,CAACF,KAAK,EAAEuE,QAAQ,EAAE;EAE1D,IAAIC,eAAe,GAAG1J,0BAA0B,CAACkF,KAAK,EAAEuE,QAAQ,CAAC;EACjE,OAAO,OAAOC,eAAe,KAAK,QAAQ,GAAG3B,IAAI,CAACC,SAAS,CAAC0B,eAAe,IAAI,EAAE,CAAC,GAAGA,eAAe;AACtG;AAMA,SAAS5D,iBAAiBA,CAACN,UAAU,EAAEiE,QAAQ,EAAEvE,KAAK,EAAE;EACtD,IAAIW,KAAK,GAAG,EAAE;EACd,IAAIoC,QAAQ,GAAG,GAAG,GAAGzC,UAAU;EAI/B,QAAQiE,QAAQ;IACd,KAAK,oBAAoB;MACvB;QACE,IAAIE,sBAAsB,GAAGvB,qBAAqB,CAAClD,KAAK,CAAC;UACvDmD,cAAc,GAAGsB,sBAAsB,CAAC,CAAC,CAAC;UAC1CrB,cAAc,GAAGqB,sBAAsB,CAAC,CAAC,CAAC;QAC5C,IAAIlB,KAAK,GAAGC,sBAAsB,CAAC;UACjCR,aAAa,EAAEG,cAAc,CAACE,IAAI,CAAC,GAAG;QACxC,CAAC,CAAC;QACF1C,KAAK,CAACJ,IAAI,CAAA+C,KAAA,CAAV3C,KAAK,GAAM,EAAE,GAAGoC,QAAQ,GAAGQ,KAAK,EAAAmB,MAAA,CAAAxC,kBAAA,CAAKkB,cAAc,GAAC;QACpD;MACF;IAGF,KAAK,sBAAsB;MACzB;QACE,IAAIuB,MAAM,GAAGnB,sBAAsB,CAAC;UAClCoB,KAAK,EAAE5E,KAAK;UACZ6E,OAAO,EAAE;QACX,CAAC,CAAC;QACFlE,KAAK,CAACJ,IAAI,CAACwC,QAAQ,GAAG,6BAA6B,GAAG4B,MAAM,EAAE5B,QAAQ,GAAG,oBAAoB,GAAG4B,MAAM,EAAE5B,QAAQ,GAAG,wBAAwB,GAAG4B,MAAM,EAAE5B,QAAQ,GAAG,eAAe,GAAG4B,MAAM,CAAC;QAC1L;MACF;IAIF,KAAK,eAAe;MAClB;QACE,IAAIG,UAAU,GAAG9E,KAAK;QACtB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;UAC5C8E,UAAU,GAAG,gBAAgB;UAC7B,IAAI9E,KAAK,KAAK,UAAU,EAAE;YACxB,IAAI+E,OAAO,GAAGvB,sBAAsB,CAAC;cACnCwB,aAAa,EAAE;YACjB,CAAC,CAAC;YACFrE,KAAK,CAACJ,IAAI,CAACwC,QAAQ,GAAG,IAAI,GAAGgC,OAAO,CAAC;UACvC;QACF,CAAC,MAAM,IAAI/E,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;UACnD8E,UAAU,GAAG,gBAAgB;UAC7B,IAAI9E,KAAK,KAAK,UAAU,EAAE;YACxB,IAAIiF,OAAO,GAAGzB,sBAAsB,CAAC;cACnCwB,aAAa,EAAE;YACjB,CAAC,CAAC;YACFrE,KAAK,CAACJ,IAAI,CAACwC,QAAQ,GAAG,IAAI,GAAGkC,OAAO,CAAC;UACvC;QACF;QACA,IAAIC,OAAO,GAAG1B,sBAAsB,CAAC;UACnCwB,aAAa,EAAEF;QACjB,CAAC,CAAC;QACFnE,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGwC,QAAQ,GAAGmC,OAAO,CAAC;QACnC;MACF;IAIF,KAAK,gBAAgB;MACnB;QACE,IAAIlF,KAAK,KAAK,MAAM,EAAE;UACpBW,KAAK,CAACJ,IAAI,CAACwC,QAAQ,GAAG,mCAAmC,CAAC;QAC5D;QACA,IAAIoC,OAAO,GAAG3B,sBAAsB,CAAC;UACnC4B,cAAc,EAAEpF;QAClB,CAAC,CAAC;QACFW,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGwC,QAAQ,GAAGoC,OAAO,CAAC;QACnC;MACF;IACF;MACE;QACE,IAAIE,OAAO,GAAG7B,sBAAsB,CAAAnE,eAAA,KACjCkF,QAAQ,EAAGvE,KAAK,CAClB,CAAC;QACFW,KAAK,CAACJ,IAAI,CAAC,EAAE,GAAGwC,QAAQ,GAAGsC,OAAO,CAAC;QACnC;MACF;EACJ;EACA,OAAO1E,KAAK;AACd;AAKA,SAAS6C,sBAAsBA,CAAC/D,KAAK,EAAE;EACrC,IAAI6F,QAAQ,GAAGvK,YAAY,CAACJ,mBAAmB,CAAC8E,KAAK,CAAC,CAAC;EACvD,IAAI8F,kBAAkB,GAAGxE,MAAM,CAACC,IAAI,CAACsE,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAAjB,QAAQ,EAAI;IAC7D,IAAIvE,KAAK,GAAGsF,QAAQ,CAACf,QAAQ,CAAC;IAC9B,IAAIxE,IAAI,GAAGlF,kBAAkB,CAAC0J,QAAQ,CAAC;IAKvC,IAAI5C,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAACwF,GAAG,CAAC,UAAAhF,CAAC;QAAA,OAAIT,IAAI,GAAG,GAAG,GAAGS,CAAC;MAAA,EAAC,CAAC6C,IAAI,CAAC,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAOtD,IAAI,GAAG,GAAG,GAAGC,KAAK;IAC3B;EACF,CAAC,CAAC,CAGDiB,IAAI,CAAC,CAAC,CAACoC,IAAI,CAAC,GAAG,CAAC;EACjB,OAAO,GAAG,GAAGkC,kBAAkB,GAAG,IAAI;AACxC;AAKA,SAAS9E,gBAAgBA,CAACgF,MAAM,EAAE/C,IAAI,EAAEgD,GAAG,EAAE;EAC3C,IAAIC,YAAY,GAAG/K,IAAI,CAAC8H,IAAI,GAAGgD,GAAG,CAAC;EACnC,OAAOE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,MAAM,GAAG,GAAG,GAAG/C,IAAI,GAAG,GAAG,GAAGiD,YAAY,GAAGF,MAAM,GAAG,GAAG,GAAGE,YAAY;AACvH;AAKA,SAASI,eAAeA,CAACC,SAAS,EAAE;EAClC,IAAIC,QAAQ,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;EAC/B,IAAI3F,UAAU,GAAGG,gBAAgB,CAAC,GAAG,EAAE,WAAW,EAAEoC,IAAI,CAACC,SAAS,CAACkD,SAAS,CAAC,CAAC;EAC9E,IAAIE,KAAK,GAAG,GAAG,GAAGnF,MAAM,CAACC,IAAI,CAACgF,SAAS,CAAC,CAACR,GAAG,CAAC,UAAAW,QAAQ,EAAI;IACvD,IAAIC,IAAI,GAAGJ,SAAS,CAACG,QAAQ,CAAC;IAC9B,IAAI5C,KAAK,GAAGC,sBAAsB,CAAC4C,IAAI,CAAC;IACxC,OAAO,EAAE,GAAGD,QAAQ,GAAG5C,KAAK;EAC9B,CAAC,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;EACjB,IAAI1C,KAAK,GAAGsF,QAAQ,CAACT,GAAG,CAAC,UAAAC,MAAM,EAAI;IACjC,OAAO,GAAG,GAAGA,MAAM,GAAG,YAAY,GAAGnF,UAAU,GAAG4F,KAAK;EACzD,CAAC,CAAC;EACF,OAAO,CAAC5F,UAAU,EAAEK,KAAK,CAAC;AAC5B;AAKA,SAASuC,qBAAqBA,CAACmD,cAAc,EAAE;EAC7C,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIC,KAAK,CAAC,8BAA8B,GAAG,OAAOD,cAAc,CAAC;EACzE;EACA,IAAIlD,cAAc,GAAG,EAAE;EACvB,IAAIxC,KAAK,GAAG,EAAE;EACd,IAAIX,KAAK,GAAG2B,KAAK,CAACC,OAAO,CAACyE,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC;EAC7ErG,KAAK,CAACkB,OAAO,CAAC,UAAA8E,SAAS,EAAI;IACzB,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MAEjC7C,cAAc,CAAC5C,IAAI,CAACyF,SAAS,CAAC;IAChC,CAAC,MAAM;MAEL,IAAIO,gBAAgB,GAAGR,eAAe,CAACC,SAAS,CAAC;QAC/C1F,UAAU,GAAGiG,gBAAgB,CAAC,CAAC,CAAC;QAChCnD,cAAc,GAAGmD,gBAAgB,CAAC,CAAC,CAAC;MACtCpD,cAAc,CAAC5C,IAAI,CAACD,UAAU,CAAC;MAC/BK,KAAK,CAACJ,IAAI,CAAA+C,KAAA,CAAV3C,KAAK,EAAAuB,kBAAA,CAASkB,cAAc,EAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO,CAACD,cAAc,EAAExC,KAAK,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}