{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { Asset } from 'expo-asset';\nimport { CodedError } from 'expo-modules-core';\nimport ExpoFontLoader from \"./ExpoFontLoader\";\nimport { FontDisplay } from \"./Font.types\";\nfunction uriFromFontSource(asset) {\n  if (typeof asset === 'string') {\n    return asset || null;\n  } else if (typeof asset === 'object') {\n    return asset.uri || asset.localUri || asset.default || null;\n  } else if (typeof asset === 'number') {\n    return uriFromFontSource(Asset.fromModule(asset));\n  }\n  return null;\n}\nfunction displayFromFontSource(asset) {\n  return asset.display || FontDisplay.AUTO;\n}\nexport function fontFamilyNeedsScoping(name) {\n  return false;\n}\nexport function getAssetForSource(source) {\n  var uri = uriFromFontSource(source);\n  var display = displayFromFontSource(source);\n  if (!uri || typeof uri !== 'string') {\n    throwInvalidSourceError(uri);\n  }\n  return {\n    uri: uri,\n    display: display\n  };\n}\nfunction throwInvalidSourceError(source) {\n  var type = typeof source;\n  if (type === 'object') type = JSON.stringify(source, null, 2);\n  throw new CodedError(`ERR_FONT_SOURCE`, `Expected font asset of type \\`string | FontResource | Asset\\` instead got: ${type}`);\n}\nexport function loadSingleFontAsync(_x, _x2) {\n  return _loadSingleFontAsync.apply(this, arguments);\n}\nfunction _loadSingleFontAsync() {\n  _loadSingleFontAsync = _asyncToGenerator(function* (name, input) {\n    if (typeof input !== 'object' || typeof input.uri !== 'string' || input.downloadAsync) {\n      throwInvalidSourceError(input);\n    }\n    yield ExpoFontLoader.loadAsync(name, input);\n  });\n  return _loadSingleFontAsync.apply(this, arguments);\n}\nexport function getNativeFontName(name) {\n  return name;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "CodedError", "ExpoFontLoader", "FontDisplay", "uriFromFontSource", "asset", "uri", "localUri", "default", "fromModule", "displayFromFontSource", "display", "AUTO", "fontFamilyNeedsScoping", "name", "getAssetForSource", "source", "throwInvalidSourceError", "type", "JSON", "stringify", "loadSingleFontAsync", "_x", "_x2", "_loadSingleFontAsync", "apply", "arguments", "_asyncToGenerator", "input", "downloadAsync", "loadAsync", "getNativeFontName"], "sources": ["/home/<USER>/Dev/clean365/mobile-app/node_modules/expo-font/src/FontLoader.web.ts"], "sourcesContent": ["import { Asset } from 'expo-asset';\nimport { CodedError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontResource, FontSource, FontDisplay } from './Font.types';\n\nfunction uriFromFontSource(asset: any): string | null {\n  if (typeof asset === 'string') {\n    return asset || null;\n  } else if (typeof asset === 'object') {\n    return asset.uri || asset.localUri || asset.default || null;\n  } else if (typeof asset === 'number') {\n    return uriFromFontSource(Asset.fromModule(asset));\n  }\n  return null;\n}\n\nfunction displayFromFontSource(asset: any): FontDisplay | undefined {\n  return asset.display || FontDisplay.AUTO;\n}\n\nexport function fontFamilyNeedsScoping(name: string): boolean {\n  return false;\n}\n\nexport function getAssetForSource(source: FontSource): Asset | FontResource {\n  const uri = uriFromFontSource(source);\n  const display = displayFromFontSource(source);\n\n  if (!uri || typeof uri !== 'string') {\n    throwInvalidSourceError(uri);\n  }\n\n  return {\n    uri: uri!,\n    display,\n  };\n}\n\nfunction throwInvalidSourceError(source: any): never {\n  let type: string = typeof source;\n  if (type === 'object') type = JSON.stringify(source, null, 2);\n  throw new CodedError(\n    `ERR_FONT_SOURCE`,\n    `Expected font asset of type \\`string | FontResource | Asset\\` instead got: ${type}`\n  );\n}\n\nexport async function loadSingleFontAsync(\n  name: string,\n  input: Asset | FontResource\n): Promise<void> {\n  if (typeof input !== 'object' || typeof input.uri !== 'string' || (input as any).downloadAsync) {\n    throwInvalidSourceError(input);\n  }\n\n  await ExpoFontLoader.loadAsync(name, input);\n}\n\nexport function getNativeFontName(name: string): string {\n  return name;\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,OAAOC,cAAc;AACrB,SAAmCC,WAAW;AAE9C,SAASC,iBAAiBA,CAACC,KAAU;EACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK,IAAI,IAAI;GACrB,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAOA,KAAK,CAACC,GAAG,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,OAAO,IAAI,IAAI;GAC5D,MAAM,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAOD,iBAAiB,CAACJ,KAAK,CAACS,UAAU,CAACJ,KAAK,CAAC,CAAC;;EAEnD,OAAO,IAAI;AACb;AAEA,SAASK,qBAAqBA,CAACL,KAAU;EACvC,OAAOA,KAAK,CAACM,OAAO,IAAIR,WAAW,CAACS,IAAI;AAC1C;AAEA,OAAM,SAAUC,sBAAsBA,CAACC,IAAY;EACjD,OAAO,KAAK;AACd;AAEA,OAAM,SAAUC,iBAAiBA,CAACC,MAAkB;EAClD,IAAMV,GAAG,GAAGF,iBAAiB,CAACY,MAAM,CAAC;EACrC,IAAML,OAAO,GAAGD,qBAAqB,CAACM,MAAM,CAAC;EAE7C,IAAI,CAACV,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACnCW,uBAAuB,CAACX,GAAG,CAAC;;EAG9B,OAAO;IACLA,GAAG,EAAEA,GAAI;IACTK,OAAO,EAAPA;GACD;AACH;AAEA,SAASM,uBAAuBA,CAACD,MAAW;EAC1C,IAAIE,IAAI,GAAW,OAAOF,MAAM;EAChC,IAAIE,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACJ,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;EAC7D,MAAM,IAAIf,UAAU,CAClB,iBAAiB,EACjB,8EAA8EiB,IAAI,EAAE,CACrF;AACH;AAEA,gBAAsBG,mBAAmBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AASxC,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CATM,WACLb,IAAY,EACZc,KAA2B;IAE3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACtB,GAAG,KAAK,QAAQ,IAAKsB,KAAa,CAACC,aAAa,EAAE;MAC9FZ,uBAAuB,CAACW,KAAK,CAAC;;IAGhC,MAAM1B,cAAc,CAAC4B,SAAS,CAAChB,IAAI,EAAEc,KAAK,CAAC;EAC7C,CAAC;EAAA,OAAAJ,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,OAAM,SAAUK,iBAAiBA,CAACjB,IAAY;EAC5C,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}