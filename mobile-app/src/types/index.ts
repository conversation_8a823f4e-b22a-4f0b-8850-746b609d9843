export interface User {
  id: string;
  email: string;
  phone: string;
  name: string;
  role: '<PERSON><PERSON><PERSON><PERSON><PERSON>' | 'ADMIN' | 'DRIVER' | 'SUPERVISOR';
  isActive: boolean;
  address?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Pickup {
  id: string;
  userId: string;
  location: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  wasteType: 'GENERAL' | 'RECYCLABLE' | 'ORGANIC' | 'HAZARDOUS' | 'ELECTRONIC';
  status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  scheduledAt?: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
  assignments?: Assignment[];
  images?: PickupImage[];
}

export interface Bin {
  id: string;
  name: string;
  location: string;
  latitude: number;
  longitude: number;
  capacity: number;
  currentLevel: number;
  status: 'NORMAL' | 'FULL' | 'OVERFLOW' | 'DAMAGED' | 'MAINTENANCE';
  lastEmptied?: string;
  sensorId?: string;
  batteryLevel?: number;
  temperature?: number;
  humidity?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Report {
  id: string;
  userId: string;
  binId?: string;
  type: 'ILLEGAL_DUMPING' | 'DAMAGED_BIN' | 'MISSED_PICKUP' | 'OVERFLOW' | 'OTHER';
  title: string;
  description: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  status: 'PENDING' | 'INVESTIGATING' | 'RESOLVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  images?: ReportImage[];
}

export interface Assignment {
  id: string;
  pickupId: string;
  userId: string;
  status: 'ASSIGNED' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user: Pick<User, 'id' | 'name' | 'phone' | 'email'>;
}

export interface PickupImage {
  id: string;
  pickupId: string;
  url: string;
  caption?: string;
  createdAt: string;
}

export interface ReportImage {
  id: string;
  reportId: string;
  url: string;
  caption?: string;
  createdAt: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: User['role'];
  phone: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  phone: string;
  name: string;
  password: string;
  address?: string;
}

export interface AuthResponse {
  access_token: string;
  user: AuthUser;
}

export interface CreatePickupData {
  location: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  wasteType?: Pickup['wasteType'];
  priority?: Pickup['priority'];
  scheduledAt?: string;
}

export interface CreateReportData {
  binId?: string;
  type: Report['type'];
  title: string;
  description: string;
  location?: string;
  latitude?: number;
  longitude?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface Location {
  latitude: number;
  longitude: number;
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}
