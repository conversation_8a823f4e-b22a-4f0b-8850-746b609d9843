import api from './api';
import { Report, CreateReportData, PaginatedResponse } from '../types';

export const reportService = {
  async createReport(data: CreateReportData): Promise<Report> {
    const response = await api.post('/reports', data);
    return response.data;
  },

  async getMyReports(page = 1, limit = 10): Promise<PaginatedResponse<Report>> {
    const response = await api.get('/reports/my-reports', {
      params: { page, limit },
    });
    return {
      data: response.data.reports,
      pagination: response.data.pagination,
    };
  },

  async getReportById(id: string): Promise<Report> {
    const response = await api.get(`/reports/${id}`);
    return response.data;
  },
};
