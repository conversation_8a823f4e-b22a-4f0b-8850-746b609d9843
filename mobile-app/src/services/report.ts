import api, { isNetworkError } from './api';
import { Report, CreateReportData, PaginatedResponse } from '../types';
import { mockReports, mockApiCall } from './mockData';

export const reportService = {
  async createReport(data: CreateReportData): Promise<Report> {
    try {
      const response = await api.post('/reports', data);
      return response.data;
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Simulating report creation in offline mode');
        // Create a mock report with the submitted data
        const mockReport: Report = {
          id: Date.now().toString(),
          type: data.type,
          status: 'PENDING',
          title: data.title,
          description: data.description,
          location: data.location,
          priority: data.priority || 'MEDIUM',
          createdAt: new Date().toISOString(),
          images: data.images || [],
        };
        return await mockApiCall(mockReport);
      }
      throw error;
    }
  },

  async getMyReports(page = 1, limit = 10): Promise<PaginatedResponse<Report>> {
    try {
      const response = await api.get('/reports/my-reports', {
        params: { page, limit },
      });
      return {
        data: response.data.reports,
        pagination: response.data.pagination,
      };
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Using mock report data due to network error');
        return await mockApiCall({
          data: mockReports,
          pagination: {
            page: 1,
            limit: 10,
            total: mockReports.length,
            totalPages: 1,
          },
        });
      }
      throw error;
    }
  },

  async getReportById(id: string): Promise<Report> {
    try {
      const response = await api.get(`/reports/${id}`);
      return response.data;
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Using mock report data due to network error');
        const mockReport = mockReports.find(r => r.id === id);
        if (mockReport) {
          return await mockApiCall(mockReport);
        }
        throw new Error('Report not found');
      }
      throw error;
    }
  },
};
