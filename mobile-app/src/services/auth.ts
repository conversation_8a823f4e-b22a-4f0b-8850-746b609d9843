import AsyncStorage from '@react-native-async-storage/async-storage';
import api from './api';
import { LoginCredentials, RegisterData, AuthResponse, AuthUser } from '../types';

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  async getProfile(): Promise<AuthUser> {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  async refreshToken(): Promise<{ access_token: string }> {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  async logout() {
    await AsyncStorage.multiRemove(['auth_token', 'auth_user']);
  },

  async setToken(token: string) {
    await AsyncStorage.setItem('auth_token', token);
  },

  async getToken(): Promise<string | null> {
    return await AsyncStorage.getItem('auth_token');
  },

  async setUser(user: AuthUser) {
    await AsyncStorage.setItem('auth_user', JSON.stringify(user));
  },

  async getUser(): Promise<AuthUser | null> {
    const user = await AsyncStorage.getItem('auth_user');
    return user ? JSON.parse(user) : null;
  },

  async isAuthenticated(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  },
};
