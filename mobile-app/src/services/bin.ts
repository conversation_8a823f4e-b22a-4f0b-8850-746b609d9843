import api, { isNetworkError } from './api';
import { Bin } from '../types';
import { mockBins, mockApiCall } from './mockData';

export const binService = {
  async getNearbyBins(latitude: number, longitude: number, radius = 5): Promise<Bin[]> {
    try {
      const response = await api.get('/bins/nearby', {
        params: { latitude, longitude, radius },
      });
      return response.data;
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Using mock bin data due to network error');
        return await mockApiCall(mockBins);
      }
      throw error;
    }
  },

  async getBinById(id: string): Promise<Bin> {
    try {
      const response = await api.get(`/bins/${id}`);
      return response.data;
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Using mock bin data due to network error');
        const mockBin = mockBins.find(b => b.id === id);
        if (mockBin) {
          return await mockApiCall(mockBin);
        }
        throw new Error('Bin not found');
      }
      throw error;
    }
  },

  async getAllBins(): Promise<Bin[]> {
    try {
      const response = await api.get('/bins');
      return response.data.bins;
    } catch (error) {
      if (isNetworkError(error)) {
        console.log('Using mock bin data due to network error');
        return await mockApiCall(mockBins);
      }
      throw error;
    }
  },
};
