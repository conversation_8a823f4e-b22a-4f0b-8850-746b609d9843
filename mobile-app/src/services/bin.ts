import api from './api';
import { Bin } from '../types';

export const binService = {
  async getNearbyBins(latitude: number, longitude: number, radius = 5): Promise<Bin[]> {
    const response = await api.get('/bins/nearby', {
      params: { latitude, longitude, radius },
    });
    return response.data;
  },

  async getBinById(id: string): Promise<Bin> {
    const response = await api.get(`/bins/${id}`);
    return response.data;
  },

  async getAllBins(): Promise<Bin[]> {
    const response = await api.get('/bins');
    return response.data.bins;
  },
};
