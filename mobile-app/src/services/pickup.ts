import api from './api';
import { Pickup, CreatePickupData, PaginatedResponse } from '../types';

export const pickupService = {
  async createPickup(data: CreatePickupData): Promise<Pickup> {
    const response = await api.post('/pickups', data);
    return response.data;
  },

  async getMyPickups(page = 1, limit = 10): Promise<PaginatedResponse<Pickup>> {
    const response = await api.get('/pickups/my-pickups', {
      params: { page, limit },
    });
    return {
      data: response.data.pickups,
      pagination: response.data.pagination,
    };
  },

  async getPickupById(id: string): Promise<Pickup> {
    const response = await api.get(`/pickups/${id}`);
    return response.data;
  },

  async cancelPickup(id: string): Promise<Pickup> {
    const response = await api.patch(`/pickups/${id}`, {
      status: 'CANCELLED',
    });
    return response.data;
  },
};
