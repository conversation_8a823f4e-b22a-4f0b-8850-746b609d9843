import api, { isNetworkError } from './api';
import { Pickup, CreatePickupData, PaginatedResponse } from '../types';
import { mockPickups, mockApiCall } from './mockData';

export const pickupService = {
  async createPickup(data: CreatePickupData): Promise<Pickup> {
    const response = await api.post('/pickups', data);
    return response.data;
  },

  async getMyPickups(page = 1, limit = 10): Promise<PaginatedResponse<Pickup>> {
    try {
      const response = await api.get('/pickups/my-pickups', {
        params: { page, limit },
      });
      return {
        data: response.data.pickups,
        pagination: response.data.pagination,
      };
    } catch (error) {
      if (isNetworkError(error)) {
        // Return mock data when network is unavailable
        console.log('Using mock pickup data due to network error');
        return await mockApiCall({
          data: mockPickups,
          pagination: {
            page: 1,
            limit: 10,
            total: mockPickups.length,
            totalPages: 1,
          },
        });
      }
      throw error;
    }
  },

  async getPickupById(id: string): Promise<Pickup> {
    try {
      const response = await api.get(`/pickups/${id}`);
      return response.data;
    } catch (error) {
      if (isNetworkError(error)) {
        // Return mock data when network is unavailable
        console.log('Using mock pickup data due to network error');
        const mockPickup = mockPickups.find(p => p.id === id);
        if (mockPickup) {
          return await mockApiCall(mockPickup);
        }
        throw new Error('Pickup not found');
      }
      throw error;
    }
  },

  async cancelPickup(id: string): Promise<Pickup> {
    const response = await api.patch(`/pickups/${id}`, {
      status: 'CANCELLED',
    });
    return response.data;
  },
};
