// Mock data for offline/demo mode
export const mockPickups = [
  {
    id: '1',
    type: 'GENERAL',
    status: 'PENDING',
    scheduledDate: '2024-01-20',
    scheduledTime: '10:00',
    address: '123 Main Street, Douala',
    description: 'General household waste pickup',
    estimatedWeight: 15,
    cost: 2500,
    createdAt: '2024-01-18T10:00:00Z',
    driver: null,
  },
  {
    id: '2',
    type: 'RECYCLABLE',
    status: 'IN_PROGRESS',
    scheduledDate: '2024-01-19',
    scheduledTime: '14:30',
    address: '456 Oak Avenue, Yaounde',
    description: 'Plastic bottles and cardboard',
    estimatedWeight: 8,
    cost: 1500,
    createdAt: '2024-01-17T14:00:00Z',
    driver: {
      id: '1',
      name: '<PERSON>',
      phone: '+237123456789',
      vehicle: 'Toyota Hilux - CM 123 AB',
    },
  },
  {
    id: '3',
    type: 'ORGANIC',
    status: 'COMPLETED',
    scheduledDate: '2024-01-18',
    scheduledTime: '09:00',
    address: '789 Pine Road, Bamenda',
    description: 'Garden waste and food scraps',
    estimatedWeight: 20,
    cost: 3000,
    createdAt: '2024-01-16T09:00:00Z',
    driver: {
      id: '2',
      name: 'Marie Fotso',
      phone: '+237987654321',
      vehicle: 'Isuzu NPR - CM 456 CD',
    },
    completedAt: '2024-01-18T09:45:00Z',
  },
  {
    id: '4',
    type: 'ELECTRONIC',
    status: 'ASSIGNED',
    scheduledDate: '2024-01-21',
    scheduledTime: '11:00',
    address: '321 Cedar Street, Garoua',
    description: 'Old computer and TV',
    estimatedWeight: 25,
    cost: 4000,
    createdAt: '2024-01-19T11:00:00Z',
    driver: {
      id: '3',
      name: 'Paul Nkomo',
      phone: '+237555666777',
      vehicle: 'Mercedes Sprinter - CM 789 EF',
    },
  },
];

export const mockReports = [
  {
    id: '1',
    type: 'OVERFLOWING_BIN',
    status: 'PENDING',
    title: 'Overflowing waste bin at Market Square',
    description: 'The main waste bin at the central market is completely full and waste is spilling onto the street.',
    location: {
      latitude: 4.0511,
      longitude: 9.7679,
      address: 'Central Market, Douala',
    },
    priority: 'HIGH',
    createdAt: '2024-01-19T08:30:00Z',
    images: [],
  },
  {
    id: '2',
    type: 'ILLEGAL_DUMPING',
    status: 'IN_PROGRESS',
    title: 'Illegal waste dumping near river',
    description: 'Large amount of construction waste dumped illegally near the Wouri River.',
    location: {
      latitude: 4.0611,
      longitude: 9.7779,
      address: 'Riverside Road, Douala',
    },
    priority: 'URGENT',
    createdAt: '2024-01-18T15:20:00Z',
    images: [],
    assignedTo: {
      id: '1',
      name: 'Environmental Team Alpha',
    },
  },
  {
    id: '3',
    type: 'DAMAGED_BIN',
    status: 'RESOLVED',
    title: 'Damaged waste bin needs replacement',
    description: 'Waste bin has been vandalized and is no longer functional.',
    location: {
      latitude: 4.0411,
      longitude: 9.7579,
      address: 'School Street, Douala',
    },
    priority: 'MEDIUM',
    createdAt: '2024-01-17T12:00:00Z',
    resolvedAt: '2024-01-18T16:30:00Z',
    images: [],
  },
];

export const mockBins = [
  {
    id: '1',
    type: 'GENERAL',
    location: {
      latitude: 4.0511,
      longitude: 9.7679,
      address: 'Central Market, Douala',
    },
    capacity: 100,
    currentLevel: 85,
    status: 'ACTIVE',
    lastEmptied: '2024-01-18T06:00:00Z',
    nextCollection: '2024-01-20T06:00:00Z',
  },
  {
    id: '2',
    type: 'RECYCLABLE',
    location: {
      latitude: 4.0611,
      longitude: 9.7779,
      address: 'University Campus, Douala',
    },
    capacity: 80,
    currentLevel: 45,
    status: 'ACTIVE',
    lastEmptied: '2024-01-19T07:00:00Z',
    nextCollection: '2024-01-21T07:00:00Z',
  },
  {
    id: '3',
    type: 'ORGANIC',
    location: {
      latitude: 4.0411,
      longitude: 9.7579,
      address: 'Residential Area, Douala',
    },
    capacity: 60,
    currentLevel: 20,
    status: 'ACTIVE',
    lastEmptied: '2024-01-19T08:00:00Z',
    nextCollection: '2024-01-22T08:00:00Z',
  },
  {
    id: '4',
    type: 'GENERAL',
    location: {
      latitude: 4.0311,
      longitude: 9.7479,
      address: 'Shopping Center, Douala',
    },
    capacity: 120,
    currentLevel: 95,
    status: 'NEEDS_ATTENTION',
    lastEmptied: '2024-01-17T06:00:00Z',
    nextCollection: '2024-01-20T06:00:00Z',
  },
];

export const mockUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+237123456789',
  address: '123 Main Street, Douala',
  avatar: null,
  createdAt: '2024-01-01T00:00:00Z',
};

export const mockStats = {
  totalPickups: 12,
  pendingPickups: 3,
  completedPickups: 8,
  totalReports: 5,
  resolvedReports: 3,
  nearbyBins: 15,
  wasteCollected: 245, // kg
};

// Helper functions to simulate API delays
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockApiCall = async <T>(data: T, delayMs: number = 1000): Promise<T> => {
  await delay(delayMs);
  return data;
};
