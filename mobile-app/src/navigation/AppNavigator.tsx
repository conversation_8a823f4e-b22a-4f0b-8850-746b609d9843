import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { HomeScreen, PickupScreen, ReportScreen, ProfileScreen } from '../screens';

const Tab = createBottomTabNavigator();

export default function AppNavigator() {
  return (
    <Tab.Navigator>
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Pickup" component={PickupScreen} />
      <Tab.Screen name="Report" component={ReportScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}