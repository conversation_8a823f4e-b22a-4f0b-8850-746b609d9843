import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const AboutScreen: React.FC = () => {
  const navigation = useNavigation();

  const appInfo = {
    version: '1.0.0',
    buildNumber: '100',
    releaseDate: 'January 2024',
  };

  const teamMembers = [
    { name: '<PERSON>', role: 'CEO & Founder' },
    { name: '<PERSON>', role: 'CTO' },
    { name: '<PERSON>', role: 'Head of Operations' },
    { name: '<PERSON>', role: 'Lead Developer' },
  ];

  const legalLinks = [
    {
      title: 'Privacy Policy',
      action: () => Linking.openURL('https://clean365.com/privacy'),
    },
    {
      title: 'Terms of Service',
      action: () => Linking.openURL('https://clean365.com/terms'),
    },
    {
      title: 'Cookie Policy',
      action: () => Linking.openURL('https://clean365.com/cookies'),
    },
    {
      title: 'Data Protection',
      action: () => Linking.openURL('https://clean365.com/data-protection'),
    },
  ];

  const socialLinks = [
    {
      name: 'Website',
      icon: 'globe' as const,
      url: 'https://clean365.com',
    },
    {
      name: 'Facebook',
      icon: 'logo-facebook' as const,
      url: 'https://facebook.com/clean365',
    },
    {
      name: 'Twitter',
      icon: 'logo-twitter' as const,
      url: 'https://twitter.com/clean365',
    },
    {
      name: 'Instagram',
      icon: 'logo-instagram' as const,
      url: 'https://instagram.com/clean365',
    },
    {
      name: 'LinkedIn',
      icon: 'logo-linkedin' as const,
      url: 'https://linkedin.com/company/clean365',
    },
  ];

  const handleSocialLink = (url: string) => {
    Linking.openURL(url).catch(() => {
      Alert.alert('Error', 'Unable to open link');
    });
  };

  const handleRateApp = () => {
    Alert.alert(
      'Rate Clean365',
      'Would you like to rate our app on the App Store?',
      [
        { text: 'Later', style: 'cancel' },
        { text: 'Rate Now', onPress: () => console.log('Rate app') }
      ]
    );
  };

  const handleShareApp = () => {
    Alert.alert(
      'Share Clean365',
      'Share this app with your friends and family!',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Share app') }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="#0ea5e9" />
            </TouchableOpacity>
            <Text style={styles.title}>About</Text>
            <View style={styles.placeholder} />
          </View>

          {/* App Logo & Info */}
          <View style={styles.appSection}>
            <View style={styles.appLogo}>
              <Ionicons name="leaf" size={48} color="#0ea5e9" />
            </View>
            <Text style={styles.appName}>Clean365</Text>
            <Text style={styles.appTagline}>Making waste management simple and sustainable</Text>
            <View style={styles.versionInfo}>
              <Text style={styles.versionText}>Version {appInfo.version}</Text>
              <Text style={styles.buildText}>Build {appInfo.buildNumber}</Text>
            </View>
          </View>

          {/* Mission */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Our Mission</Text>
            <View style={styles.missionCard}>
              <Text style={styles.missionText}>
                Clean365 is dedicated to revolutionizing waste management in Cameroon. 
                We connect communities with efficient waste collection services while 
                promoting environmental sustainability and cleaner cities for everyone.
              </Text>
            </View>
          </View>

          {/* Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Key Features</Text>
            <View style={styles.featuresContainer}>
              {[
                { icon: 'car', title: 'Smart Pickup Scheduling', desc: 'Schedule waste pickups at your convenience' },
                { icon: 'map', title: 'Bin Locator', desc: 'Find nearby waste disposal bins easily' },
                { icon: 'document-text', title: 'Issue Reporting', desc: 'Report waste management issues quickly' },
                { icon: 'leaf', title: 'Eco-Friendly', desc: 'Promoting sustainable waste practices' },
              ].map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <View style={styles.featureIcon}>
                    <Ionicons name={feature.icon as any} size={20} color="#0ea5e9" />
                  </View>
                  <View style={styles.featureContent}>
                    <Text style={styles.featureTitle}>{feature.title}</Text>
                    <Text style={styles.featureDesc}>{feature.desc}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Team */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Our Team</Text>
            <View style={styles.teamContainer}>
              {teamMembers.map((member, index) => (
                <View key={index} style={styles.teamMember}>
                  <View style={styles.memberAvatar}>
                    <Ionicons name="person" size={20} color="#0ea5e9" />
                  </View>
                  <View style={styles.memberInfo}>
                    <Text style={styles.memberName}>{member.name}</Text>
                    <Text style={styles.memberRole}>{member.role}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support Us</Text>
            <View style={styles.actionsContainer}>
              <TouchableOpacity style={styles.actionButton} onPress={handleRateApp}>
                <Ionicons name="star" size={20} color="#0ea5e9" />
                <Text style={styles.actionText}>Rate This App</Text>
                <Ionicons name="chevron-forward" size={16} color="#64748b" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={handleShareApp}>
                <Ionicons name="share" size={20} color="#0ea5e9" />
                <Text style={styles.actionText}>Share With Friends</Text>
                <Ionicons name="chevron-forward" size={16} color="#64748b" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Social Links */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Connect With Us</Text>
            <View style={styles.socialContainer}>
              {socialLinks.map((social, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.socialButton}
                  onPress={() => handleSocialLink(social.url)}
                >
                  <Ionicons name={social.icon} size={24} color="#0ea5e9" />
                  <Text style={styles.socialText}>{social.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Legal */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Legal</Text>
            <View style={styles.legalContainer}>
              {legalLinks.map((link, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.legalLink}
                  onPress={link.action}
                >
                  <Text style={styles.legalText}>{link.title}</Text>
                  <Ionicons name="chevron-forward" size={16} color="#64748b" />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © 2024 Clean365. All rights reserved.
            </Text>
            <Text style={styles.footerText}>
              Made with ❤️ in Cameroon
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  placeholder: {
    width: 40,
  },
  appSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  appLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 16,
  },
  versionInfo: {
    flexDirection: 'row',
    gap: 16,
  },
  versionText: {
    fontSize: 14,
    color: '#0ea5e9',
    fontWeight: '500',
  },
  buildText: {
    fontSize: 14,
    color: '#64748b',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  missionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  missionText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
  },
  featuresContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 2,
  },
  featureDesc: {
    fontSize: 14,
    color: '#64748b',
  },
  teamContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  teamMember: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 2,
  },
  memberRole: {
    fontSize: 14,
    color: '#64748b',
  },
  actionsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
    marginLeft: 12,
  },
  socialContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  socialButton: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  socialText: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 8,
  },
  legalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  legalLink: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  legalText: {
    fontSize: 16,
    color: '#1f2937',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
    gap: 8,
  },
  footerText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
});

export default AboutScreen;
