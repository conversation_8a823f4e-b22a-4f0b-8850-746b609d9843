import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from 'react-query';
import { pickupService } from '../../services/pickup';
import { Pickup } from '../../types';
import { format } from 'date-fns';

const PickupScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('ALL');

  const { data, isLoading, refetch } = useQuery(
    'my-pickups',
    () => pickupService.getMyPickups(),
    {
      onError: (error) => {
        console.log('Error fetching pickups:', error);
      },
    }
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  // Handle different API response structures
  const pickupsArray = data?.data || data || [];

  const filteredPickups = Array.isArray(pickupsArray) ? pickupsArray.filter((pickup: Pickup) => {
    if (filterStatus === 'ALL') return true;
    return pickup.status === filterStatus;
  }) : [];

  const statusFilters = [
    { value: 'ALL', label: 'All', count: pickupsArray?.length || 0 },
    { value: 'PENDING', label: 'Pending', count: Array.isArray(pickupsArray) ? pickupsArray.filter((p: Pickup) => p.status === 'PENDING').length : 0 },
    { value: 'IN_PROGRESS', label: 'Active', count: Array.isArray(pickupsArray) ? pickupsArray.filter((p: Pickup) => p.status === 'IN_PROGRESS' || p.status === 'ASSIGNED').length : 0 },
    { value: 'COMPLETED', label: 'Completed', count: Array.isArray(pickupsArray) ? pickupsArray.filter((p: Pickup) => p.status === 'COMPLETED').length : 0 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '#f59e0b';
      case 'ASSIGNED':
        return '#3b82f6';
      case 'IN_PROGRESS':
        return '#8b5cf6';
      case 'COMPLETED':
        return '#22c55e';
      case 'CANCELLED':
        return '#ef4444';
      default:
        return '#64748b';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'time-outline';
      case 'ASSIGNED':
        return 'person-outline';
      case 'IN_PROGRESS':
        return 'car-outline';
      case 'COMPLETED':
        return 'checkmark-circle-outline';
      case 'CANCELLED':
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const getWasteTypeIcon = (wasteType: string) => {
    switch (wasteType) {
      case 'RECYCLABLE':
        return 'leaf';
      case 'ORGANIC':
        return 'nutrition';
      case 'HAZARDOUS':
        return 'warning';
      case 'ELECTRONIC':
        return 'phone-portrait';
      default:
        return 'trash';
    }
  };

  const renderPickupItem = ({ item }: { item: Pickup }) => (
    <TouchableOpacity
      style={styles.pickupCard}
      onPress={() => navigation.navigate('PickupDetails', { pickupId: item.id })}
    >
      <View style={styles.pickupHeader}>
        <View style={styles.pickupInfo}>
          <View style={styles.wasteTypeContainer}>
            <Ionicons
              name={getWasteTypeIcon(item.wasteType)}
              size={20}
              color="#64748b"
            />
            <Text style={styles.wasteType}>{item.wasteType}</Text>
          </View>
          <Text style={styles.location} numberOfLines={1}>
            {item.location}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(item.status)}20` }]}>
          <Ionicons
            name={getStatusIcon(item.status)}
            size={16}
            color={getStatusColor(item.status)}
          />
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status}
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={styles.description} numberOfLines={2}>
          {item.description}
        </Text>
      )}

      <View style={styles.pickupFooter}>
        <Text style={styles.date}>
          {format(new Date(item.createdAt), 'MMM dd, yyyy')}
        </Text>
        <View style={styles.priorityContainer}>
          <View
            style={[
              styles.priorityDot,
              {
                backgroundColor:
                  item.priority === 'URGENT'
                    ? '#ef4444'
                    : item.priority === 'HIGH'
                    ? '#f59e0b'
                    : item.priority === 'NORMAL'
                    ? '#3b82f6'
                    : '#22c55e',
              },
            ]}
          />
          <Text style={styles.priority}>{item.priority}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="car-outline" size={64} color="#d1d5db" />
      <Text style={styles.emptyTitle}>No Pickups Yet</Text>
      <Text style={styles.emptySubtitle}>
        Request your first pickup to get started
      </Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => navigation.navigate('CreatePickup')}
      >
        <Ionicons name="add" size={20} color="#ffffff" />
        <Text style={styles.createButtonText}>Request Pickup</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading && !data) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading pickups...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Pickups</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreatePickup')}
        >
          <Ionicons name="add" size={24} color="#0ea5e9" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        {statusFilters.map((filter) => (
          <TouchableOpacity
            key={filter.value}
            style={[
              styles.filterTab,
              filterStatus === filter.value && styles.filterTabActive,
            ]}
            onPress={() => setFilterStatus(filter.value)}
          >
            <Text
              style={[
                styles.filterTabText,
                filterStatus === filter.value && styles.filterTabTextActive,
              ]}
            >
              {filter.label}
            </Text>
            {filter.count > 0 && (
              <View style={[
                styles.filterBadge,
                filterStatus === filter.value && styles.filterBadgeActive,
              ]}>
                <Text style={[
                  styles.filterBadgeText,
                  filterStatus === filter.value && styles.filterBadgeTextActive,
                ]}>
                  {filter.count}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredPickups}
        renderItem={renderPickupItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
    gap: 6,
  },
  filterTabActive: {
    backgroundColor: '#0ea5e9',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  filterTabTextActive: {
    color: '#ffffff',
  },
  filterBadge: {
    backgroundColor: '#e2e8f0',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeActive: {
    backgroundColor: '#ffffff',
  },
  filterBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748b',
  },
  filterBadgeTextActive: {
    color: '#0ea5e9',
  },
  listContainer: {
    padding: 20,
    flexGrow: 1,
  },
  pickupCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pickupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  pickupInfo: {
    flex: 1,
    marginRight: 12,
  },
  wasteTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  wasteType: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 6,
    textTransform: 'capitalize',
  },
  location: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  description: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 12,
    lineHeight: 20,
  },
  pickupFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontSize: 14,
    color: '#64748b',
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  priority: {
    fontSize: 12,
    color: '#64748b',
    textTransform: 'capitalize',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0ea5e9',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
});

export default PickupScreen;
