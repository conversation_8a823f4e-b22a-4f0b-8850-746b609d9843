import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useQuery } from 'react-query';
import * as Location from 'expo-location';
import { binService } from '../../services/bin';
import { Bin } from '../../types';

const MapScreen: React.FC = () => {
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [showList, setShowList] = useState(true);

  const { data: bins, isLoading } = useQuery(
    ['nearby-bins', location?.coords.latitude, location?.coords.longitude],
    () => {
      if (location) {
        return binService.getNearbyBins(
          location.coords.latitude,
          location.coords.longitude
        );
      }
      return binService.getAllBins();
    },
    {
      enabled: true,
      onError: (error) => {
        console.log('Error fetching bins:', error);
      },
    }
  );

  useEffect(() => {
    getLocationPermission();
  }, []);

  const getLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Location permission is needed to show nearby bins'
        );
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      setLocation(currentLocation);
    } catch (error) {
      console.log('Error getting location:', error);
    }
  };

  const getBinStatusColor = (status: string) => {
    switch (status) {
      case 'NORMAL':
        return '#22c55e';
      case 'FULL':
        return '#f59e0b';
      case 'OVERFLOW':
        return '#ef4444';
      case 'DAMAGED':
        return '#64748b';
      case 'MAINTENANCE':
        return '#8b5cf6';
      default:
        return '#64748b';
    }
  };

  const getBinStatusIcon = (status: string) => {
    switch (status) {
      case 'NORMAL':
        return 'checkmark-circle';
      case 'FULL':
        return 'warning';
      case 'OVERFLOW':
        return 'alert-circle';
      case 'DAMAGED':
        return 'close-circle';
      case 'MAINTENANCE':
        return 'construct';
      default:
        return 'help-circle';
    }
  };

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  };

  const renderBinItem = ({ item }: { item: Bin }) => {
    const distance = location
      ? calculateDistance(
          location.coords.latitude,
          location.coords.longitude,
          item.latitude,
          item.longitude
        )
      : null;

    return (
      <View style={styles.binCard}>
        <View style={styles.binHeader}>
          <View style={styles.binInfo}>
            <Text style={styles.binName}>{item.name}</Text>
            <Text style={styles.binLocation} numberOfLines={1}>
              {item.location}
            </Text>
            {distance && (
              <Text style={styles.distance}>
                {distance < 1
                  ? `${Math.round(distance * 1000)}m away`
                  : `${distance.toFixed(1)}km away`}
              </Text>
            )}
          </View>
          <View style={[styles.statusBadge, { backgroundColor: `${getBinStatusColor(item.status)}20` }]}>
            <Ionicons
              name={getBinStatusIcon(item.status)}
              size={16}
              color={getBinStatusColor(item.status)}
            />
            <Text style={[styles.statusText, { color: getBinStatusColor(item.status) }]}>
              {item.status}
            </Text>
          </View>
        </View>

        <View style={styles.binDetails}>
          <View style={styles.levelContainer}>
            <Text style={styles.levelLabel}>Fill Level</Text>
            <View style={styles.levelBar}>
              <View
                style={[
                  styles.levelFill,
                  {
                    width: `${item.currentLevel}%`,
                    backgroundColor: getBinStatusColor(item.status),
                  },
                ]}
              />
            </View>
            <Text style={styles.levelText}>{item.currentLevel}%</Text>
          </View>

          <View style={styles.capacityInfo}>
            <Ionicons name="cube-outline" size={16} color="#64748b" />
            <Text style={styles.capacityText}>Capacity: {item.capacity}L</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="location-outline" size={64} color="#d1d5db" />
      <Text style={styles.emptyTitle}>No Bins Found</Text>
      <Text style={styles.emptySubtitle}>
        {location
          ? 'No waste bins found in your area'
          : 'Enable location to find nearby bins'}
      </Text>
      {!location && (
        <TouchableOpacity style={styles.locationButton} onPress={getLocationPermission}>
          <Ionicons name="location" size={20} color="#ffffff" />
          <Text style={styles.locationButtonText}>Enable Location</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>Waste Bins</Text>
          <Text style={styles.subtitle}>
            {Array.isArray(bins) ? bins.length : 0} bins found {location ? 'nearby' : ''}
          </Text>
        </View>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.viewButton, showList && styles.viewButtonActive]}
            onPress={() => setShowList(true)}
          >
            <Ionicons
              name="list"
              size={20}
              color={showList ? '#ffffff' : '#64748b'}
            />
            <Text style={[styles.viewButtonText, showList && styles.viewButtonTextActive]}>
              List
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.viewButton, !showList && styles.viewButtonActive]}
            onPress={() => setShowList(false)}
          >
            <Ionicons
              name="map"
              size={20}
              color={!showList ? '#ffffff' : '#64748b'}
            />
            <Text style={[styles.viewButtonText, !showList && styles.viewButtonTextActive]}>
              Map
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Location Status Banner */}
      {!location && (
        <View style={styles.locationBanner}>
          <Ionicons name="location-outline" size={16} color="#f59e0b" />
          <Text style={styles.locationBannerText}>
            Enable location for nearby bins
          </Text>
          <TouchableOpacity onPress={getLocationPermission}>
            <Text style={styles.locationBannerAction}>Enable</Text>
          </TouchableOpacity>
        </View>
      )}

      {showList ? (
        <FlatList
          data={bins || []}
          renderItem={renderBinItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      ) : (
        <View style={styles.mapPlaceholder}>
          <Ionicons name="map-outline" size={64} color="#d1d5db" />
          <Text style={styles.mapPlaceholderText}>Interactive Map</Text>
          <Text style={styles.mapPlaceholderSubtext}>
            Map integration coming soon
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 2,
  },
  headerButtons: {
    flexDirection: 'row',
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
    padding: 4,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  viewButtonActive: {
    backgroundColor: '#0ea5e9',
  },
  viewButtonText: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },
  viewButtonTextActive: {
    color: '#ffffff',
  },
  locationBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  locationBannerText: {
    flex: 1,
    fontSize: 14,
    color: '#92400e',
  },
  locationBannerAction: {
    fontSize: 14,
    color: '#0ea5e9',
    fontWeight: '600',
  },
  listContainer: {
    padding: 20,
    flexGrow: 1,
  },
  binCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  binHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  binInfo: {
    flex: 1,
    marginRight: 12,
  },
  binName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  binLocation: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  distance: {
    fontSize: 12,
    color: '#0ea5e9',
    fontWeight: '500',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  binDetails: {
    gap: 12,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  levelLabel: {
    fontSize: 14,
    color: '#64748b',
    width: 60,
  },
  levelBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#f1f5f9',
    borderRadius: 4,
    overflow: 'hidden',
  },
  levelFill: {
    height: '100%',
    borderRadius: 4,
  },
  levelText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    width: 40,
    textAlign: 'right',
  },
  capacityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  capacityText: {
    fontSize: 14,
    color: '#64748b',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0ea5e9',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  locationButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  mapPlaceholderText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  mapPlaceholderSubtext: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
});

export default MapScreen;
