import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface NotificationSettings {
  pushNotifications: boolean;
  pickupReminders: boolean;
  pickupUpdates: boolean;
  reportUpdates: boolean;
  promotions: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

const NotificationsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [settings, setSettings] = useState<NotificationSettings>({
    pushNotifications: true,
    pickupReminders: true,
    pickupUpdates: true,
    reportUpdates: false,
    promotions: false,
    emailNotifications: true,
    smsNotifications: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleToggle = (key: keyof NotificationSettings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      Alert.alert('Success!', 'Notification settings updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  const notificationSections = [
    {
      title: 'Push Notifications',
      items: [
        {
          key: 'pushNotifications' as keyof NotificationSettings,
          title: 'Enable Push Notifications',
          subtitle: 'Receive notifications on your device',
          icon: 'notifications' as const,
        },
        {
          key: 'pickupReminders' as keyof NotificationSettings,
          title: 'Pickup Reminders',
          subtitle: 'Get reminded about upcoming pickups',
          icon: 'alarm' as const,
        },
        {
          key: 'pickupUpdates' as keyof NotificationSettings,
          title: 'Pickup Status Updates',
          subtitle: 'Updates when pickup status changes',
          icon: 'car' as const,
        },
        {
          key: 'reportUpdates' as keyof NotificationSettings,
          title: 'Report Updates',
          subtitle: 'Updates on your submitted reports',
          icon: 'document-text' as const,
        },
      ]
    },
    {
      title: 'Marketing',
      items: [
        {
          key: 'promotions' as keyof NotificationSettings,
          title: 'Promotions & Offers',
          subtitle: 'Special offers and promotions',
          icon: 'gift' as const,
        },
      ]
    },
    {
      title: 'Communication Channels',
      items: [
        {
          key: 'emailNotifications' as keyof NotificationSettings,
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          icon: 'mail' as const,
        },
        {
          key: 'smsNotifications' as keyof NotificationSettings,
          title: 'SMS Notifications',
          subtitle: 'Receive notifications via SMS',
          icon: 'chatbubble' as const,
        },
      ]
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="#0ea5e9" />
            </TouchableOpacity>
            <Text style={styles.title}>Notifications</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Description */}
          <View style={styles.description}>
            <Text style={styles.descriptionText}>
              Manage how you receive notifications and updates from Clean365
            </Text>
          </View>

          {/* Notification Sections */}
          {notificationSections.map((section, sectionIndex) => (
            <View key={sectionIndex} style={styles.section}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
              <View style={styles.sectionContent}>
                {section.items.map((item, itemIndex) => (
                  <View key={item.key} style={styles.notificationItem}>
                    <View style={styles.itemLeft}>
                      <View style={styles.iconContainer}>
                        <Ionicons name={item.icon} size={20} color="#0ea5e9" />
                      </View>
                      <View style={styles.itemContent}>
                        <Text style={styles.itemTitle}>{item.title}</Text>
                        <Text style={styles.itemSubtitle}>{item.subtitle}</Text>
                      </View>
                    </View>
                    <Switch
                      value={settings[item.key]}
                      onValueChange={() => handleToggle(item.key)}
                      trackColor={{ false: '#f1f5f9', true: '#bfdbfe' }}
                      thumbColor={settings[item.key] ? '#0ea5e9' : '#64748b'}
                    />
                  </View>
                ))}
              </View>
            </View>
          ))}

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Saving...' : 'Save Settings'}
            </Text>
          </TouchableOpacity>

          {/* Info */}
          <View style={styles.infoSection}>
            <Ionicons name="information-circle-outline" size={16} color="#64748b" />
            <Text style={styles.infoText}>
              You can change these settings anytime. Some notifications may be required for app functionality.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  placeholder: {
    width: 40,
  },
  description: {
    marginBottom: 32,
  },
  descriptionText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  sectionContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  saveButton: {
    backgroundColor: '#0ea5e9',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f8fafc',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
});

export default NotificationsScreen;
