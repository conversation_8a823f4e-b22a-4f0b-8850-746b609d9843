import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainNavigator';

type PickupDetailsRouteProp = RouteProp<MainStackParamList, 'PickupDetails'>;

interface PickupDetails {
  id: string;
  status: 'PENDING' | 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  wasteType: string;
  priority: string;
  location: string;
  description: string;
  requestedDate: string;
  scheduledDate?: string;
  completedDate?: string;
  driverName?: string;
  driverPhone?: string;
  estimatedTime?: string;
  notes?: string;
}

const PickupDetailsScreen: React.FC = () => {
  const route = useRoute<PickupDetailsRouteProp>();
  const navigation = useNavigation();
  const { pickupId } = route.params;
  const [pickup, setPickup] = useState<PickupDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch pickup details
    const fetchPickupDetails = async () => {
      setLoading(true);
      try {
        // Mock data - replace with actual API call
        const mockPickup: PickupDetails = {
          id: pickupId,
          status: 'SCHEDULED',
          wasteType: 'General Waste',
          priority: 'Normal',
          location: '123 Main Street, Downtown',
          description: 'Large household items including old furniture and appliances',
          requestedDate: '2024-01-15T10:00:00Z',
          scheduledDate: '2024-01-17T09:00:00Z',
          driverName: 'John Smith',
          driverPhone: '+****************',
          estimatedTime: '9:00 AM - 11:00 AM',
          notes: 'Please call before arrival. Items are in the garage.',
        };

        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
        setPickup(mockPickup);
      } catch (error) {
        Alert.alert('Error', 'Failed to load pickup details');
      } finally {
        setLoading(false);
      }
    };

    fetchPickupDetails();
  }, [pickupId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return '#f59e0b';
      case 'SCHEDULED': return '#0ea5e9';
      case 'IN_PROGRESS': return '#8b5cf6';
      case 'COMPLETED': return '#22c55e';
      case 'CANCELLED': return '#ef4444';
      default: return '#64748b';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return 'time-outline';
      case 'SCHEDULED': return 'calendar-outline';
      case 'IN_PROGRESS': return 'car-outline';
      case 'COMPLETED': return 'checkmark-circle-outline';
      case 'CANCELLED': return 'close-circle-outline';
      default: return 'help-outline';
    }
  };

  const handleCancelPickup = () => {
    Alert.alert(
      'Cancel Pickup',
      'Are you sure you want to cancel this pickup request?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            // Handle cancellation
            Alert.alert('Success', 'Pickup has been cancelled');
            navigation.goBack();
          }
        }
      ]
    );
  };

  const handleCallDriver = () => {
    if (pickup?.driverPhone) {
      Alert.alert('Call Driver', `Call ${pickup.driverName} at ${pickup.driverPhone}?`);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading pickup details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!pickup) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
          <Text style={styles.errorText}>Pickup not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Status Header */}
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <View style={[styles.statusIcon, { backgroundColor: `${getStatusColor(pickup.status)}20` }]}>
                <Ionicons
                  name={getStatusIcon(pickup.status) as any}
                  size={24}
                  color={getStatusColor(pickup.status)}
                />
              </View>
              <View style={styles.statusInfo}>
                <Text style={styles.statusText}>{pickup.status.replace('_', ' ')}</Text>
                <Text style={styles.pickupId}>Pickup #{pickup.id}</Text>
              </View>
            </View>
          </View>

          {/* Pickup Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pickup Information</Text>
            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <Ionicons name="trash-outline" size={20} color="#64748b" />
                <Text style={styles.infoLabel}>Waste Type</Text>
                <Text style={styles.infoValue}>{pickup.wasteType}</Text>
              </View>
              <View style={styles.infoRow}>
                <Ionicons name="flag-outline" size={20} color="#64748b" />
                <Text style={styles.infoLabel}>Priority</Text>
                <Text style={styles.infoValue}>{pickup.priority}</Text>
              </View>
              <View style={styles.infoRow}>
                <Ionicons name="location-outline" size={20} color="#64748b" />
                <Text style={styles.infoLabel}>Location</Text>
                <Text style={styles.infoValue}>{pickup.location}</Text>
              </View>
              <View style={styles.infoRow}>
                <Ionicons name="document-text-outline" size={20} color="#64748b" />
                <Text style={styles.infoLabel}>Description</Text>
                <Text style={styles.infoValue}>{pickup.description}</Text>
              </View>
            </View>
          </View>

          {/* Schedule Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Schedule</Text>
            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <Ionicons name="calendar-outline" size={20} color="#64748b" />
                <Text style={styles.infoLabel}>Requested</Text>
                <Text style={styles.infoValue}>
                  {new Date(pickup.requestedDate).toLocaleDateString()}
                </Text>
              </View>
              {pickup.scheduledDate && (
                <View style={styles.infoRow}>
                  <Ionicons name="time-outline" size={20} color="#64748b" />
                  <Text style={styles.infoLabel}>Scheduled</Text>
                  <Text style={styles.infoValue}>
                    {new Date(pickup.scheduledDate).toLocaleDateString()}
                  </Text>
                </View>
              )}
              {pickup.estimatedTime && (
                <View style={styles.infoRow}>
                  <Ionicons name="clock-outline" size={20} color="#64748b" />
                  <Text style={styles.infoLabel}>Time Window</Text>
                  <Text style={styles.infoValue}>{pickup.estimatedTime}</Text>
                </View>
              )}
            </View>
          </View>

          {/* Driver Information */}
          {pickup.driverName && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Driver Information</Text>
              <View style={styles.infoCard}>
                <View style={styles.infoRow}>
                  <Ionicons name="person-outline" size={20} color="#64748b" />
                  <Text style={styles.infoLabel}>Driver</Text>
                  <Text style={styles.infoValue}>{pickup.driverName}</Text>
                </View>
                {pickup.driverPhone && (
                  <View style={styles.infoRow}>
                    <Ionicons name="call-outline" size={20} color="#64748b" />
                    <Text style={styles.infoLabel}>Phone</Text>
                    <TouchableOpacity onPress={handleCallDriver}>
                      <Text style={[styles.infoValue, styles.phoneLink]}>
                        {pickup.driverPhone}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Notes */}
          {pickup.notes && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Notes</Text>
              <View style={styles.notesCard}>
                <Text style={styles.notesText}>{pickup.notes}</Text>
              </View>
            </View>
          )}

          {/* Action Buttons */}
          {pickup.status !== 'COMPLETED' && pickup.status !== 'CANCELLED' && (
            <View style={styles.actionsSection}>
              {pickup.driverPhone && (
                <TouchableOpacity style={styles.callButton} onPress={handleCallDriver}>
                  <Ionicons name="call" size={20} color="#ffffff" />
                  <Text style={styles.callButtonText}>Call Driver</Text>
                </TouchableOpacity>
              )}

              {pickup.status === 'PENDING' && (
                <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPickup}>
                  <Ionicons name="close" size={20} color="#ef4444" />
                  <Text style={styles.cancelButtonText}>Cancel Pickup</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 18,
    color: '#ef4444',
    fontWeight: '600',
  },
  statusCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  statusInfo: {
    flex: 1,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    textTransform: 'capitalize',
  },
  pickupId: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 2,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  infoLabel: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 12,
    width: 80,
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  phoneLink: {
    color: '#0ea5e9',
    textDecorationLine: 'underline',
  },
  notesCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notesText: {
    fontSize: 14,
    color: '#1f2937',
    lineHeight: 20,
  },
  actionsSection: {
    gap: 12,
    marginTop: 8,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0ea5e9',
    borderRadius: 12,
    padding: 16,
    gap: 8,
  },
  callButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#ef4444',
    borderRadius: 12,
    padding: 16,
    gap: 8,
  },
  cancelButtonText: {
    color: '#ef4444',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PickupDetailsScreen;
