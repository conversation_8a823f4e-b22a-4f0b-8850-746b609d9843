import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface CreatePickupFormData {
  location: string;
  description: string;
  wasteType: string;
  priority: string;
}

const wasteTypes = [
  { value: 'GENERAL', label: 'General Waste', icon: 'trash' as const },
  { value: 'RECYCLABLE', label: 'Recyclable', icon: 'leaf' as const },
  { value: 'ORGANIC', label: 'Organic', icon: 'nutrition' as const },
  { value: 'HAZARDOUS', label: 'Hazardous', icon: 'warning' as const },
  { value: 'ELECTRONIC', label: 'Electronic', icon: 'phone-portrait' as const },
];

const priorities = [
  { value: 'LOW', label: 'Low', color: '#22c55e' },
  { value: 'NORMAL', label: 'Normal', color: '#3b82f6' },
  { value: 'HIGH', label: 'High', color: '#f59e0b' },
  { value: 'URGENT', label: 'Urgent', color: '#ef4444' },
];

const CreatePickupScreen: React.FC = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<CreatePickupFormData>({
    location: '',
    description: '',
    wasteType: 'GENERAL',
    priority: 'NORMAL',
  });

  const validateForm = (): boolean => {
    if (!formData.location.trim()) {
      Alert.alert('Validation Error', 'Please enter a pickup location');
      return false;
    }
    if (formData.location.length < 10) {
      Alert.alert('Validation Error', 'Please provide a more detailed location');
      return false;
    }
    if (!formData.description.trim()) {
      Alert.alert('Validation Error', 'Please describe what needs to be picked up');
      return false;
    }
    if (formData.description.length < 10) {
      Alert.alert('Validation Error', 'Please provide more details about the waste');
      return false;
    }
    return true;
  };

  const onSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      Alert.alert(
        'Success!',
        'Your pickup request has been submitted successfully. You will receive a confirmation shortly.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit pickup request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <Ionicons name="car" size={48} color="#0ea5e9" />
            <Text style={styles.title}>Request Pickup</Text>
            <Text style={styles.subtitle}>Schedule waste collection at your location</Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Location */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Location *</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter pickup location"
                value={formData.location}
                onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
                multiline
              />
            </View>

            {/* Waste Type */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Waste Type</Text>
              <View style={styles.optionsContainer}>
                {wasteTypes.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.optionCard,
                      formData.wasteType === type.value && styles.optionCardSelected,
                    ]}
                    onPress={() => setFormData(prev => ({ ...prev, wasteType: type.value }))}
                  >
                    <Ionicons
                      name={type.icon}
                      size={24}
                      color={formData.wasteType === type.value ? '#0ea5e9' : '#64748b'}
                    />
                    <Text
                      style={[
                        styles.optionText,
                        formData.wasteType === type.value && styles.optionTextSelected,
                      ]}
                    >
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Priority */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Priority</Text>
              <View style={styles.priorityContainer}>
                {priorities.map((priority) => (
                  <TouchableOpacity
                    key={priority.value}
                    style={[
                      styles.priorityCard,
                      formData.priority === priority.value && {
                        backgroundColor: `${priority.color}20`,
                        borderColor: priority.color,
                      },
                    ]}
                    onPress={() => setFormData(prev => ({ ...prev, priority: priority.value }))}
                  >
                    <View
                      style={[
                        styles.priorityDot,
                        { backgroundColor: priority.color },
                      ]}
                    />
                    <Text
                      style={[
                        styles.priorityText,
                        formData.priority === priority.value && { color: priority.color },
                      ]}
                    >
                      {priority.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Description */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Description (Optional)</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Describe the waste or any special instructions..."
                value={formData.description}
                onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                multiline
                numberOfLines={4}
              />
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
              onPress={onSubmit}
              disabled={isLoading}
            >
              <Text style={styles.submitButtonText}>
                {isLoading ? 'Submitting...' : 'Submit Request'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  form: {
    gap: 24,
  },
  inputContainer: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#ffffff',
    minHeight: 50,
  },
  inputError: {
    borderColor: '#ef4444',
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  optionCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    gap: 8,
  },
  optionCardSelected: {
    borderColor: '#0ea5e9',
    backgroundColor: '#e0f2fe',
  },
  optionText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
  optionTextSelected: {
    color: '#0ea5e9',
    fontWeight: '600',
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  priorityText: {
    fontSize: 14,
    color: '#64748b',
  },
  submitButton: {
    backgroundColor: '#0ea5e9',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreatePickupScreen;
