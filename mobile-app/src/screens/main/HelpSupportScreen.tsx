import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface FAQItem {
  question: string;
  answer: string;
}

const HelpSupportScreen: React.FC = () => {
  const navigation = useNavigation();
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const contactOptions = [
    {
      title: 'Call Support',
      subtitle: '+237 123 456 789',
      icon: 'call' as const,
      action: () => Linking.openURL('tel:+237123456789'),
    },
    {
      title: 'Email Support',
      subtitle: '<EMAIL>',
      icon: 'mail' as const,
      action: () => Linking.openURL('mailto:<EMAIL>'),
    },
    {
      title: 'WhatsApp',
      subtitle: 'Chat with us on WhatsApp',
      icon: 'logo-whatsapp' as const,
      action: () => Linking.openURL('https://wa.me/237123456789'),
    },
    {
      title: 'Live Chat',
      subtitle: 'Chat with our support team',
      icon: 'chatbubbles' as const,
      action: () => Alert.alert('Live Chat', 'Live chat feature coming soon!'),
    },
  ];

  const faqData: FAQItem[] = [
    {
      question: 'How do I schedule a waste pickup?',
      answer: 'Go to the Pickup tab, tap the "+" button, fill in your location and waste details, then submit your request. You\'ll receive a confirmation with pickup details.',
    },
    {
      question: 'What types of waste can be collected?',
      answer: 'We collect general waste, recyclables, organic waste, electronic waste, and hazardous materials. Each type may have different pickup schedules and requirements.',
    },
    {
      question: 'How much does waste pickup cost?',
      answer: 'Pricing varies based on waste type, quantity, and location. You\'ll see the estimated cost before confirming your pickup request.',
    },
    {
      question: 'Can I cancel or reschedule a pickup?',
      answer: 'Yes, you can cancel or reschedule pickups up to 2 hours before the scheduled time. Go to your pickup details and select the appropriate option.',
    },
    {
      question: 'How do I find nearby waste bins?',
      answer: 'Use the Map tab to view all nearby waste bins. You can see their locations, capacity levels, and types of waste they accept.',
    },
    {
      question: 'How do I report waste management issues?',
      answer: 'Go to the Report tab and tap "+" to submit a new report. Describe the issue, add location details, and submit. We\'ll investigate and respond promptly.',
    },
    {
      question: 'What should I do if my pickup is missed?',
      answer: 'If your scheduled pickup is missed, please report it through the app or contact our support team. We\'ll reschedule at no additional cost.',
    },
    {
      question: 'How do I update my profile information?',
      answer: 'Go to Profile > Edit Profile to update your personal information, contact details, and address.',
    },
  ];

  const quickActions = [
    {
      title: 'Report a Problem',
      subtitle: 'Report issues with the app or service',
      icon: 'warning' as const,
      action: () => navigation.navigate('CreateReport' as never),
    },
    {
      title: 'Track My Pickup',
      subtitle: 'Check the status of your pickups',
      icon: 'location' as const,
      action: () => navigation.navigate('Pickup' as never),
    },
    {
      title: 'Find Waste Bins',
      subtitle: 'Locate nearby waste disposal bins',
      icon: 'map' as const,
      action: () => navigation.navigate('Map' as never),
    },
  ];

  const handleFAQToggle = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="#0ea5e9" />
            </TouchableOpacity>
            <Text style={styles.title}>Help & Support</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.quickActionsContainer}>
              {quickActions.map((action, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.quickActionCard}
                  onPress={action.action}
                >
                  <View style={styles.quickActionIcon}>
                    <Ionicons name={action.icon} size={24} color="#0ea5e9" />
                  </View>
                  <Text style={styles.quickActionTitle}>{action.title}</Text>
                  <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Contact Support */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Support</Text>
            <View style={styles.contactContainer}>
              {contactOptions.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.contactItem}
                  onPress={option.action}
                >
                  <View style={styles.contactIcon}>
                    <Ionicons name={option.icon} size={20} color="#0ea5e9" />
                  </View>
                  <View style={styles.contactContent}>
                    <Text style={styles.contactTitle}>{option.title}</Text>
                    <Text style={styles.contactSubtitle}>{option.subtitle}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#64748b" />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* FAQ */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
            <View style={styles.faqContainer}>
              {faqData.map((faq, index) => (
                <View key={index} style={styles.faqItem}>
                  <TouchableOpacity
                    style={styles.faqQuestion}
                    onPress={() => handleFAQToggle(index)}
                  >
                    <Text style={styles.faqQuestionText}>{faq.question}</Text>
                    <Ionicons
                      name={expandedFAQ === index ? 'chevron-up' : 'chevron-down'}
                      size={20}
                      color="#64748b"
                    />
                  </TouchableOpacity>
                  {expandedFAQ === index && (
                    <View style={styles.faqAnswer}>
                      <Text style={styles.faqAnswerText}>{faq.answer}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Support Hours */}
          <View style={styles.supportHours}>
            <Ionicons name="time-outline" size={20} color="#64748b" />
            <View style={styles.supportHoursContent}>
              <Text style={styles.supportHoursTitle}>Support Hours</Text>
              <Text style={styles.supportHoursText}>
                Monday - Friday: 8:00 AM - 6:00 PM{'\n'}
                Saturday: 9:00 AM - 4:00 PM{'\n'}
                Sunday: Closed
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  placeholder: {
    width: 40,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
  },
  contactContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0f2fe',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 2,
  },
  contactSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  faqContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  faqQuestion: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  faqQuestionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginRight: 12,
  },
  faqAnswer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  faqAnswerText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  supportHours: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  supportHoursContent: {
    flex: 1,
  },
  supportHoursTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  supportHoursText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
});

export default HelpSupportScreen;
