import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import { authService } from '../services/auth';
import { AuthUser, LoginCredentials, RegisterData } from '../types';
import Toast from 'react-native-toast-message';

interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const { isLoading } = useQuery(
    'profile',
    authService.getProfile,
    {
      enabled: isAuthenticated && !user && isInitialized,
      onSuccess: (userData) => {
        setUser(userData);
        authService.setUser(userData);
      },
      onError: () => {
        logout();
      },
      retry: false,
    }
  );

  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials);
      await authService.setToken(response.access_token);
      await authService.setUser(response.user);
      setUser(response.user);
      setIsAuthenticated(true);
      Toast.show({
        type: 'success',
        text1: 'Welcome!',
        text2: 'Login successful',
      });
    } catch (error) {
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await authService.register(data);
      await authService.setToken(response.access_token);
      await authService.setUser(response.user);
      setUser(response.user);
      setIsAuthenticated(true);
      Toast.show({
        type: 'success',
        text1: 'Welcome!',
        text2: 'Registration successful',
      });
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    Toast.show({
      type: 'success',
      text1: 'Goodbye!',
      text2: 'Logged out successfully',
    });
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = await authService.getToken();
        const savedUser = await authService.getUser();
        
        if (token && savedUser) {
          setUser(savedUser);
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        setIsAuthenticated(false);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  const value = {
    user,
    isLoading: isLoading || !isInitialized,
    isAuthenticated,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
