{"name": "cleancity360-mobile", "version": "1.0.0", "description": "React Native mobile app for Cleancity360 waste management", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "build:android": "expo build:android", "build:ios": "expo build:ios", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "@react-navigation/native-stack": "^6.9.17", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-location": "~16.1.0", "expo-permissions": "~14.2.1", "expo-notifications": "~0.20.1", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-image-picker": "~14.3.2", "expo-camera": "~13.4.4", "react-native-maps": "1.7.1", "axios": "^1.6.2", "react-query": "^3.39.3", "@react-native-async-storage/async-storage": "1.18.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-native-toast-message": "^2.1.6", "react-native-vector-icons": "^10.0.2", "date-fns": "^2.30.0", "expo-font": "~11.4.0", "@expo/vector-icons": "^13.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "private": true}