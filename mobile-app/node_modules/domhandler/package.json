{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.1", "description": "Handler for htmlparser2 that turns pages into a dom", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "files": ["lib"], "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.21", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.0", "ts-jest": "^27.1.3", "typescript": "^4.6.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}}