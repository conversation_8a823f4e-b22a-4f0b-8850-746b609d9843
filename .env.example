# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/cleancity360"

# Redis Configuration
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# MQTT Configuration
MQTT_BROKER_URL="mqtt://localhost:1883"
MQTT_USERNAME=""
MQTT_PASSWORD=""

# API Configuration
API_PORT=3001
API_HOST="localhost"

# Frontend Configuration
REACT_APP_API_URL="http://localhost:3001"
REACT_APP_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Mobile App Configuration
EXPO_PUBLIC_API_URL="http://localhost:3001"
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# IoT Configuration
IOT_API_BASE_URL="http://localhost:3001"
IOT_DEVICE_ID="cleancity360-iot-001"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Firebase Configuration (for mobile push notifications)
FIREBASE_PROJECT_ID="your-firebase-project-id"
FIREBASE_PRIVATE_KEY="your-firebase-private-key"
FIREBASE_CLIENT_EMAIL="your-firebase-client-email"

# AWS S3 Configuration (for file uploads)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="cleancity360-uploads"

# Development/Production Environment
NODE_ENV="development"

# Logging
LOG_LEVEL="debug"
